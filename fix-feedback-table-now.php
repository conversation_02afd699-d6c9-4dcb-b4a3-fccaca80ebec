<?php
/**
 * Fix Feedback Table Script
 * 
 * This script will drop and recreate the feedback table with the correct structure.
 * It can be run directly from the browser or from the command line.
 */

// Load WordPress
require_once dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';

// Get the database connection
global $wpdb;

// Define the table name
$table_name = $wpdb->prefix . 'qkb_ml_interactions';

// Drop the table if it exists
$wpdb->query("DROP TABLE IF EXISTS $table_name");

// Create the table with the correct structure
$charset_collate = $wpdb->get_charset_collate();

$sql = "CREATE TABLE $table_name (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    message_id varchar(255) NOT NULL,
    query text NOT NULL,
    response text NOT NULL,
    feedback int(11) NOT NULL,
    assistant_id bigint(20) NOT NULL,
    created_at datetime NOT NULL,
    PRIMARY KEY  (id),
    KEY assistant_id (assistant_id),
    <PERSON><PERSON><PERSON> created_at (created_at)
) $charset_collate;";

require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
$result = dbDelta($sql);

// Check if the table was created successfully
if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name) {
    echo "SUCCESS: The feedback table has been recreated successfully.";
} else {
    echo "ERROR: Failed to recreate the feedback table. Please check the error logs for more information.";
    if ($wpdb->last_error) {
        echo "\nDatabase Error: " . $wpdb->last_error;
    }
}
