/**
 * Content Gap Analysis JavaScript
 */
(function($) {
    'use strict';

    // Initialize
    $(document).ready(function() {
        // Set up event listeners
        $('.show-content-suggestion').on('click', showContentSuggestion);
        $('.create-content').on('click', showCreateContentModal);

        // Initialize modals
        initializeModals();
    });

    /**
     * Initialize modal dialogs
     */
    function initializeModals() {
        // Create modals if they don't exist
        if ($('#qkb-suggestion-modal').length === 0) {
            const suggestionModal = `
                <div id="qkb-suggestion-modal" class="qkb-modal">
                    <div class="qkb-modal-overlay"></div>
                    <div class="qkb-modal-container">
                        <div class="qkb-modal-header">
                            <h2 class="qkb-modal-title">${qkbContentGaps.suggestionTitle}</h2>
                            <button class="qkb-modal-close">&times;</button>
                        </div>
                        <div class="qkb-modal-content">
                            <div id="qkb-suggestion-content"></div>
                        </div>
                    </div>
                </div>
            `;
            $('body').append(suggestionModal);
        }

        if ($('#qkb-create-content-modal').length === 0) {
            const createContentModal = `
                <div id="qkb-create-content-modal" class="qkb-modal">
                    <div class="qkb-modal-overlay"></div>
                    <div class="qkb-modal-container">
                        <div class="qkb-modal-header">
                            <h2 class="qkb-modal-title">${qkbContentGaps.createContentTitle}</h2>
                            <button class="qkb-modal-close">&times;</button>
                        </div>
                        <div class="qkb-modal-content">
                            <div class="qkb-create-content-form">
                                <p><strong>${qkbContentGaps.topicLabel}:</strong> <span id="qkb-content-topic"></span></p>
                                <div class="qkb-content-editor-container">
                                    <textarea id="qkb-content-editor" rows="15"></textarea>
                                </div>
                                <div class="qkb-modal-actions">
                                    <button id="qkb-generate-suggestion" class="button">${qkbContentGaps.generateSuggestionBtn}</button>
                                    <button id="qkb-create-draft" class="button button-primary">${qkbContentGaps.createDraftBtn}</button>
                                    <span class="spinner"></span>
                                </div>
                                <div id="qkb-modal-message"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('body').append(createContentModal);
        }

        // Set up modal event listeners
        $('.qkb-modal-close, .qkb-modal-overlay').on('click', closeModals);
        $('#qkb-generate-suggestion').on('click', generateContentSuggestion);
        $('#qkb-create-draft').on('click', createContentDraft);
    }

    /**
     * Show content suggestion modal
     */
    function showContentSuggestion() {
        const suggestion = $(this).data('suggestion');

        if (suggestion) {
            // Simple formatting for markdown content
            let formattedContent = suggestion
                // Handle code blocks
                .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
                // Handle inline code
                .replace(/`([^`]+)`/g, '<code>$1</code>')
                // Handle bold
                .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
                // Handle italic
                .replace(/\*([^*]+)\*/g, '<em>$1</em>')
                // Handle links
                .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
                // Handle unordered lists
                .replace(/^\s*-\s+(.+)$/gm, '<li>$1</li>')
                // Handle ordered lists
                .replace(/^\s*\d+\.\s+(.+)$/gm, '<li>$1</li>')
                // Handle headings
                .replace(/^\s*#\s+(.+)$/gm, '<h1>$1</h1>')
                .replace(/^\s*##\s+(.+)$/gm, '<h2>$1</h2>')
                .replace(/^\s*###\s+(.+)$/gm, '<h3>$1</h3>');

            // Replace lists with proper HTML
            formattedContent = formattedContent.replace(/(<li>.+<\/li>\s*)+/g, '<ul>$&</ul>');

            $('#qkb-suggestion-content').html(formattedContent);
            $('#qkb-suggestion-modal').addClass('open');
        } else {
            alert(qkbContentGaps.noSuggestionMsg);
        }
    }

    /**
     * Show create content modal
     */
    function showCreateContentModal() {
        const topic = $(this).data('topic');

        $('#qkb-content-topic').text(topic);
        $('#qkb-content-editor').val('');
        $('#qkb-modal-message').html('');
        $('#qkb-create-content-modal').addClass('open');

        // Initialize editor if needed
        if (typeof tinyMCE !== 'undefined' && tinyMCE.get('qkb-content-editor') === null) {
            tinyMCE.init({
                selector: '#qkb-content-editor',
                height: 300,
                menubar: false,
                plugins: 'lists link image code',
                toolbar: 'undo redo | formatselect | bold italic | bullist numlist | link image | code',
                content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif; font-size: 16px; }'
            });
        }
    }

    /**
     * Generate content suggestion
     */
    function generateContentSuggestion() {
        const topic = $('#qkb-content-topic').text();
        const $button = $(this);
        const $spinner = $button.siblings('.spinner');
        const $message = $('#qkb-modal-message');

        // Show loading state
        $button.prop('disabled', true);
        $spinner.addClass('is-active');
        $message.html('');

        // Make AJAX request
        $.ajax({
            url: qkbContentGaps.ajaxUrl,
            type: 'POST',
            data: {
                action: 'qkb_generate_gap_suggestion',
                nonce: qkbContentGaps.suggestionNonce,
                gap_id: $button.data('gap-id')
            },
            success: function(response) {
                if (response.success) {
                    // Set content in editor
                    if (typeof tinyMCE !== 'undefined' && tinyMCE.get('qkb-content-editor') !== null) {
                        tinyMCE.get('qkb-content-editor').setContent(response.data.suggestion);
                    } else {
                        $('#qkb-content-editor').val(response.data.suggestion);
                    }

                    $message.html(`<div class="notice notice-success inline"><p>${qkbContentGaps.suggestionGeneratedMsg}</p></div>`);
                } else {
                    $message.html(`<div class="notice notice-error inline"><p>${response.data || qkbContentGaps.errorMsg}</p></div>`);
                }
            },
            error: function() {
                $message.html(`<div class="notice notice-error inline"><p>${qkbContentGaps.errorMsg}</p></div>`);
            },
            complete: function() {
                $button.prop('disabled', false);
                $spinner.removeClass('is-active');
            }
        });
    }

    /**
     * Create content draft
     */
    function createContentDraft() {
        const topic = $('#qkb-content-topic').text();
        const $button = $(this);
        const $spinner = $button.siblings('.spinner');
        const $message = $('#qkb-modal-message');
        let content;

        // Get content from editor
        if (typeof tinyMCE !== 'undefined' && tinyMCE.get('qkb-content-editor') !== null) {
            content = tinyMCE.get('qkb-content-editor').getContent();
        } else {
            content = $('#qkb-content-editor').val();
        }

        if (!content) {
            $message.html(`<div class="notice notice-error inline"><p>${qkbContentGaps.emptyContentMsg}</p></div>`);
            return;
        }

        // Show loading state
        $button.prop('disabled', true);
        $spinner.addClass('is-active');
        $message.html('');

        // Make AJAX request
        $.ajax({
            url: qkbContentGaps.ajaxUrl,
            type: 'POST',
            data: {
                action: 'qkb_create_gap_draft',
                nonce: qkbContentGaps.draftNonce,
                content: content,
                gap_id: $button.data('gap-id')
            },
            success: function(response) {
                if (response.success) {
                    $message.html(`<div class="notice notice-success inline"><p>${qkbContentGaps.draftCreatedMsg}</p></div>`);

                    // Redirect to edit page after a short delay
                    setTimeout(function() {
                        window.location.href = response.data.edit_url;
                    }, 1500);
                } else {
                    $message.html(`<div class="notice notice-error inline"><p>${response.data || qkbContentGaps.errorMsg}</p></div>`);
                    $button.prop('disabled', false);
                    $spinner.removeClass('is-active');
                }
            },
            error: function() {
                $message.html(`<div class="notice notice-error inline"><p>${qkbContentGaps.errorMsg}</p></div>`);
                $button.prop('disabled', false);
                $spinner.removeClass('is-active');
            }
        });
    }

    /**
     * Close all modals
     */
    function closeModals() {
        $('.qkb-modal').removeClass('open');
    }

})(jQuery);
