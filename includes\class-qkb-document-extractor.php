<?php
/**
 * Document Extractor Class
 *
 * Handles extraction of content from PDF, DOC, and DOCX files with improved capabilities
 * including OCR, structure preservation, and metadata extraction.
 *
 * @package Q-Knowledge-Base
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define constants if not already defined
if (!defined('DAY_IN_SECONDS')) {
    define('DAY_IN_SECONDS', 86400);
}

/**
 * Document Extractor Class
 */
class QKB_Document_Extractor
{

    /**
     * Extraction settings
     *
     * @var array
     */
    private $settings;

    /**
     * Constructor
     */
    public function __construct()
    {
        // Default settings
        $this->settings = array(
            'use_ocr' => false,
            'ocr_language' => 'eng',
            'preserve_structure' => true,
            'extract_metadata' => true,
            'cache_extraction' => true,
            'cache_expiration' => 7 * DAY_IN_SECONDS,
        );

        // Load settings from WordPress options if function exists
        if (function_exists('get_option')) {
            $this->settings['use_ocr'] = get_option('qkb_use_ocr', $this->settings['use_ocr']);
            $this->settings['ocr_language'] = get_option('qkb_ocr_language', $this->settings['ocr_language']);
            $this->settings['preserve_structure'] = get_option('qkb_preserve_structure', $this->settings['preserve_structure']);
            $this->settings['extract_metadata'] = get_option('qkb_extract_metadata', $this->settings['extract_metadata']);
            $this->settings['cache_extraction'] = get_option('qkb_cache_extraction', $this->settings['cache_extraction']);
            $this->settings['cache_expiration'] = get_option('qkb_cache_expiration', $this->settings['cache_expiration']);
        }
    }

    /**
     * Extract content from a document file
     *
     * @param string $file_path Path to the document file
     * @param string $file_type File type (pdf, doc, docx)
     * @param array  $options   Additional extraction options
     * @return array Extracted content and metadata
     */
    public function extract($file_path, $file_type, $options = array())
    {
        // Merge options with default settings
        if (function_exists('wp_parse_args')) {
            $options = wp_parse_args($options, $this->settings);
        } else {
            // Simple fallback for wp_parse_args
            $options = array_merge($this->settings, $options);
        }

        // Check if we have a cached version
        $cache_key = 'qkb_doc_extract_' . md5($file_path . serialize($options));
        if ($options['cache_extraction'] && function_exists('get_transient')) {
            $cached = get_transient($cache_key);
            if ($cached !== false) {
                return $cached;
            }
        }

        // Initialize result array
        $result = array(
            'content' => '',
            'metadata' => array(),
            'structure' => array(),
            'error' => null,
        );

        try {
            // Extract based on file type
            switch (strtolower($file_type)) {
                case 'pdf':
                    $result = $this->extract_pdf($file_path, $options);
                    break;
                case 'doc':
                case 'docx':
                    $result = $this->extract_doc($file_path, $options);
                    break;
                default:
                    throw new Exception("Unsupported file type: {$file_type}");
            }

            // Cache the result if caching is enabled
            if ($options['cache_extraction'] && function_exists('set_transient')) {
                set_transient($cache_key, $result, $options['cache_expiration']);
            }

            return $result;
        } catch (Exception $e) {
            error_log('Document Extraction Error: ' . $e->getMessage());
            $result['error'] = $e->getMessage();
            return $result;
        }
    }

    /**
     * Extract content from a PDF file
     *
     * @param string $file_path Path to the PDF file
     * @param array  $options   Extraction options
     * @return array Extracted content and metadata
     */
    private function extract_pdf($file_path, $options)
    {
        require_once QKB_PLUGIN_DIR . 'vendor/autoload.php';

        $result = array(
            'content' => '',
            'metadata' => array(),
            'structure' => array(),
            'error' => null,
        );

        try {
            // First try with smalot/pdfparser
            $parser = new \Smalot\PdfParser\Parser();
            $pdf = $parser->parseFile($file_path);

            // Extract metadata
            if ($options['extract_metadata']) {
                $result['metadata'] = $this->extract_pdf_metadata($pdf);
            }

            // Extract text content
            $content = '';
            $structure = array();

            // Process each page
            foreach ($pdf->getPages() as $page_num => $page) {
                $page_text = $page->getText();

                // Clean up the text - remove excessive whitespace
                $page_text = preg_replace('/\s+/', ' ', $page_text);
                $page_text = preg_replace('/\n\s*\n+/', "\n\n", $page_text);

                // Add page number as heading for better structure
                $content .= "Page " . ($page_num + 1) . "\n\n" . $page_text . "\n\n";

                // Add to structure if enabled
                if ($options['preserve_structure']) {
                    $structure[] = array(
                        'type' => 'page',
                        'number' => $page_num + 1,
                        'content' => $page_text
                    );
                }
            }

            $result['content'] = $content;
            $result['structure'] = $structure;

            // If content is empty or OCR is forced, try OCR
            if ((empty(trim($content)) || $options['use_ocr']) && class_exists('\\thiagoalessio\\TesseractOCR\\TesseractOCR')) {
                $result = $this->perform_ocr_on_pdf($file_path, $options, $result);
            }

            // If still empty, try spatie/pdf-to-text as a fallback
            if (empty(trim($result['content'])) && class_exists('\\Spatie\\PdfToText\\Pdf')) {
                $pdf_to_text = new \Spatie\PdfToText\Pdf();
                $text = $pdf_to_text->setPdf($file_path)->text();

                if (!empty(trim($text))) {
                    // Clean up the text
                    $text = preg_replace('/\s+/', ' ', $text);
                    $text = preg_replace('/\n\s*\n+/', "\n\n", $text);

                    $result['content'] = $text;

                    // Update structure if it was empty
                    if (empty($result['structure']) && $options['preserve_structure']) {
                        $result['structure'][] = array(
                            'type' => 'document',
                            'content' => $text
                        );
                    }
                }
            }

            // Post-process the content to improve readability
            if (!empty($result['content'])) {
                $result['content'] = $this->post_process_content($result['content']);
            }

            return $result;
        } catch (Exception $e) {
            error_log('PDF Extraction Error: ' . $e->getMessage());
            $result['error'] = $e->getMessage();
            return $result;
        }
    }

    /**
     * Post-process extracted content to improve readability
     *
     * @param string $content Raw extracted content
     * @return string Processed content
     */
    private function post_process_content($content)
    {
        // Fix common extraction issues

        // 1. Remove repeated characters that often appear in PDFs
        $content = preg_replace('/([a-zA-Z])\1{3,}/', '$1', $content);

        // 2. Fix broken sentences (sentences split across lines)
        $content = preg_replace('/(\w)- (\w)/', '$1$2', $content);

        // 3. Normalize spacing around punctuation
        $content = preg_replace('/\s+([.,;:!?])/', '$1', $content);

        // 4. Ensure proper spacing after punctuation
        $content = preg_replace('/([.,;:!?])(\w)/', '$1 $2', $content);

        // 5. Remove excessive line breaks
        $content = preg_replace('/\n{3,}/', "\n\n", $content);

        return $content;
    }

    /**
     * Extract metadata from a PDF document
     *
     * @param \Smalot\PdfParser\Document $pdf PDF document
     * @return array Metadata
     */
    private function extract_pdf_metadata($pdf)
    {
        $metadata = array();

        // Extract standard metadata
        $details = $pdf->getDetails();

        if (isset($details['Title']))
            $metadata['title'] = $details['Title'];
        if (isset($details['Author']))
            $metadata['author'] = $details['Author'];
        if (isset($details['Subject']))
            $metadata['subject'] = $details['Subject'];
        if (isset($details['Keywords']))
            $metadata['keywords'] = $details['Keywords'];
        if (isset($details['CreationDate']))
            $metadata['creation_date'] = $details['CreationDate'];
        if (isset($details['ModDate']))
            $metadata['modification_date'] = $details['ModDate'];
        if (isset($details['Creator']))
            $metadata['creator'] = $details['Creator'];
        if (isset($details['Producer']))
            $metadata['producer'] = $details['Producer'];

        // Add page count
        $metadata['page_count'] = count($pdf->getPages());

        return $metadata;
    }

    /**
     * Perform OCR on a PDF file
     *
     * @param string $file_path Path to the PDF file
     * @param array  $options   Extraction options
     * @param array  $result    Current extraction result
     * @return array Updated extraction result
     */
    private function perform_ocr_on_pdf($file_path, $options, $result)
    {
        // Check if we have the required dependencies
        if (!class_exists('\\thiagoalessio\\TesseractOCR\\TesseractOCR')) {
            return $result;
        }

        try {
            // Create a temporary directory for image extraction
            $temp_dir = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'qkb_ocr_' . uniqid();
            if (!file_exists($temp_dir)) {
                mkdir($temp_dir, 0755, true);
            }

            // Convert PDF to images using Imagick if available
            if (extension_loaded('imagick')) {
                $imagick = new \Imagick();
                $imagick->setResolution(300, 300); // Higher resolution for better OCR
                $imagick->readImage($file_path);
                $imagick->setImageFormat('png');

                $ocr_text = '';
                $structure = array();

                // Process each page
                foreach ($imagick as $page_num => $page) {
                    $image_path = $temp_dir . '/page_' . ($page_num + 1) . '.png';
                    $page->writeImage($image_path);

                    // Perform OCR on the image with improved settings
                    $tesseract = new \thiagoalessio\TesseractOCR\TesseractOCR($image_path);
                    $tesseract->lang($options['ocr_language']);

                    // Add additional OCR options for better results
                    $tesseract->psm(6); // Assume a single uniform block of text
                    $tesseract->oem(1); // Use LSTM OCR Engine only

                    // Run OCR
                    $page_text = $tesseract->run();

                    // Clean up the OCR text
                    $page_text = preg_replace('/\s+/', ' ', $page_text);
                    $page_text = preg_replace('/\n\s*\n+/', "\n\n", $page_text);

                    // Add page number as heading for better structure
                    $ocr_text .= "Page " . ($page_num + 1) . "\n\n" . $page_text . "\n\n";

                    // Add to structure if enabled
                    if ($options['preserve_structure']) {
                        $structure[] = array(
                            'type' => 'page',
                            'number' => $page_num + 1,
                            'content' => $page_text
                        );
                    }

                    // Clean up the image file
                    unlink($image_path);
                }

                // Update result if OCR produced content
                if (!empty(trim($ocr_text))) {
                    // Post-process the OCR text
                    $ocr_text = $this->post_process_content($ocr_text);

                    $result['content'] = $ocr_text;
                    if ($options['preserve_structure']) {
                        $result['structure'] = $structure;
                    }
                }
            }

            // Clean up temporary directory
            rmdir($temp_dir);

            return $result;
        } catch (Exception $e) {
            error_log('OCR Error: ' . $e->getMessage());
            return $result;
        }
    }

    /**
     * Extract content from a DOC/DOCX file
     *
     * @param string $file_path Path to the DOC/DOCX file
     * @param array  $options   Extraction options
     * @return array Extracted content and metadata
     */
    private function extract_doc($file_path, $options)
    {
        require_once QKB_PLUGIN_DIR . 'vendor/autoload.php';

        $result = array(
            'content' => '',
            'metadata' => array(),
            'structure' => array(),
            'error' => null,
        );

        try {
            $phpWord = \PhpOffice\PhpWord\IOFactory::load($file_path);

            // Extract metadata if enabled
            if ($options['extract_metadata']) {
                $result['metadata'] = $this->extract_doc_metadata($phpWord);
            }

            $content = '';
            $structure = array();

            // Process each section
            foreach ($phpWord->getSections() as $section_num => $section) {
                $section_content = '';
                $section_elements = array();

                foreach ($section->getElements() as $element) {
                    if ($element instanceof \PhpOffice\PhpWord\Element\TextRun) {
                        $text_run_content = '';
                        foreach ($element->getElements() as $textElement) {
                            if ($textElement instanceof \PhpOffice\PhpWord\Element\Text) {
                                $text_run_content .= $textElement->getText();
                            }
                        }
                        $section_content .= $text_run_content . "\n";

                        if ($options['preserve_structure']) {
                            $section_elements[] = array(
                                'type' => 'paragraph',
                                'content' => $text_run_content
                            );
                        }
                    } elseif ($element instanceof \PhpOffice\PhpWord\Element\Text) {
                        $text = $element->getText();
                        $section_content .= $text . "\n";

                        if ($options['preserve_structure']) {
                            $section_elements[] = array(
                                'type' => 'text',
                                'content' => $text
                            );
                        }
                    } elseif ($element instanceof \PhpOffice\PhpWord\Element\Table) {
                        $table_content = $this->extract_table_content($element);
                        $section_content .= $table_content . "\n";

                        if ($options['preserve_structure']) {
                            $section_elements[] = array(
                                'type' => 'table',
                                'content' => $table_content
                            );
                        }
                    }
                }

                $content .= $section_content . "\n\n";

                if ($options['preserve_structure']) {
                    $structure[] = array(
                        'type' => 'section',
                        'number' => $section_num + 1,
                        'content' => $section_content,
                        'elements' => $section_elements
                    );
                }
            }

            $result['content'] = $content;

            if ($options['preserve_structure']) {
                $result['structure'] = $structure;
            }

            return $result;
        } catch (Exception $e) {
            error_log('DOC/DOCX Extraction Error: ' . $e->getMessage());
            $result['error'] = $e->getMessage();
            return $result;
        }
    }

    /**
     * Extract table content from a Word document table
     *
     * @param \PhpOffice\PhpWord\Element\Table $table Table element
     * @return string Formatted table content
     */
    private function extract_table_content($table)
    {
        $content = '';

        foreach ($table->getRows() as $row) {
            $row_content = array();

            foreach ($row->getCells() as $cell) {
                $cell_content = '';

                foreach ($cell->getElements() as $element) {
                    if ($element instanceof \PhpOffice\PhpWord\Element\TextRun) {
                        foreach ($element->getElements() as $textElement) {
                            if ($textElement instanceof \PhpOffice\PhpWord\Element\Text) {
                                $cell_content .= $textElement->getText();
                            }
                        }
                    } elseif ($element instanceof \PhpOffice\PhpWord\Element\Text) {
                        $cell_content .= $element->getText();
                    }
                }

                $row_content[] = trim($cell_content);
            }

            $content .= implode("\t", $row_content) . "\n";
        }

        return $content;
    }

    /**
     * Extract metadata from a DOC/DOCX document
     *
     * @param \PhpOffice\PhpWord\PhpWord $phpWord PHPWord document
     * @return array Metadata
     */
    private function extract_doc_metadata($phpWord)
    {
        $metadata = array();

        $properties = $phpWord->getDocInfo();

        if ($properties) {
            if ($properties->getTitle())
                $metadata['title'] = $properties->getTitle();
            if ($properties->getCreator())
                $metadata['author'] = $properties->getCreator();
            if ($properties->getSubject())
                $metadata['subject'] = $properties->getSubject();
            if ($properties->getDescription())
                $metadata['description'] = $properties->getDescription();
            if ($properties->getKeywords())
                $metadata['keywords'] = $properties->getKeywords();
            if ($properties->getCreated())
                $metadata['creation_date'] = $properties->getCreated();
            if ($properties->getModified())
                $metadata['modification_date'] = $properties->getModified();
            if ($properties->getCategory())
                $metadata['category'] = $properties->getCategory();
            if ($properties->getCompany())
                $metadata['company'] = $properties->getCompany();
        }

        // Add section count
        $metadata['section_count'] = count($phpWord->getSections());

        return $metadata;
    }

    /**
     * Format extracted content based on structure
     *
     * @param array $structure Document structure
     * @return string Formatted content
     */
    public function format_structured_content($structure)
    {
        $content = '';

        foreach ($structure as $section) {
            if ($section['type'] === 'page' || $section['type'] === 'section') {
                $content .= "## " . ucfirst($section['type']) . " " . $section['number'] . "\n\n";

                if (isset($section['elements']) && is_array($section['elements'])) {
                    foreach ($section['elements'] as $element) {
                        if ($element['type'] === 'heading') {
                            $content .= "### " . $element['content'] . "\n\n";
                        } elseif ($element['type'] === 'paragraph') {
                            $content .= $element['content'] . "\n\n";
                        } elseif ($element['type'] === 'table') {
                            $content .= "```\n" . $element['content'] . "```\n\n";
                        } else {
                            $content .= $element['content'] . "\n\n";
                        }
                    }
                } else {
                    $content .= $section['content'] . "\n\n";
                }

                $content .= "---\n\n";
            } else {
                $content .= $section['content'] . "\n\n";
            }
        }

        return $content;
    }

    /**
     * Get document metadata as formatted string
     *
     * @param array $metadata Document metadata
     * @return string Formatted metadata
     */
    public function format_metadata($metadata)
    {
        $formatted = "# Document Metadata\n\n";

        foreach ($metadata as $key => $value) {
            $label = ucwords(str_replace('_', ' ', $key));
            $formatted .= "**{$label}**: {$value}\n";
        }

        return $formatted;
    }

    /**
     * Check if OCR is available
     *
     * @return boolean True if OCR is available
     */
    public function is_ocr_available()
    {
        return class_exists('\\thiagoalessio\\TesseractOCR\\TesseractOCR');
    }

    /**
     * Get available OCR languages
     *
     * @return array Available OCR languages
     */
    public function get_available_ocr_languages()
    {
        if (!$this->is_ocr_available()) {
            return array();
        }

        try {
            $tesseract = new \thiagoalessio\TesseractOCR\TesseractOCR();
            $output = $tesseract->availableLanguages();

            return $output;
        } catch (Exception $e) {
            error_log('Error getting OCR languages: ' . $e->getMessage());
            return array('eng'); // Default to English
        }
    }
}