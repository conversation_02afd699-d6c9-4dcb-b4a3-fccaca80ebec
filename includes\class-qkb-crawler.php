<?php
if (!defined('ABSPATH')) {
    exit;
}

require_once(ABSPATH . 'wp-includes/class-wp-error.php');
require_once(ABSPATH . 'wp-includes/class-wp-http.php');

class QKB_Crawler
{
    private $processed_urls = array();
    private $max_pages = 100;
    private $current_count = 0;
    private $error_handler;
    private $rate_limiter;
    private $crawl_session_id;
    private $domain_content = array(); // Store content by domain

    public function __construct()
    {
        $this->rate_limiter = new QKB_Rate_Limiter();
        $this->error_handler = new QKB_Error_Handler();
        $this->crawl_session_id = uniqid('qkb_crawl_');

        add_action('qkb_crawl_scheduled_event', array($this, 'process_scheduled_crawl'));
        add_action('wp_ajax_qkb_manual_crawl', array($this, 'handle_manual_crawl'));
    }

    public function init()
    {
        if (!wp_next_scheduled('qkb_crawl_scheduled_event')) {
            wp_schedule_event(time(), get_option('qkb_crawl_frequency', 'weekly'), 'qkb_crawl_scheduled_event');
        }
    }

    public function process_url($url)
    {
        try {
            if (!$this->is_valid_url($url)) {
                $this->log_error($url, "Invalid URL format");
                return;
            }

            $domain = parse_url($url, PHP_URL_HOST);
            if (!isset($this->domain_content[$domain])) {
                $this->domain_content[$domain] = array(
                    'count' => 0,
                    'urls' => array()
                );
            }

            // Check domain-specific limits
            if ($this->domain_content[$domain]['count'] >= $this->max_pages) {
                return;
            }

            $stored_progress = get_transient($this->crawl_session_id);
            if ($stored_progress) {
                $this->processed_urls = $stored_progress['processed_urls'];
                $this->current_count = $stored_progress['current_count'];
            }

            if (in_array($url, $this->processed_urls) || $this->current_count >= $this->max_pages) {
                return;
            }

            $this->processed_urls[] = $url;
            $this->current_count++;

            set_transient($this->crawl_session_id, [
                'processed_urls' => $this->processed_urls,
                'current_count' => $this->current_count
            ], HOUR_IN_SECONDS);

            $response = wp_remote_get($url, array(
                'timeout' => 30,
                'user-assistant' => 'Q Knowledge Base Crawler/1.0'
            ));

            if (is_wp_error($response)) {
                $this->log_error($url, $response->get_error_message());
                return;
            }

            $status_code = wp_remote_retrieve_response_code($response);
            if ($status_code !== 200) {
                $this->log_error($url, "HTTP status code: $status_code");
                return;
            }

            $html = wp_remote_retrieve_body($response);
            if (empty($html)) {
                $this->log_error($url, "Empty response body");
                return;
            }

            $doc = new DOMDocument();
            libxml_use_internal_errors(true);
            @$doc->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
            libxml_clear_errors();

            // Extract title BEFORE removing unwanted elements (including title tag)
            $title = $this->get_page_title($doc, $url);
            $content = $this->extract_main_content($doc);

            if (!empty($content) && $this->is_content_meaningful($content)) {
                $this->save_to_knowledge_base($url, $title, $content);
                error_log("QKB Crawler: Successfully extracted meaningful content from $url (length: " . strlen($content) . " chars) with title: '$title'");
            } else {
                if (empty($content)) {
                    error_log("QKB Crawler: No content extracted from $url");
                } else {
                    error_log("QKB Crawler: Content from $url was not meaningful (length: " . strlen($content) . " chars)");
                }
            }

            // Update domain tracking
            $this->domain_content[$domain]['count']++;
            $this->domain_content[$domain]['urls'][] = $url;

            // Enhanced link extraction
            $links = $this->extract_all_links($doc, $url);
            foreach ($links as $link) {
                if ($this->should_crawl_url($link, $url)) {
                    $this->rate_limiter->throttle();
                    $this->process_url($link);
                }
            }

        } catch (Exception $e) {
            $this->error_handler->handle_exception($e, $url);
        }
    }

    private function extract_main_content($doc)
    {
        $content = '';
        $xpath = new DOMXPath($doc);

        // First, remove all unwanted elements from the document
        $this->remove_unwanted_elements($doc, $xpath);

        $selectors = array(
            array('type' => 'class', 'value' => 'article-content'),
            array('type' => 'class', 'value' => 'post-content'),
            array('type' => 'class', 'value' => 'entry-content'),
            array('type' => 'id', 'value' => 'content'),
            array('type' => 'tag', 'value' => 'article'),
            array('type' => 'class', 'value' => 'main-content'),
            array('type' => 'tag', 'value' => 'main'),
            array('type' => 'class', 'value' => 'content'),
        );

        foreach ($selectors as $selector) {
            $elements = null;
            if ($selector['type'] === 'class') {
                $elements = $xpath->query("//*[contains(@class, '{$selector['value']}')]");
            } elseif ($selector['type'] === 'id') {
                $elements = $xpath->query("//*[@id='{$selector['value']}']");
            } elseif ($selector['type'] === 'tag') {
                $elements = $doc->getElementsByTagName($selector['value']);
            }

            if ($elements && $elements->length > 0) {
                foreach ($elements as $element) {
                    if ($this->should_skip_element($element)) {
                        continue;
                    }
                    $element_content = $this->extract_text_content($element);
                    if (!empty($element_content)) {
                        $content .= $element_content . "\n\n";
                    }
                }
                if (!empty($content)) {
                    break;
                }
            }
        }

        if (empty($content)) {
            $body = $doc->getElementsByTagName('body');
            if ($body->length > 0) {
                $content = $this->extract_text_content($body[0]);
            }
        }

        return $this->clean_content($content);
    }

    /**
     * Remove unwanted elements from the DOM before extracting content
     */
    private function remove_unwanted_elements($doc, $xpath)
    {
        // Define unwanted elements by tag name
        $unwanted_elements = array(
            '//script',
            '//style',
            '//noscript',
            '//iframe',
            '//nav',
            '//header',
            '//footer',
            '//form',
            '//aside',
            '//link[@rel="stylesheet"]',
            '//meta',
            '//title'
        );

        // Define unwanted classes and IDs
        $unwanted_selectors = array(
            'sidebar',
            'menu',
            'footer',
            'header',
            'navigation',
            'nav',
            'comment',
            'comments',
            'widget',
            'ad',
            'ads',
            'advertisement',
            'social',
            'share',
            'related',
            'popup',
            'modal',
            'cookie',
            'newsletter',
            'subscription'
        );

        // Remove elements by tag name
        foreach ($unwanted_elements as $element_xpath) {
            $elements = $xpath->query($element_xpath);
            if ($elements) {
                foreach ($elements as $element) {
                    if ($element->parentNode) {
                        $element->parentNode->removeChild($element);
                    }
                }
            }
        }

        // Remove elements by class and ID
        foreach ($unwanted_selectors as $selector) {
            // Remove by class
            $class_elements = $xpath->query("//*[contains(concat(' ', normalize-space(@class), ' '), ' $selector ')]");
            if ($class_elements) {
                foreach ($class_elements as $element) {
                    if ($element->parentNode) {
                        $element->parentNode->removeChild($element);
                    }
                }
            }

            // Remove by ID
            $id_elements = $xpath->query("//*[contains(@id, '$selector')]");
            if ($id_elements) {
                foreach ($id_elements as $element) {
                    if ($element->parentNode) {
                        $element->parentNode->removeChild($element);
                    }
                }
            }
        }

        // Remove HTML comments
        $comments = $xpath->query('//comment()');
        if ($comments) {
            foreach ($comments as $comment) {
                if ($comment->parentNode) {
                    $comment->parentNode->removeChild($comment);
                }
            }
        }

        // Remove empty elements (but keep important ones like img, br, hr)
        $empty_elements = $xpath->query("//*[not(normalize-space()) and not(self::img) and not(self::br) and not(self::hr) and not(self::input)]");
        if ($empty_elements) {
            foreach ($empty_elements as $element) {
                if ($element->parentNode) {
                    $element->parentNode->removeChild($element);
                }
            }
        }
    }

    /**
     * Extract clean text content from a DOM element
     */
    private function extract_text_content($element)
    {
        if (!$element) {
            return '';
        }

        // Get the text content
        $text = $element->textContent;

        // Clean up the text
        $text = $this->clean_extracted_text($text);

        return $text;
    }

    /**
     * Clean extracted text content
     */
    private function clean_extracted_text($text)
    {
        // Remove extra whitespace
        $text = preg_replace('/\s+/', ' ', $text);

        // Remove common noise patterns
        $noise_patterns = array(
            '/\b\d+\s*(min|minute|minutes) read\b/i',
            '/\bshare this\b/i',
            '/\bfollow us\b/i',
            '/\bsubscribe\b/i',
            '/\bclick here\b/i',
            '/\bread more\b/i',
            '/\blearn more\b/i',
            '/\bsee more\b/i',
            '/\bview all\b/i',
            '/\bshow all\b/i',
            '/\bload more\b/i',
            '/\bcontinue reading\b/i',
            '/\bnext page\b/i',
            '/\bprevious page\b/i',
            '/\bpage \d+ of \d+\b/i',
            '/\bshowing \d+ of \d+\b/i',
            '/\bresults \d+-\d+ of \d+\b/i',
            '/\bprint this page\b/i',
            '/\bemail this\b/i',
            '/\bshare on facebook\b/i',
            '/\bshare on twitter\b/i',
            '/\bshare on linkedin\b/i',
            '/\blike us on facebook\b/i',
            '/\bfollow us on twitter\b/i',
            '/\bjoin our newsletter\b/i',
            '/\bsign up for\b/i',
            '/\bterms of service\b/i',
            '/\bprivacy policy\b/i',
            '/\bcookie policy\b/i',
            '/\baccept cookies\b/i',
            '/\bwe use cookies\b/i',
            '/\badvertisement\b/i',
            '/\bsponsored\b/i',
            '/\brelated articles\b/i',
            '/\byou might also like\b/i',
            '/\brecommended for you\b/i',
            '/\bpopular posts\b/i',
            '/\brecent posts\b/i',
            '/\btags:\s*$/i',
            '/\bcategories:\s*$/i',
            '/\bposted by\b/i',
            '/\bposted on\b/i',
            '/\bpublished on\b/i',
            '/\blast updated\b/i',
            '/\bcomments?\s*\(\d+\)/i',
            '/\b\d+\s*comments?\b/i',
            '/\bno comments\b/i',
            '/\bleave a comment\b/i',
            '/\bpost a comment\b/i',
            '/\byour email address will not be published\b/i',
            '/\brequired fields are marked\b/i',
            '/\bname\s*\*?\s*email\s*\*?\s*website\b/i',
            '/\bsave my name, email, and website\b/i'
        );

        $text = preg_replace($noise_patterns, '', $text);

        // Remove URLs
        $text = preg_replace('/https?:\/\/[^\s]+/i', '', $text);

        // Remove email addresses
        $text = preg_replace('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/', '', $text);

        // Remove excessive punctuation
        $text = preg_replace('/[.]{3,}/', '...', $text);
        $text = preg_replace('/[-]{3,}/', '---', $text);
        $text = preg_replace('/[=]{3,}/', '', $text);
        $text = preg_replace('/[*]{3,}/', '', $text);

        // Remove standalone numbers that might be navigation or pagination
        $text = preg_replace('/\b\d+\s*\|\s*\d+\b/', '', $text);
        $text = preg_replace('/\b\d+\s*\/\s*\d+\b/', '', $text);

        // Clean up extra spaces again
        $text = preg_replace('/\s+/', ' ', $text);

        return trim($text);
    }

    /**
     * Check if the extracted content is meaningful and not just CSS/JS remnants
     */
    private function is_content_meaningful($content)
    {
        // Remove whitespace for analysis
        $trimmed_content = trim($content);

        // Check minimum length
        if (strlen($trimmed_content) < 50) {
            return false;
        }

        // Check for CSS/JS patterns that might have leaked through
        $css_js_patterns = array(
            '/^\s*[\{\};\(\)]+\s*$/',  // Only CSS/JS syntax characters
            '/^\s*function\s*\(/i',    // JavaScript function declarations
            '/^\s*var\s+\w+\s*=/i',    // JavaScript variable declarations
            '/^\s*\$\(\s*["\']/',      // jQuery selectors
            '/^\s*@media\s+/i',        // CSS media queries
            '/^\s*\.[\w-]+\s*\{/',     // CSS class selectors
            '/^\s*#[\w-]+\s*\{/',      // CSS ID selectors
            '/^\s*\w+\s*:\s*[\w\d#%px-]+\s*;/', // CSS property declarations
            '/^\s*\/\*.*\*\/\s*$/s',   // CSS comments
            '/^\s*\/\/.*$/m',          // JavaScript comments
            '/^\s*<\?php/i',           // PHP code
            '/^\s*<script/i',          // Script tags
            '/^\s*<style/i',           // Style tags
        );

        foreach ($css_js_patterns as $pattern) {
            if (preg_match($pattern, $trimmed_content)) {
                return false;
            }
        }

        // Check for meaningful word count
        $words = str_word_count($trimmed_content);
        if ($words < 10) {
            return false;
        }

        // Check for reasonable sentence structure
        $sentences = preg_split('/[.!?]+/', $trimmed_content);
        $meaningful_sentences = 0;

        foreach ($sentences as $sentence) {
            $sentence = trim($sentence);
            if (strlen($sentence) > 20 && str_word_count($sentence) >= 3) {
                $meaningful_sentences++;
            }
        }

        // Require at least 2 meaningful sentences
        if ($meaningful_sentences < 2) {
            return false;
        }

        // Check for excessive repetition (might indicate navigation or menu items)
        $words_array = explode(' ', strtolower($trimmed_content));
        $word_counts = array_count_values($words_array);
        $total_words = count($words_array);

        // If any single word makes up more than 20% of content, it's likely repetitive
        foreach ($word_counts as $count) {
            if ($count > ($total_words * 0.2) && $total_words > 20) {
                return false;
            }
        }

        // Check for common non-content indicators
        $non_content_indicators = array(
            'javascript is disabled',
            'enable javascript',
            'cookies are disabled',
            'enable cookies',
            'page not found',
            '404 error',
            'access denied',
            'permission denied',
            'login required',
            'please log in',
            'sign in to continue',
            'loading...',
            'please wait',
            'redirecting...',
            'under construction',
            'coming soon',
            'maintenance mode'
        );

        $lower_content = strtolower($trimmed_content);
        foreach ($non_content_indicators as $indicator) {
            if (strpos($lower_content, $indicator) !== false) {
                return false;
            }
        }

        return true;
    }

    private function should_skip_element($element)
    {
        $skip_classes = array('nav', 'menu', 'sidebar', 'footer', 'header', 'comments', 'advertisement', 'ad', 'social', 'share');
        $skip_ids = array('nav', 'menu', 'sidebar', 'footer', 'header', 'comments', 'advertisement', 'ad', 'social', 'share');

        $class_attr = $element->getAttribute('class');
        $id_attr = $element->getAttribute('id');

        // Check classes
        if (!empty($class_attr)) {
            foreach ($skip_classes as $class) {
                if (strpos(strtolower($class_attr), $class) !== false) {
                    return true;
                }
            }
        }

        // Check IDs
        if (!empty($id_attr)) {
            foreach ($skip_ids as $id) {
                if (strpos(strtolower($id_attr), $id) !== false) {
                    return true;
                }
            }
        }

        // Check tag names that should be skipped
        $skip_tags = array('script', 'style', 'noscript', 'iframe', 'form');
        if (in_array(strtolower($element->nodeName), $skip_tags)) {
            return true;
        }

        return false;
    }

    private function clean_content($content)
    {
        // Use the comprehensive text cleaning method
        return $this->clean_extracted_text($content);
    }

    private function get_page_title($doc, $url = '')
    {
        $title = '';

        // Try to get title from <title> tag
        $titles = $doc->getElementsByTagName('title');
        if ($titles->length > 0) {
            $title = trim($titles[0]->textContent);
            error_log("QKB Crawler: Found title tag: '$title' for URL: $url");
        }

        // If no title found or title is empty, try alternative methods
        if (empty($title)) {
            $xpath = new DOMXPath($doc);

            // Try h1 tags
            $h1_elements = $xpath->query('//h1');
            if ($h1_elements->length > 0) {
                $title = trim($h1_elements[0]->textContent);
                error_log("QKB Crawler: Found H1 title: '$title' for URL: $url");
            }

            // Try meta title
            if (empty($title)) {
                $meta_title = $xpath->query('//meta[@property="og:title"]/@content');
                if ($meta_title->length > 0) {
                    $title = trim($meta_title[0]->textContent);
                    error_log("QKB Crawler: Found meta og:title: '$title' for URL: $url");
                }
            }

            // Try meta twitter title
            if (empty($title)) {
                $twitter_title = $xpath->query('//meta[@name="twitter:title"]/@content');
                if ($twitter_title->length > 0) {
                    $title = trim($twitter_title[0]->textContent);
                    error_log("QKB Crawler: Found twitter:title: '$title' for URL: $url");
                }
            }
        }

        // If still no title, generate one from URL
        if (empty($title) && !empty($url)) {
            $parsed_url = parse_url($url);
            $path = isset($parsed_url['path']) ? $parsed_url['path'] : '';

            // Remove leading slash and file extension
            $path = trim($path, '/');
            $path = preg_replace('/\.[^.]*$/', '', $path); // Remove file extension

            if (!empty($path)) {
                // Convert path to readable title
                $title = str_replace(['/', '-', '_'], ' ', $path);
                $title = ucwords($title);
                error_log("QKB Crawler: Generated title from path: '$title' for URL: $url");
            } else {
                // Use domain name as fallback
                $title = isset($parsed_url['host']) ? $parsed_url['host'] : 'Untitled Page';
                error_log("QKB Crawler: Using domain as title: '$title' for URL: $url");
            }
        }

        // Clean and sanitize the title
        $title = preg_replace('/\s+/', ' ', $title); // Remove extra whitespace
        $title = trim($title);

        // If title is still empty, provide a default
        if (empty($title)) {
            $title = 'Untitled Page';
            error_log("QKB Crawler: Using default title for URL: $url");
        }

        error_log("QKB Crawler: Final title: '$title' for URL: $url");
        return $title;
    }

    private function save_to_knowledge_base($url, $title, $content)
    {
        error_log("QKB Crawler: Saving to knowledge base - URL: $url, Title: '$title'");

        $existing_post = get_posts(array(
            'post_type' => 'kb_external_content',
            'meta_key' => '_qkb_source_url',
            'meta_value' => $url,
            'posts_per_page' => 1
        ));

        if (!empty($existing_post)) {
            $sanitized_title = sanitize_text_field($title);
            error_log("QKB Crawler: Updating existing post ID {$existing_post[0]->ID} with title: '$sanitized_title'");
            wp_update_post(array(
                'ID' => $existing_post[0]->ID,
                'post_title' => $sanitized_title,
                'post_content' => wp_kses_post($content),
                'post_modified' => current_time('mysql')
            ));
        } else {
            $sanitized_title = sanitize_text_field($title);
            error_log("QKB Crawler: Creating new post with title: '$sanitized_title'");
            $post_id = wp_insert_post(array(
                'post_type' => 'kb_external_content',
                'post_title' => $sanitized_title,
                'post_content' => wp_kses_post($content),
                'post_status' => 'publish'
            ));

            if ($post_id) {
                error_log("QKB Crawler: Successfully created post ID: $post_id with title: '$sanitized_title'");
                update_post_meta($post_id, '_qkb_source_url', esc_url_raw($url));
                $openai = new QKB_OpenAI_Handler();
                $embedding = $openai->get_embedding($title . "\n" . $content);
                if (!is_wp_error($embedding)) {
                    update_post_meta($post_id, '_qkb_embedding', $embedding);
                }
            } else {
                error_log("QKB Crawler: Failed to create post for URL: $url with title: '$sanitized_title'");
            }
        }
    }

    private function extract_all_links($doc, $base_url)
    {
        $links = array();
        $xpath = new DOMXPath($doc);

        // Get all <a> tags
        $anchors = $xpath->query('//a[@href]');
        foreach ($anchors as $anchor) {
            $href = $anchor->getAttribute('href');
            $absolute_url = $this->make_absolute_url($href, $base_url);
            if ($absolute_url) {
                $links[] = $absolute_url;
            }
        }

        // Get links from sitemaps if available
        $sitemap_links = $this->process_sitemap($base_url);
        if (!empty($sitemap_links)) {
            $links = array_merge($links, $sitemap_links);
        }

        return array_unique($links);
    }

    private function process_sitemap($base_url)
    {
        $links = array();
        $sitemap_url = rtrim($base_url, '/') . '/sitemap.xml';

        $response = wp_remote_get($sitemap_url);
        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $xml = wp_remote_retrieve_body($response);
            try {
                $sitemap = new SimpleXMLElement($xml);
                foreach ($sitemap->url as $url) {
                    $links[] = (string) $url->loc;
                }
            } catch (Exception $e) {
                $this->log_error($sitemap_url, "Failed to parse sitemap: " . $e->getMessage());
            }
        }

        return $links;
    }

    private function is_valid_url($url)
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }

    private function should_crawl_url($href, $base_url)
    {
        if (empty($href))
            return false;

        $absolute_url = $this->make_absolute_url($href, $base_url);
        if (!$absolute_url)
            return false;

        // Check if URL has already been processed
        if (in_array($absolute_url, $this->processed_urls)) {
            return false;
        }

        // Check domain limits
        $parsed_url = parse_url($absolute_url);
        if (!isset($parsed_url['host'])) {
            return false;
        }
        $domain = $parsed_url['host'];

        if (
            isset($this->domain_content[$domain]) &&
            $this->domain_content[$domain]['count'] >= $this->max_pages
        ) {
            return false;
        }

        // Skip non-HTML resources and certain patterns
        $skip_patterns = array(
            '#\.(jpg|jpeg|png|gif|pdf|doc|docx|zip|rar|css|js)$#i',
            '#/wp-admin/#',
            '#/wp-login\.php#',
            '#/feed/#',
            '#/comments/#',
            '#\?#', // Skip URLs with query parameters
        );

        foreach ($skip_patterns as $pattern) {
            if (preg_match($pattern, $absolute_url)) {
                return false;
            }
        }

        return $this->is_crawling_allowed($absolute_url);
    }

    private function make_absolute_url($href, $base_url)
    {
        if (empty($href))
            return false;

        // Handle javascript: and data: URLs
        if (preg_match('/^(javascript:|data:|mailto:|tel:)/i', $href)) {
            return false;
        }

        // Handle fragment-only URLs
        if (strpos($href, '#') === 0) {
            return false;
        }

        if (parse_url($href, PHP_URL_SCHEME) !== null) {
            return $href;
        }

        $parsed_base = parse_url($base_url);
        if (!isset($parsed_base['host'])) {
            return false;
        }

        // Build absolute URL
        $scheme = isset($parsed_base['scheme']) ? $parsed_base['scheme'] : 'http';
        $host = $parsed_base['host'];
        $path = isset($parsed_base['path']) ? dirname($parsed_base['path']) : '';

        if (strpos($href, '/') === 0) {
            return "$scheme://$host$href";
        }

        return "$scheme://$host" . rtrim($path, '/') . '/' . ltrim($href, '/');
    }

    public function process_scheduled_crawl()
    {
        $urls = get_option('qkb_external_urls', array());
        foreach ($urls as $url) {
            $this->current_count = 0;
            $this->processed_urls = array();
            $this->process_url($url);
        }
    }

    public function handle_manual_crawl()
    {
        check_ajax_referer('qkb_manual_crawl', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error([
                'message' => __('Permission denied', 'q-knowledge-base')
            ]);
        }

        try {
            $urls = get_option('qkb_external_urls', array());
            $processed = 0;

            delete_transient($this->crawl_session_id);

            set_transient($this->crawl_session_id, [
                'processed_urls' => array(),
                'current_count' => 0
            ], HOUR_IN_SECONDS);

            foreach ($urls as $url) {
                $this->process_url($url);
                $processed++;

                wp_send_json_success([
                    'message' => sprintf(
                        __('Processing URL %d of %d', 'q-knowledge-base'),
                        $processed,
                        count($urls)
                    ),
                    'progress' => ($processed / count($urls)) * 100
                ]);
            }

            delete_transient($this->crawl_session_id);

            wp_send_json_success([
                'message' => sprintf(
                    __('Successfully crawled %d URLs', 'q-knowledge-base'),
                    $processed
                ),
                'progress' => 100
            ]);

        } catch (Exception $e) {
            wp_send_json_error([
                'message' => $e->getMessage()
            ]);
        }
    }

    private function log_error($url, $message)
    {
        $errors = get_option('qkb_crawler_errors', array());
        $errors[] = array(
            'url' => $url,
            'message' => $message,
            'time' => current_time('mysql'),
            'session_id' => $this->crawl_session_id
        );

        if (count($errors) > 100) {
            array_shift($errors);
        }

        update_option('qkb_crawler_errors', $errors);
    }

    private function is_crawling_allowed($url)
    {
        $parsed_url = parse_url($url);
        $robots_url = $parsed_url['scheme'] . '://' . $parsed_url['host'] . '/robots.txt';

        $response = wp_remote_get($robots_url);
        if (is_wp_error($response)) {
            return true;
        }

        $robots_content = wp_remote_retrieve_body($response);
        if (empty($robots_content)) {
            return true;
        }

        $path = isset($parsed_url['path']) ? $parsed_url['path'] : '/';
        $lines = explode("\n", $robots_content);
        $current_assistant = '';
        $disallowed = array();

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line) || strpos($line, '#') === 0) {
                continue;
            }

            if (preg_match('/^User-assistant:\s*(.+)$/i', $line, $matches)) {
                $current_assistant = strtolower($matches[1]);
            } elseif (preg_match('/^Disallow:\s*(.+)$/i', $line, $matches)) {
                if ($current_assistant === '*' || $current_assistant === 'q knowledge base crawler') {
                    $disallowed[] = $matches[1];
                }
            }
        }

        foreach ($disallowed as $pattern) {
            if (strpos($path, $pattern) === 0) {
                return false;
            }
        }

        return true;
    }
}