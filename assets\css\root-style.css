@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
:root {
    /* Primary Colors */
    --qkb-primary: #2271b1;
    --qkb-primary-dark: #135e96;
    --qkb-primary-light: #2271b180;

    /* Status Colors */
    --qkb-error: #dc2626;
    --qkb-success: #16a34a;
    --qkb-warning: #f59e0b;


    /* Text Colors */
    --qkb-text: #2c3338;
    --qkb-text-light: #646970;
    --qkb-text-dark: #1f2328;

    /* Background Colors */
    --qkb-bg: #ffffff;
    --qkb-bg-light: #f6f7f7;

    /* Border and Shadows */
    --qkb-border: #e1e4e8;
    --qkb-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    --qkb-shadow-sm: 0 5px 10px rgba(0, 0, 0, 0.3);

    /* Radius */
    --qkb-radius-xs: 5px;
    --qkb-radius-sm: 10px;
    --qkb-radius-md: 15px;
    --qkb-radius-lg: 20px;
    --qkb-radius-xl: 25px;

    /* Spacing */
    --qkb-gap: 5px;
    --qkb-gap-md: 10px;
    --qkb-gap-lg: 15px;
    --qkb-gap-xl: 20px;

    /* Padding */
    --qkb-padding: 15px 20px;
    --qkb-padding-md: 10px 15px;
    --qkb-padding-sm: 5px 10px;

    /* Effects */
    --qkb-gradient: linear-gradient(135deg, var(--qkb-primary), var(--qkb-primary-dark));
    --qkb-transition: all 0.5s cubic-bezier(0.5, 0, 0.2, 1);
    --qkb-transform: scale(1.05);
    --qkb-filter: brightness(0) invert(1);
    --qkb-blur: blur(4px);

    /* Typography */
    --qkb-font: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --qkb-font-size-xs: 0.65rem;
    --qkb-font-size-sm: 0.75rem;
    --qkb-font-size-md: 0.875rem;
    --qkb-font-size-lg: 1rem;
    --qkb-letter-spacing: 0.05em;


    /* Font Weights */

    --qkb-font-weight-light: 300;
    --qkb-font-weight-normal: 400;
    --qkb-font-weight-medium: 500;
    --qkb-font-weight-semibold: 600;
    --qkb-font-weight-bold: 800;

    /* Z-Index */
    --qkb-z-index-low: 10;
    --qkb-z-index-mid: 50;
    --qkb-z-index-high: 100;
    --qkb-z-index-overlay: 999;
    --qkb-z-index-modal: 1000;
    --qkb-z-index-top: 999999;
}