<?php
/**
 * QKB Chat Interface Renderer
 *
 * Handles rendering of the embedded chat interface
 */

if (!defined('ABSPATH')) {
    exit;
}

require_once QKB_PLUGIN_DIR . 'includes/chat/class-qkb-chat-renderer.php';

class QKB_Chat_Interface_Renderer extends QKB_Chat_Renderer
{
    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Initialize settings for the embedded chat interface
     */
    protected function init_settings()
    {
        $this->settings = [
            'chatbot_name' => get_option('qkb_chat_interface_name', get_option('qkb_chatbot_name', 'Ask Q')),
            'chatbot_icon' => get_option('qkb_chat_interface_icon', get_option('qkb_chatbot_icon', QKB_PLUGIN_URL . 'assets/images/q.svg')),
            'splash_subtitle' => get_option('qkb_chat_interface_subtitle', get_option('qkb_splash_subtitle', 'Your AI Assistant')),
            'fullscreen_default' => get_option('qkb_chat_interface_fullscreen_default', false),
        ];
    }

    /**
     * Check if user can add knowledge
     *
     * @return bool True if user can add knowledge
     */
    private function can_add_knowledge()
    {
        // Get allowed roles
        $allowed_roles = get_option('qkb_add_knowledge_roles', []);

        // Get current user's roles
        $user = wp_get_current_user();
        if (!$user->exists()) {
            return false;
        }

        // Check if user has any allowed role
        $user_roles = (array) $user->roles;
        return !empty(array_intersect($user_roles, $allowed_roles));
    }

    /**
     * Render the chat interface
     *
     * @return string HTML output
     */
    public function render()
    {
        // Get current user roles
        $user = wp_get_current_user();
        $user_display_name = $user->display_name ?: $user->user_login;
        $roles = $user->roles;

        // Get sidebar visibility settings
        $sidebar_visibility = get_option('qkb_sidebar_visibility', []);
        $assistant_roles = get_option('qkb_assistant_roles', []);
        $enable_assistants = get_option('qkb_enable_assistants', 'disabled');
        $is_logged_in = $user->exists();
        $user_roles = $is_logged_in ? $user->roles : [];

        // Start output buffering to capture the HTML
        ob_start();

        $accessible_assistants = [];
        if ($enable_assistants === 'enabled' && $is_logged_in) {
            // Get assistants for chat interface - keep hide_empty true here
            $chat_assistants = get_terms([
                'taxonomy' => 'kb_assistant',
                'hide_empty' => true,
                // 'exclude' => [$this->get_default_assistant_id()] // Exclude default assistant
            ]);

            $accessible_assistants = array_filter($chat_assistants, function ($assistant) use ($user_roles, $assistant_roles) {
                if (!isset($assistant_roles[$assistant->term_id]) || empty($assistant_roles[$assistant->term_id])) {
                    return true;
                }
                return array_intersect($user_roles, $assistant_roles[$assistant->term_id]);
            });
        }

        // Add this new code after the above section
        $all_assistants = [];
        if ($this->can_add_knowledge()) {
            // Get all assistants for knowledge form - set hide_empty to false
            $all_assistants = get_terms([
                'taxonomy' => 'kb_assistant',
                'hide_empty' => false,
                'exclude' => [$this->get_default_assistant_id()]
            ]);
        }

        $chatbot_name = $this->settings['chatbot_name'];
        $chatbot_icon = $this->settings['chatbot_icon'];
        $splash_subtitle = $this->settings['splash_subtitle'];

        // Determine if sidebar should be visible
        $show_sidebar = false;
        foreach ($roles as $role) {
            if (isset($sidebar_visibility[$role]) && $sidebar_visibility[$role] === 'show') {
                $show_sidebar = true;
                break;
            }
        }

        // Start building the HTML output
        ?>
        <!-- Main Chat Interface Container -->
        <main class="qi-chat-container" data-fullpage="true" data-show-sidebar="<?php echo $show_sidebar ? 'true' : 'false'; ?>"
            data-fullscreen-default="<?php echo $this->settings['fullscreen_default'] ? 'true' : 'false'; ?>" role="application"
            aria-label="<?php echo esc_attr($chatbot_name); ?> Chat Interface">
            <?php if ($show_sidebar): ?>
                <!-- Sidebar Navigation -->
                <aside class="qi-sidebar" role="complementary" aria-label="Chat Navigation and History">
                    <!-- Brand Header -->
                    <header class="qi-brand">
                        <div class="qi-logo-wrapper qi-primary-logo">
                            <img src="<?php echo esc_url($chatbot_icon); ?>" alt="<?php echo esc_attr($chatbot_name); ?>"
                                class="qi-logo">
                        </div>
                        <div class="qi-brand-text">
                            <h1 class="qi-brand-name"><?php echo esc_html($chatbot_name); ?></h1>
                            <p class="qi-brand-slogan">
                                <?php echo esc_html($splash_subtitle); ?>
                            </p>
                        </div>
                    </header>

                    <?php $this->render_assistants_list($accessible_assistants); ?>
                    <?php $this->render_chat_history($user); ?>

                    <footer class="qi-powered-by" role="contentinfo" aria-label="Powered By">
                        <div class="qi-disclaimer-content">
                            <p><?php _e('Powered by <strong>Q-Ai</strong>', 'q-knowledge-base'); ?></p>
                        </div>
                    </footer>
                </aside>
            <?php endif; ?>

            <!-- Main Chat Area -->
            <section class="qi-main-chat" role="main" aria-label="Chat Messages and Controls">
                <?php $this->render_chat_header($accessible_assistants, $show_sidebar); ?>

                <div class="qi-chat-wrapper" role="log" aria-live="polite" aria-label="Chat Messages">
                    <div class="qi-chat-messages" aria-label="Conversation History">
                        <?php $this->render_welcome_message($user_display_name); ?>
                    </div>

                    <!-- Scroll to bottom button -->
                    <div class="qi-scroll-bottom" title="<?php esc_attr_e('Scroll to bottom', 'q-knowledge-base'); ?>">
                        <i class="fas fa-arrow-down"></i>
                    </div>

                    <!-- Enhanced Chat Input Container -->
                    <?php $this->render_chat_input(); ?>
                </div>

                <footer class="qi-disclaimer" role="contentinfo" aria-label="Disclaimer">
                    <div class="qi-disclaimer-content">
                        <p><strong><?php echo esc_html($chatbot_name); ?></strong> can make mistakes. Please verify important information.</p>
                    </div>
                </footer>
            </section>

            <!-- Knowledge Base Modal -->
            <?php $this->render_knowledge_modal($all_assistants); ?>
        </main>
        <?php

        // Get the HTML output and clear the buffer
        $html_output = ob_get_clean();

        // Add initialization script to the HTML output
        $html_output .= $this->get_initialization_script();

        // Return the HTML instead of echoing it
        return $html_output;
    }

    /**
     * Render assistants list
     *
     * @param array $accessible_assistants List of accessible assistants
     */
    private function render_assistants_list($accessible_assistants)
    {
        ?>
        <div class="qi-assistants-list">
            <h3>
                <?php _e('Available assistants', 'q-knowledge-base'); ?>
            </h3>
            <div class="qi-assistants-wrapper">
                <?php if (!empty($accessible_assistants)): ?>
                    <?php foreach ($accessible_assistants as $assistant):
                        $icon = get_term_meta($assistant->term_id, 'assistant_icon', true);
                        $icon = !empty($icon) ? $icon : 'fas fa-robot';
                        // Get the stored assistant ID from user meta or session
                        $current_assistant_id = get_user_meta(get_current_user_id(), 'qi_current_assistant', true);
                        ?>
                        <div class="qi-assistant-item <?php echo ($current_assistant_id == $assistant->term_id) ? 'active' : ''; ?>"
                            data-assistant-id="<?php echo esc_attr($assistant->term_id); ?>">
                            <div class="qi-assistant-avatar">
                                <i class="<?php echo esc_attr($icon); ?>" aria-hidden="true"></i>
                            </div>
                            <div class="qi-assistant-info">
                                <span class="qi-assistant-name"><?php echo esc_html($assistant->name); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="qi-no-assistants">
                        <p><?php _e('No Assistants', 'q-knowledge-base'); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }

    /**
     * Render chat history
     *
     * @param WP_User $user Current user
     */
    private function render_chat_history($user)
    {
        ?>
        <div class="qi-chat-history">
            <div class="qi-history-list">
                <h3>
                    <?php _e('Chat History', 'q-knowledge-base'); ?>
                </h3>
                <button class="qi-clear-history" title="<?php esc_attr_e('Clear History', 'q-knowledge-base'); ?>">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
            <div class="qi-history-wrapper">
                <?php
                $chat_history = get_option('qkb_chat_history_' . $user->ID, array());

                if (!empty($chat_history)) {
                    foreach ($chat_history as $history_item) {
                        $timestamp = isset($history_item['timestamp']) ? $history_item['timestamp'] : '';
                        $assistant_id = isset($history_item['assistant_id']) ? intval($history_item['assistant_id']) : 0;
                        $title = isset($history_item['title']) ? esc_html($history_item['title']) : '';
                        $assistant_name = '';

                        if ($assistant_id) {
                            $assistant = get_term($assistant_id, 'kb_assistant');
                            if (!is_wp_error($assistant)) {
                                $assistant_name = $assistant->name;
                            }
                        }
                        ?>
                        <div class="qi-history-item" data-chat-id="<?php echo esc_attr($timestamp); ?>"
                            data-assistant-id="<?php echo esc_attr($assistant_id); ?>">
                            <div class="qi-history-assistant"><?php echo esc_html($assistant_name); ?></div>
                            <div class="qi-history-title"><?php echo $title; ?></div>
                            <div class="qi-history-meta">
                                <?php echo esc_html(human_time_diff(strtotime($timestamp), current_time('timestamp'))); ?> ago
                            </div>
                            <button class="qi-delete-history" title="<?php esc_attr_e('Delete conversation', 'q-knowledge-base'); ?>">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <?php
                    }
                } else {
                    ?>
                    <div class="qi-no-history">
                        <div class="qi-no-history-icon">
                            <i class="fas fa-history"></i>
                        </div>
                        <p><?php _e('No chat history yet', 'q-knowledge-base'); ?></p>
                        <p class="qi-no-history-desc">
                            <?php _e('Your conversations will appear here once you start chatting.', 'q-knowledge-base'); ?>
                        </p>
                    </div>
                    <?php
                }
                ?>
            </div>
        </div>
        <?php
    }

    /**
     * Render chat header
     *
     * @param array $accessible_assistants List of accessible assistants
     * @param bool $show_sidebar Whether to show sidebar
     */
    private function render_chat_header($accessible_assistants, $show_sidebar)
    {
        ?>
        <header class="qi-chat-header" role="banner">
        
            <div class="qi-current-assistant">
                    <div class="qi-toggle">
                <?php if ($show_sidebar): ?>
                    <button class="qi-toggle-sidebar" title="<?php esc_attr_e('Toggle Sidebar', 'q-knowledge-base'); ?>">
                        <i class="fas fa-bars"></i>
                    </button>
                <?php endif; ?>
            </div>
                <div class="qi-assistant-icon-wrapper">
                    <i class="fas fa-robot" aria-hidden="true"></i>
                </div>
                <div class="qi-assistant-select-wrapper">
                    <select class="qi-assistant-select">
                        <?php if (!empty($accessible_assistants)): ?>
                            <?php foreach ($accessible_assistants as $assistant):
                                $icon = get_term_meta($assistant->term_id, 'assistant_icon', true);
                                $icon = !empty($icon) ? $icon : 'fas fa-robot';
                                ?>
                                <option value="<?php echo esc_attr($assistant->term_id); ?>" data-icon="<?php echo esc_attr($icon); ?>">
                                    <?php echo esc_html($assistant->name); ?>
                                </option>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <option value="" data-icon="fa-solid fa-handshake-slash">
                                <?php _e('No Assistants', 'q-knowledge-base'); ?>
                            </option>
                        <?php endif; ?>
                    </select>
                </div>
            </div>
            <div class="qi-chat-actions">

                <button class="qi-new-chat" title="<?php esc_attr_e('New Chat', 'q-knowledge-base'); ?>">
                    <i class="fas fa-plus"></i>
                </button>
                <?php
                $user = wp_get_current_user();
                $export_roles = get_option('qkb_export_button_roles', []);
                $show_export = false;
                if (!empty($export_roles)) {
                    $user_roles = (array) $user->roles;
                    if (array_intersect($export_roles, $user_roles)) {
                        $show_export = true;
                    }
                }
                if ($show_export):
                    ?>
                    <div class="qi-export-dropdown">
                        <button class="qi-export-button" title="<?php esc_attr_e('Export Transcript', 'q-knowledge-base'); ?>">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                <?php endif; ?>
                <?php if ($this->can_add_knowledge()): ?>
                    <button class="qi-add-knowledge" title="<?php esc_attr_e('Add Knowledge', 'q-knowledge-base'); ?>">
                        <i class="fas fa-book-medical"></i>
                    </button>
                <?php endif; ?>
                <?php if (!$this->settings['fullscreen_default']): ?>
                    <button class="qi-toggle-fullscreen" title="<?php esc_attr_e('Toggle Fullscreen', 'q-knowledge-base'); ?>">
                        <i class="fas fa-expand"></i>
                    </button>
                <?php endif; ?>
            </div>
        </header>
        <?php
    }

    /**
     * Render welcome message
     *
     * @param string $user_display_name User display name
     */
    private function render_welcome_message($user_display_name)
    {
        ?>
        <div class="qi-welcome-message">
            <div id="qi-particles-js" class="qi-particles-container"></div>
            <div class="qi-welcome-content">
                <div class="qi-welcome-header">
                    <div class="qi-welcome-title">
                        <h2><?php echo esc_html(sprintf(__('Hello, %s', 'q-knowledge-base'), $user_display_name)); ?>
                        </h2>
                        <span><?php _e('How can I assist you today?', 'q-knowledge-base'); ?></span>
                    </div>
                </div>
                <div class="qi-suggested-prompts">
                    <h3><?php _e('Try asking:', 'q-knowledge-base'); ?></h3>
                    <div class="qi-prompt-buttons">
                        <button class="qi-prompt-button"><?php _e('What can you help me with?', 'q-knowledge-base'); ?></button>
                        <button class="qi-prompt-button"><?php _e('How do I get started?', 'q-knowledge-base'); ?></button>
                        <button
                            class="qi-prompt-button"><?php _e('What can you assist me with?', 'q-knowledge-base'); ?></button>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render chat input
     */
    private function render_chat_input()
    {
        ?>
        <div class="qi-input-container" role="form" aria-label="Chat Input">
    
            <div class="qi-input-area" role="textbox">
                <textarea class="qkb-input-textarea" placeholder="How can I help you today?..."
                    aria-label="<?php esc_attr_e('Chat input', 'q-knowledge-base'); ?>" maxlength="500" autocomplete="off"
                    spellcheck="true"></textarea>
            </div>

            <div class="qi-input-buttons">
                <div class="qi-utility-buttons">
                </div>

                <div class="qi-submit-buttons">
                    <button class="qi-send-button" disabled>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="22" y1="2" x2="11" y2="13"></line>
                            <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render knowledge modal
     *
     * @param array $all_assistants List of all assistants
     */
    private function render_knowledge_modal($all_assistants)
    {
        ?>
        <!-- Updated Knowledge Base Modal with Tabs -->
        <div class="qi-kb-modal">
            <div class="qi-kb-modal-content">
                <button class="qi-kb-modal-close-global">×</button>
                <!-- Modal Tabs -->
                <div class="qi-kb-modal-tabs">
                    <button type="button" class="qi-kb-tab active" data-tab="add">
                        <i class="fas fa-plus-circle" aria-hidden="true"></i>
                        <?php _e('Add Knowledge', 'q-knowledge-base'); ?>
                    </button>
                    <button type="button" class="qi-kb-tab" data-tab="manage">
                        <i class="fas fa-list" aria-hidden="true"></i>
                        <?php _e('Manage Knowledge', 'q-knowledge-base'); ?>
                    </button>
                </div>
                <!-- Modal Tab Contents -->
                <div class="qi-kb-tab-contents">
                    <!-- Add Knowledge Tab (existing form) -->
                    <div class="qi-kb-tab-content qi-add-knowledge-content" style="display: block;">
                        <div class="qi-kb-modal-header">
                            <h2><?php _e('Add Knowledge', 'q-knowledge-base'); ?></h2>
                        </div>
                        <form class="qi-kb-form" enctype="multipart/form-data">
                            <div class="qi-kb-form-group">
                                <label for="kb-title"><?php _e('Title', 'q-knowledge-base'); ?></label>
                                <input type="text" id="kb-title" name="title" required>
                            </div>

                            <div class="qi-kb-form-group">
                                <label for="kb-type"><?php _e('Content Type', 'q-knowledge-base'); ?></label>
                                <select id="kb-type" name="content_type" required>
                                    <option value=""><?php _e('Select Type', 'q-knowledge-base'); ?></option>
                                    <option value="text"><?php _e('Text', 'q-knowledge-base'); ?></option>
                                    <option value="file"><?php _e('File Upload', 'q-knowledge-base'); ?></option>
                                    <option value="url"><?php _e('URL', 'q-knowledge-base'); ?></option>
                                </select>
                            </div>

                            <div class="qi-kb-form-group kb-content kb-content-text" style="display: none;">
                                <label for="kb-text"><?php _e('Content', 'q-knowledge-base'); ?></label>
                                <textarea id="kb-text" name="content" rows="6"></textarea>
                            </div>

                            <div class="qi-kb-form-group kb-content kb-content-file" style="display: none;">
                                <label for="kb-file"><?php _e('Upload File', 'q-knowledge-base'); ?></label>
                                <div class="file-input-wrapper">
                                    <input type="file" id="kb-file" name="file" accept=".pdf,.doc,.docx,.txt"
                                        class="file-input">
                                    <label for="kb-file" class="file-input-label">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <?php _e('Choose a file', 'q-knowledge-base'); ?>
                                    </label>
                                    <div class="file-name"></div>
                                    <div class="upload-progress">
                                        <div class="upload-progress-bar"></div>
                                    </div>
                                    <div class="upload-status"></div>
                                </div>
                                <p class="description">
                                    <?php _e('Drag & drop a file here or click to browse', 'q-knowledge-base'); ?>
                                </p>
                                <p class="file-formats">
                                    <?php _e('Supported formats: PDF, DOC, DOCX, TXT', 'q-knowledge-base'); ?>
                                </p>
                            </div>

                            <div class="qi-kb-form-group kb-content kb-content-url" style="display: none;">
                                <label for="kb-url"><?php _e('URL', 'q-knowledge-base'); ?></label>
                                <input type="url" id="kb-url" name="url" placeholder="https://">
                            </div>

                            <div class="qi-kb-form-group">
                                <label for="kb-assistant"><?php _e('Assign to assistant', 'q-knowledge-base'); ?></label>
                                <select id="kb-assistant" name="assistant_id" required>
                                    <option value=""><?php _e('Select assistant', 'q-knowledge-base'); ?></option>
                                    <?php foreach ($all_assistants as $assistant): ?>
                                        <option value="<?php echo esc_attr($assistant->term_id); ?>">
                                            <?php echo esc_html($assistant->name); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="qi-kb-form-actions">
                                <button type="button" class="qi-kb-cancel">
                                    <?php _e('Cancel', 'q-knowledge-base'); ?>
                                </button>
                                <button type="submit" class="qi-kb-submit">
                                    <?php _e('Submit', 'q-knowledge-base'); ?>
                                </button>
                            </div>
                        </form>
                    </div>
                    <!-- Manage Knowledge Tab -->
                    <div class="qi-kb-tab-content qi-manage-knowledge-content" style="display: none;">
                        <div class="qi-kb-modal-header">
                            <h2><?php _e('Manage Knowledge', 'q-knowledge-base'); ?></h2>
                        </div>
                        <div class="qi-manage-knowledge-list">
                            <div class="qi-no-knowledge">
                                <div class="qi-no-knowledge-icon">
                                    <i class="fas fa-book"></i>
                                </div>
                                <p><?php _e('No knowledge entries added yet.', 'q-knowledge-base'); ?></p>
                                <p class="qi-no-knowledge-desc">
                                    <?php _e('Click the "Add Knowledge" tab to start adding content.', 'q-knowledge-base'); ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Get initialization script
     *
     * @return string JavaScript initialization code
     */
    private function get_initialization_script()
    {
        return <<<HTML
        <script>
            document.addEventListener('DOMContentLoaded', () => {

                const chatContainer = document.querySelector('.qi-chat-container');
                const systemDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

                // Character counter initialization
                const textarea = document.querySelector('.qkb-input-textarea');
                const counter = document.querySelector('.qi-current-count');
                if (textarea && counter) {
                    const maxLength = parseInt(textarea.getAttribute('maxlength')) || 500;

                    function updateCounter() {
                        const remaining = maxLength - textarea.value.length;
                        counter.textContent = remaining;

                        const container = counter.parentElement;
                        if (remaining <= 50) {
                            container.classList.add('qi-limit-warning');
                        } else {
                            container.classList.remove('qi-limit-warning');
                        }

                        if (remaining <= 10) {
                            container.classList.add('qi-limit-exceeded');
                        } else {
                            container.classList.remove('qi-limit-exceeded');
                        }
                    }

                    textarea.addEventListener('input', updateCounter);
                    updateCounter(); // Initial count
                }

                // Suggested prompts functionality
                const promptButtons = document.querySelectorAll('.qi-prompt-button');
                promptButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        if (textarea) {
                            textarea.value = button.textContent.trim();
                            textarea.focus();

                            // Trigger input event to update character count
                            const inputEvent = new Event('input', { bubbles: true });
                            textarea.dispatchEvent(inputEvent);

                            // Enable send button if needed
                            const sendButton = document.querySelector('.qi-send-button');
                            if (sendButton && textarea.value.trim() !== '') {
                                sendButton.disabled = false;
                            }
                        }
                    });
                });

                // Scroll to bottom button functionality
                const messagesContainer = document.querySelector('.qi-chat-messages');
                const scrollButton = document.querySelector('.qi-scroll-bottom');

                if (messagesContainer && scrollButton) {
                    function toggleScrollButton() {
                        const scrollHeight = messagesContainer.scrollHeight;
                        const scrollTop = messagesContainer.scrollTop;
                        const clientHeight = messagesContainer.clientHeight;

                        // Show button if not at bottom (with a small threshold)
                        if (scrollHeight - scrollTop - clientHeight > 50) {
                            scrollButton.classList.add('visible');
                        } else {
                            scrollButton.classList.remove('visible');
                        }
                    }

                    messagesContainer.addEventListener('scroll', toggleScrollButton);

                    scrollButton.addEventListener('click', () => {
                        messagesContainer.scrollTo({
                            top: messagesContainer.scrollHeight,
                            behavior: 'smooth'
                        });
                    });

                    // Check on new messages
                    const observer = new MutationObserver(toggleScrollButton);
                    observer.observe(messagesContainer, { childList: true, subtree: true });
                }

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    // Ctrl/Cmd + / to focus the textarea
                    if ((e.ctrlKey || e.metaKey) && e.key === '/') {
                        e.preventDefault();
                        if (textarea) {
                            textarea.focus();
                        }
                    }

                    // Escape to close modals or minimize chat
                    if (e.key === 'Escape') {
                        // Check if any modal is open
                        const openModal = document.querySelector('.qi-kb-modal[style*="display: block"]');
                        if (openModal) {
                            const closeButton = openModal.querySelector('.qi-kb-modal-close');
                            if (closeButton) {
                                closeButton.click();
                            }
                        } else {
                            const minimizeButton = document.querySelector('.qi-toggle-fullscreen');
                            if (minimizeButton) {
                                minimizeButton.click();
                            }
                        }
                    }
                });
            });
        </script>
HTML;
    }
}
