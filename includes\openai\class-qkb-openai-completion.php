<?php
if (!defined('ABSPATH')) {
    exit;
}

class QKB_OpenAI_Completion extends QKB_OpenAI_Base
{
    private $ml_patterns = [];
    private $prompts_handler;
    private $context_handler;

    public function __construct()
    {
        parent::__construct();
        $this->prompts_handler = new QKB_OpenAI_Prompts();
        $this->context_handler = new QKB_OpenAI_Context();
    }

    private function handle_error($message, $exception)
    {
        if ($this->error_handler && method_exists($this->error_handler, 'log_error')) {
            $this->error_handler->log_error($message, $exception);
        } else {
            error_log("QKB Error: {$message}");
            if ($exception) {
                error_log("Exception: " . $exception->getMessage());
            }
        }

        $error_message = 'An error occurred while processing your request.';

        if (strpos($exception->getMessage(), 'cURL error 28') !== false) {
            $error_message = 'The request timed out. Please try again or rephrase your question.';
        } elseif (strpos($exception->getMessage(), 'No knowledge base categories found') !== false) {
            $error_message = 'The knowledge base is still being set up. Please try again later.';
        }

        return new WP_Error('completion_error', $error_message);
    }

    public function get_completion($message, $context = '', $assistant_id = null, $conversation_context = [])
    {
        if (!$this->api_key) {
            return new WP_Error('no_api_key', 'API key not configured.');
        }

        // Enhanced cache key with conversation context hash
        $context_hash = !empty($conversation_context) ? md5(serialize($conversation_context)) : '';
        $cache_key = 'completion_' . md5($message . '_' . ($assistant_id ? $assistant_id : '0') . '_' . $context_hash);
        $cached = $this->cache->get($cache_key);
        if ($cached) {
            return $cached;
        }

        try {
            // Start performance timer
            $start_time = microtime(true);

            // Get enriched context with performance optimizations
            $enriched_context = $this->context_handler->get_enriched_context($message, [
                'assistant_id' => $assistant_id,
                'include_user_context' => false, // Disable for faster response
                'include_conversation_history' => false, // We handle this separately
                'timeout' => 3, // Reduced timeout
                'max_results' => 3 // Limit results for faster processing
            ]);

            $is_generation_request = $this->detect_generation_request($message);

            // Build optimized messages array
            $messages = $this->build_optimized_messages($assistant_id, $enriched_context, $conversation_context, $message);

            // Get response format settings
            $response_format = get_option('qkb_response_format', 'markdown');
            $response_format_param = [];

            if ($response_format === 'json') {
                $response_format_param = [
                    'response_format' => ['type' => 'json_object']
                ];
            }

            // Build optimized request parameters
            $request_params = [
                'model' => $this->model,
                'messages' => $messages,
                'temperature' => $is_generation_request ? 0.7 : min($this->temperature, 0.5), // Cap temperature for consistency
                'max_tokens' => min($this->max_tokens, 800), // Limit tokens for faster response
                'presence_penalty' => $is_generation_request ? 0.3 : $this->presence_penalty,
                'frequency_penalty' => $is_generation_request ? 0.3 : $this->frequency_penalty,
                'stream' => false // Ensure no streaming
            ];

            // Add response format if specified
            if (!empty($response_format_param)) {
                $request_params = array_merge($request_params, $response_format_param);
            }

            // Make optimized request with retry logic
            $response = $this->make_optimized_request('chat/completions', $request_params);

            if (is_wp_error($response)) {
                throw new Exception($response->get_error_message());
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);
            if (!isset($body['choices'][0]['message']['content'])) {
                throw new Exception('Invalid API response format');
            }

            $content = trim($body['choices'][0]['message']['content']);

            // Cache with performance-based expiration
            $cache_expiration = $is_generation_request ? 1800 : 3600; // 30 min for generation, 1 hour for regular
            $this->cache->set_performance($cache_key, $content);

            // Log performance metrics
            $execution_time = microtime(true) - $start_time;
            if ($execution_time > 5) {
                error_log("Slow OpenAI completion: {$execution_time} seconds for message: " . substr($message, 0, 100));
            }

            return $content;

        } catch (Exception $e) {
            return $this->handle_error('Completion error', $e);
        }
    }

    /**
     * Build optimized messages array with length limits
     */
    private function build_optimized_messages($assistant_id, $enriched_context, $conversation_context, $message)
    {
        $messages = [
            $this->prompts_handler->get_system_instructions($assistant_id)
        ];

        // Add context if available (with length limit)
        if (!empty($enriched_context)) {
            $context_content = is_string($enriched_context) ? $enriched_context : (string) $enriched_context;
            if (strlen($context_content) > 1500) {
                $context_content = substr($context_content, 0, 1500) . '...';
            }
            $messages[] = [
                'role' => 'system',
                'content' => $context_content
            ];
        }

        // Add conversation context (limit to last 2 exchanges for performance)
        if (!empty($conversation_context) && isset($conversation_context['history'])) {
            $recent_history = array_slice($conversation_context['history'], -2);
            foreach ($recent_history as $exchange) {
                if (isset($exchange['user'])) {
                    $messages[] = [
                        'role' => 'user',
                        'content' => substr($exchange['user'], 0, 300) // Limit length
                    ];
                }
                if (isset($exchange['bot'])) {
                    $messages[] = [
                        'role' => 'assistant',
                        'content' => substr($exchange['bot'], 0, 300) // Limit length
                    ];
                }
            }
        }

        // Add current user message
        $messages[] = [
            'role' => 'user',
            'content' => $message
        ];

        // Skip ML patterns for performance unless specifically needed
        if (!empty($this->ml_patterns) && $this->should_include_ml_patterns($message)) {
            $messages[] = [
                'role' => 'system',
                'content' => "Previous patterns:\n" . $this->format_ml_patterns(array_slice($this->ml_patterns, 0, 3))
            ];
        }

        return $messages;
    }

    /**
     * Make optimized request with intelligent retry
     */
    private function make_optimized_request($endpoint, $data)
    {
        // First attempt with aggressive timeout
        $response = $this->make_request($endpoint, $data, 8);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();

            // Only retry for timeout errors
            if (strpos($error_message, 'cURL error 28') !== false || strpos($error_message, 'timeout') !== false) {
                error_log('OpenAI request timed out, retrying with optimized parameters');

                // Optimize parameters for retry
                $data['max_tokens'] = min($data['max_tokens'], 400);
                $data['temperature'] = min($data['temperature'], 0.3);

                // Remove any optional parameters that might slow down the request
                unset($data['presence_penalty']);
                unset($data['frequency_penalty']);

                $response = $this->make_request($endpoint, $data, 15);
            }
        }

        return $response;
    }

    /**
     * Check if ML patterns should be included
     */
    private function should_include_ml_patterns($message)
    {
        // Only include ML patterns for complex queries
        return strlen($message) > 50 && (
            strpos(strtolower($message), 'complex') !== false ||
            strpos(strtolower($message), 'detailed') !== false ||
            strpos(strtolower($message), 'explain') !== false
        );
    }

    private function get_completion_with_citations($messages)
    {
        try {
            $response = $this->make_request('chat/completions', [
                'model' => $this->model,
                'messages' => $messages,
                'temperature' => 0.3,
                'max_tokens' => $this->max_tokens
            ]);

            if (is_wp_error($response)) {
                throw new Exception($response->get_error_message());
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);
            return $body['choices'][0]['message']['content'];

        } catch (Exception $e) {
            $this->error_handler->log_error('Citation completion error', $e);
            return $messages[count($messages) - 2]['content'];
        }
    }

    private function detect_generation_request($message)
    {
        $generation_keywords = [
            'create',
            'generate',
            'make',
            'write',
            'summarize',
            'explain',
            'list',
            'faqs',
            'questions',
            'summary'
        ];

        $message_lower = strtolower($message);
        foreach ($generation_keywords as $keyword) {
            if (strpos($message_lower, $keyword) !== false) {
                return true;
            }
        }
        return false;
    }

    private function format_ml_patterns($patterns)
    {
        return implode("\n", array_map(function ($pattern) {
            return sprintf(
                "- Pattern: %s\n  Response: %s\n  Confidence: %.2f",
                $pattern['pattern'],
                $pattern['response'],
                $pattern['confidence']
            );
        }, $patterns));
    }
}
