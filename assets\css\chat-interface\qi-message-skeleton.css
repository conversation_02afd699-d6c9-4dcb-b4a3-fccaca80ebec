.skeleton-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 800px;
    padding: var(--qkb-padding);
    gap: var(--qkb-gap-md);
    opacity: 0.8;
}

.skeleton-line {
    height: 12px;
    background: linear-gradient(to right,
            var(--qkb-border) 0%,
            var(--qkb-primary-light) 50%,
            var(--qkb-border) 100%);
    border-radius: var(--qkb-radius-sm);
    margin: var(--qkb-gap-sm) 0;
    position: relative;
    overflow: hidden;
    transform-origin: left;
    animation: qipulse 2s infinite ease-in-out;
}

.skeleton-line::after {
    content: "";
    position: absolute;
    inset: 0;
    background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.3),
            transparent);
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
}

.skeleton-line.xs {
    width: 15%;
}

.skeleton-line.sm {
    width: 25%;
}

.skeleton-line.md {
    width: 50%;
}

.skeleton-line.lg {
    width: 75%;
}

.skeleton-line.xl {
    width: 90%;
}

.skeleton-line.full {
    width: 100%;
}

.skeleton-line:nth-child(4n) {
    width: 45%;
}

.skeleton-line:nth-child(3n) {
    width: 65%;
}

.skeleton-line:nth-child(7n) {
    width: 85%;
}