jQuery(document).ready(function($) {
    $('.qkb-generate-all-embeddings').on('click', function() {
        const button = $(this);
        button.prop('disabled', true);
        button.find('.dashicons').addClass('dashicons-update-spin');
        
        $('.qkb-embeddings-status').show();
        $('.qkb-embeddings-results').hide();
        
        // Call AJAX to generate all embeddings
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'qkb_generate_all_embeddings',
                nonce: qkb_embeddings.nonce
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    $('.qkb-progress-bar-inner').css('width', '100%');
                    $('.qkb-processed').text(data.success);
                    $('.qkb-total').text(data.total);
                    
                    // Show results
                    $('.qkb-results-summary').html(
                        'Processed <strong>' + data.processed + '</strong> knowledge base articles. ' +
                        'Successfully generated <strong>' + data.success + '</strong> embeddings.'
                    );
                    
                    // Show errors if any
                    if (data.errors && data.errors.length > 0) {
                        $('.qkb-results-errors').show();
                        const errorList = $('.qkb-error-list');
                        errorList.empty();
                        
                        for (let i = 0; i < data.errors.length; i++) {
                            const error = data.errors[i];
                            errorList.append('<li>' + error.title + ' - ' + error.error + '</li>');
                        }
                    } else {
                        $('.qkb-results-errors').hide();
                    }
                    
                    $('.qkb-embeddings-results').show();
                } else {
                    alert('Error: ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error generating embeddings. Please try again.');
            },
            complete: function() {
                button.prop('disabled', false);
                button.find('.dashicons').removeClass('dashicons-update-spin');
            }
        });
    });
});
