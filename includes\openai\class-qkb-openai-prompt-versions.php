<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for handling prompt versioning
 */
class QKB_OpenAI_Prompt_Versions {
    private $db;
    private $versions_table;

    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->db = $wpdb;
        $this->versions_table = $wpdb->prefix . 'qkb_prompt_versions';

        // Create tables if they don't exist
        $this->create_tables();

        // Add hooks
        add_action('admin_menu', [$this, 'add_versions_page']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_scripts']);
        add_action('wp_ajax_qkb_get_prompt_versions', [$this, 'ajax_get_versions']);
        add_action('wp_ajax_qkb_restore_prompt_version', [$this, 'ajax_restore_version']);
        add_action('wp_ajax_qkb_compare_prompt_versions', [$this, 'ajax_compare_versions']);
    }

    /**
     * Create database tables
     */
    public function create_tables() {
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        $charset_collate = $this->db->get_charset_collate();

        // Versions table
        $sql = "CREATE TABLE IF NOT EXISTS {$this->versions_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            prompt_id varchar(255) NOT NULL,
            prompt_type varchar(50) NOT NULL,
            version int(11) NOT NULL,
            content text NOT NULL,
            created_by bigint(20) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            comment text,
            PRIMARY KEY (id),
            UNIQUE KEY prompt_version (prompt_id, prompt_type, version),
            KEY prompt_id (prompt_id),
            KEY prompt_type (prompt_type),
            KEY version (version)
        ) $charset_collate;";

        dbDelta($sql);
    }

    /**
     * Add versions page to admin menu
     */
    public function add_versions_page() {
        add_submenu_page(
            'edit.php?post_type=kb_knowledge_base',
            __('Prompt Versions', 'q-knowledge-base'),
            __('Prompt Versions', 'q-knowledge-base'),
            'manage_options',
            'qkb-prompt-versions',
            [$this, 'render_versions_page']
        );
    }

    /**
     * Enqueue scripts for versions page
     */
    public function enqueue_scripts($hook) {
        if ($hook !== 'kb_knowledge_base_page_qkb-prompt-versions') {
            return;
        }

        wp_enqueue_style('qkb-admin-css', plugin_dir_url(QKB_PLUGIN_FILE) . 'assets/css/admin.css', [], QKB_VERSION);
        wp_enqueue_script('diff-match-patch', 'https://cdnjs.cloudflare.com/ajax/libs/diff-match-patch/20121119/diff_match_patch.js', [], '20121119', true);
        wp_enqueue_script('qkb-prompt-versioning', plugin_dir_url(QKB_PLUGIN_FILE) . 'assets/js/prompt-versioning.js', ['jquery', 'diff-match-patch'], QKB_VERSION, true);

        wp_localize_script('qkb-prompt-versioning', 'qkbPromptVersions', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('qkb_prompt_versions_nonce'),
            'errorMessage' => __('An error occurred while fetching version data.', 'q-knowledge-base'),
            'confirmRestore' => __('Are you sure you want to restore this version? This will overwrite the current prompt.', 'q-knowledge-base'),
            'restoreSuccess' => __('Version restored successfully.', 'q-knowledge-base'),
            'restoreError' => __('Failed to restore version.', 'q-knowledge-base')
        ]);
    }

    /**
     * Render versions page
     */
    public function render_versions_page() {
        ?>
        <div class="wrap qkb-versions-page">
            <h1><?php _e('Prompt Versions', 'q-knowledge-base'); ?></h1>

            <div class="qkb-versions-filters">
                <select id="qkb-prompt-type-filter">
                    <option value="all"><?php _e('All Prompt Types', 'q-knowledge-base'); ?></option>
                    <option value="system"><?php _e('System Prompts', 'q-knowledge-base'); ?></option>
                    <option value="assistant"><?php _e('Assistant Prompts', 'q-knowledge-base'); ?></option>
                    <option value="template"><?php _e('Template Prompts', 'q-knowledge-base'); ?></option>
                </select>

                <select id="qkb-prompt-id-filter">
                    <option value="all"><?php _e('All Prompts', 'q-knowledge-base'); ?></option>
                    <?php $this->render_prompt_options(); ?>
                </select>

                <button id="qkb-refresh-versions" class="button button-primary">
                    <i class="dashicons dashicons-update"></i> <?php _e('Refresh', 'q-knowledge-base'); ?>
                </button>
            </div>

            <div class="qkb-versions-container">
                <div class="qkb-versions-list">
                    <h2><?php _e('Version History', 'q-knowledge-base'); ?></h2>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Version', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Prompt', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Type', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Created By', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Date', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Actions', 'q-knowledge-base'); ?></th>
                            </tr>
                        </thead>
                        <tbody id="qkb-versions-table">
                            <tr>
                                <td colspan="6"><?php _e('Loading data...', 'q-knowledge-base'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="qkb-version-details" style="display: none;">
                    <h2><?php _e('Version Details', 'q-knowledge-base'); ?></h2>
                    <div class="qkb-version-info">
                        <div class="qkb-version-header">
                            <span class="qkb-version-title"></span>
                            <div class="qkb-version-actions">
                                <button id="qkb-restore-version" class="button button-primary">
                                    <i class="dashicons dashicons-backup"></i> <?php _e('Restore This Version', 'q-knowledge-base'); ?>
                                </button>
                                <button id="qkb-compare-versions" class="button">
                                    <i class="dashicons dashicons-visibility"></i> <?php _e('Compare With Current', 'q-knowledge-base'); ?>
                                </button>
                                <button id="qkb-close-details" class="button">
                                    <i class="dashicons dashicons-no"></i> <?php _e('Close', 'q-knowledge-base'); ?>
                                </button>
                            </div>
                        </div>
                        <div class="qkb-version-meta">
                            <span class="qkb-version-created-by"></span>
                            <span class="qkb-version-date"></span>
                        </div>
                        <div class="qkb-version-comment"></div>
                    </div>
                    <div class="qkb-version-content">
                        <pre id="qkb-version-content-display"></pre>
                    </div>
                </div>

                <div class="qkb-version-comparison" style="display: none;">
                    <h2><?php _e('Version Comparison', 'q-knowledge-base'); ?></h2>
                    <div class="qkb-comparison-header">
                        <div class="qkb-comparison-title"><?php _e('Comparing versions', 'q-knowledge-base'); ?></div>
                        <button id="qkb-close-comparison" class="button">
                            <i class="dashicons dashicons-no"></i> <?php _e('Close', 'q-knowledge-base'); ?>
                        </button>
                    </div>
                    <div class="qkb-comparison-content">
                        <div class="qkb-comparison-old">
                            <h3><?php _e('Selected Version', 'q-knowledge-base'); ?></h3>
                            <pre id="qkb-comparison-old-content"></pre>
                        </div>
                        <div class="qkb-comparison-new">
                            <h3><?php _e('Current Version', 'q-knowledge-base'); ?></h3>
                            <pre id="qkb-comparison-new-content"></pre>
                        </div>
                        <div class="qkb-comparison-diff">
                            <h3><?php _e('Differences', 'q-knowledge-base'); ?></h3>
                            <div id="qkb-comparison-diff-content"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render prompt options for select dropdown
     */
    private function render_prompt_options() {
        // Get assistants
        $assistants = get_terms([
            'taxonomy' => 'kb_assistant',
            'hide_empty' => false
        ]);

        if (!empty($assistants) && !is_wp_error($assistants)) {
            echo '<optgroup label="' . __('Assistants', 'q-knowledge-base') . '">';
            foreach ($assistants as $assistant) {
                echo '<option value="assistant-' . esc_attr($assistant->term_id) . '">' . esc_html($assistant->name) . '</option>';
            }
            echo '</optgroup>';
        }

        // Get templates
        $templates_handler = new QKB_OpenAI_Prompt_Templates();
        $templates = $templates_handler->get_templates(true); // Force refresh to get latest templates

        if (!empty($templates)) {
            echo '<optgroup label="' . __('Templates', 'q-knowledge-base') . '">';
            foreach ($templates as $template) {
                echo '<option value="template-' . esc_attr($template['id']) . '">' . esc_html($template['name']) . '</option>';
            }
            echo '</optgroup>';
        }

        // System prompt
        echo '<optgroup label="' . __('System', 'q-knowledge-base') . '">';
        echo '<option value="system-default">' . __('Default System Prompt', 'q-knowledge-base') . '</option>';
        echo '</optgroup>';
    }

    /**
     * AJAX handler for getting prompt versions
     */
    public function ajax_get_versions() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_prompt_versions_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'q-knowledge-base')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have sufficient permissions', 'q-knowledge-base')]);
        }

        // Get filter parameters
        $prompt_type = isset($_POST['prompt_type']) ? sanitize_text_field($_POST['prompt_type']) : 'all';
        $prompt_id = isset($_POST['prompt_id']) ? sanitize_text_field($_POST['prompt_id']) : 'all';

        // Parse prompt ID if it's in format "type-id"
        if ($prompt_id !== 'all' && strpos($prompt_id, '-') !== false) {
            list($type, $id) = explode('-', $prompt_id, 2);
            $prompt_type = $type;
            $prompt_id = $id;
        }

        // Get versions
        $versions = $this->get_prompt_versions($prompt_type, $prompt_id);

        wp_send_json_success([
            'versions' => $versions
        ]);
    }

    /**
     * AJAX handler for restoring a prompt version
     */
    public function ajax_restore_version() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_prompt_versions_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'q-knowledge-base')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have sufficient permissions', 'q-knowledge-base')]);
        }

        // Get parameters
        $version_id = isset($_POST['version_id']) ? intval($_POST['version_id']) : 0;

        if ($version_id <= 0) {
            wp_send_json_error(['message' => __('Invalid version ID', 'q-knowledge-base')]);
        }

        // Get version data
        $version = $this->get_version($version_id);

        if (!$version) {
            wp_send_json_error(['message' => __('Version not found', 'q-knowledge-base')]);
        }

        // Restore version
        $result = $this->restore_version($version);

        if (is_wp_error($result)) {
            wp_send_json_error(['message' => $result->get_error_message()]);
        }

        wp_send_json_success([
            'message' => __('Version restored successfully', 'q-knowledge-base')
        ]);
    }

    /**
     * AJAX handler for comparing prompt versions
     */
    public function ajax_compare_versions() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_prompt_versions_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'q-knowledge-base')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have sufficient permissions', 'q-knowledge-base')]);
        }

        // Get parameters
        $version_id = isset($_POST['version_id']) ? intval($_POST['version_id']) : 0;

        if ($version_id <= 0) {
            wp_send_json_error(['message' => __('Invalid version ID', 'q-knowledge-base')]);
        }

        // Get version data
        $version = $this->get_version($version_id);

        if (!$version) {
            wp_send_json_error(['message' => __('Version not found', 'q-knowledge-base')]);
        }

        // Get current prompt content
        $current_content = $this->get_current_prompt_content($version->prompt_id, $version->prompt_type);

        if (is_wp_error($current_content)) {
            wp_send_json_error(['message' => $current_content->get_error_message()]);
        }

        wp_send_json_success([
            'old_content' => $version->content,
            'new_content' => $current_content
        ]);
    }

    /**
     * Save a new prompt version
     *
     * @param string $prompt_id Prompt ID
     * @param string $prompt_type Prompt type
     * @param string $content Prompt content
     * @param string $comment Optional comment
     * @return int|WP_Error Version ID or error
     */
    public function save_version($prompt_id, $prompt_type, $content, $comment = '') {
        // Get the latest version number
        $latest_version = $this->get_latest_version_number($prompt_id, $prompt_type);
        $new_version = $latest_version + 1;

        // Insert new version
        $result = $this->db->insert(
            $this->versions_table,
            [
                'prompt_id' => $prompt_id,
                'prompt_type' => $prompt_type,
                'version' => $new_version,
                'content' => $content,
                'created_by' => get_current_user_id(),
                'created_at' => current_time('mysql'),
                'comment' => $comment
            ],
            [
                '%s', '%s', '%d', '%s', '%d', '%s', '%s'
            ]
        );

        if (!$result) {
            return new WP_Error('db_error', __('Failed to save version', 'q-knowledge-base'));
        }

        return $this->db->insert_id;
    }

    /**
     * Get prompt versions
     *
     * @param string $prompt_type Prompt type filter
     * @param string $prompt_id Prompt ID filter
     * @return array Versions data
     */
    private function get_prompt_versions($prompt_type = 'all', $prompt_id = 'all') {
        $where = [];
        $where_sql = '';

        if ($prompt_type !== 'all') {
            $where[] = $this->db->prepare("prompt_type = %s", $prompt_type);
        }

        if ($prompt_id !== 'all') {
            $where[] = $this->db->prepare("prompt_id = %s", $prompt_id);
        }

        if (!empty($where)) {
            $where_sql = "WHERE " . implode(" AND ", $where);
        }

        $query = "SELECT
                    v.*,
                    u.display_name as created_by_name
                  FROM {$this->versions_table} v
                  LEFT JOIN {$this->db->users} u ON v.created_by = u.ID
                  {$where_sql}
                  ORDER BY v.prompt_id, v.prompt_type, v.version DESC
                  LIMIT 100";

        $results = $this->db->get_results($query);

        $versions = [];

        foreach ($results as $row) {
            $prompt_name = $this->get_prompt_name($row->prompt_id, $row->prompt_type);

            $versions[] = [
                'id' => $row->id,
                'prompt_id' => $row->prompt_id,
                'prompt_type' => $row->prompt_type,
                'prompt_name' => $prompt_name,
                'version' => $row->version,
                'content' => $row->content,
                'created_by' => $row->created_by,
                'created_by_name' => $row->created_by_name ?: __('Unknown', 'q-knowledge-base'),
                'created_at' => $row->created_at,
                'comment' => $row->comment
            ];
        }

        return $versions;
    }

    /**
     * Get a specific version by ID
     *
     * @param int $version_id Version ID
     * @return object|null Version data or null if not found
     */
    private function get_version($version_id) {
        return $this->db->get_row(
            $this->db->prepare(
                "SELECT * FROM {$this->versions_table} WHERE id = %d",
                $version_id
            )
        );
    }

    /**
     * Get the latest version number for a prompt
     *
     * @param string $prompt_id Prompt ID
     * @param string $prompt_type Prompt type
     * @return int Latest version number
     */
    private function get_latest_version_number($prompt_id, $prompt_type) {
        $result = $this->db->get_var(
            $this->db->prepare(
                "SELECT MAX(version) FROM {$this->versions_table} WHERE prompt_id = %s AND prompt_type = %s",
                $prompt_id, $prompt_type
            )
        );

        return intval($result) ?: 0;
    }

    /**
     * Restore a prompt version
     *
     * @param object $version Version data
     * @return bool|WP_Error True on success or error
     */
    private function restore_version($version) {
        switch ($version->prompt_type) {
            case 'assistant':
                return $this->restore_assistant_prompt($version->prompt_id, $version->content);

            case 'template':
                return $this->restore_template_prompt($version->prompt_id, $version->content);

            case 'system':
                return $this->restore_system_prompt($version->content);

            default:
                return new WP_Error('invalid_type', __('Invalid prompt type', 'q-knowledge-base'));
        }
    }

    /**
     * Restore an assistant prompt
     *
     * @param string $assistant_id Assistant ID
     * @param string $content Prompt content
     * @return bool|WP_Error True on success or error
     */
    private function restore_assistant_prompt($assistant_id, $content) {
        $result = update_term_meta($assistant_id, 'assistant_prompt', $content);

        if (false === $result) {
            return new WP_Error('update_failed', __('Failed to update assistant prompt', 'q-knowledge-base'));
        }

        return true;
    }

    /**
     * Restore a template prompt
     *
     * @param string $template_id Template ID
     * @param string $content Prompt content
     * @return bool|WP_Error True on success or error
     */
    private function restore_template_prompt($template_id, $content) {
        $templates_handler = new QKB_OpenAI_Prompt_Templates();
        $templates = $templates_handler->get_templates();

        foreach ($templates as $key => $template) {
            if ($template['id'] == $template_id) {
                $templates[$key]['content'] = $content;
                break;
            }
        }

        $result = update_option('qkb_prompt_templates', $templates);

        if (false === $result) {
            return new WP_Error('update_failed', __('Failed to update template prompt', 'q-knowledge-base'));
        }

        return true;
    }

    /**
     * Restore the system prompt
     *
     * @param string $content Prompt content
     * @return bool|WP_Error True on success or error
     */
    private function restore_system_prompt($content) {
        $result = update_option('qkb_system_instructions', $content);

        if (false === $result) {
            return new WP_Error('update_failed', __('Failed to update system prompt', 'q-knowledge-base'));
        }

        return true;
    }

    /**
     * Get current prompt content
     *
     * @param string $prompt_id Prompt ID
     * @param string $prompt_type Prompt type
     * @return string|WP_Error Current content or error
     */
    private function get_current_prompt_content($prompt_id, $prompt_type) {
        switch ($prompt_type) {
            case 'assistant':
                $content = get_term_meta($prompt_id, 'assistant_prompt', true);
                return $content ?: '';

            case 'template':
                $templates_handler = new QKB_OpenAI_Prompt_Templates();
                $template = $templates_handler->get_template($prompt_id);
                return $template ? $template['content'] : '';

            case 'system':
                return get_option('qkb_system_instructions', '');

            default:
                return new WP_Error('invalid_type', __('Invalid prompt type', 'q-knowledge-base'));
        }
    }

    /**
     * Get prompt name from ID and type
     *
     * @param string $prompt_id Prompt ID
     * @param string $prompt_type Prompt type
     * @return string Prompt name
     */
    private function get_prompt_name($prompt_id, $prompt_type) {
        switch ($prompt_type) {
            case 'assistant':
                $assistant = get_term($prompt_id, 'kb_assistant');
                if ($assistant && !is_wp_error($assistant)) {
                    // Handle both object and array formats for assistant
                    return is_object($assistant) ? $assistant->name : (isset($assistant['name']) ? $assistant['name'] : __('Unknown Assistant', 'q-knowledge-base'));
                }
                return __('Unknown Assistant', 'q-knowledge-base');

            case 'template':
                $templates_handler = new QKB_OpenAI_Prompt_Templates();
                $template = $templates_handler->get_template($prompt_id);
                return $template ? $template['name'] : __('Unknown Template', 'q-knowledge-base');

            case 'system':
                return __('System Prompt', 'q-knowledge-base');

            default:
                return __('Unknown Prompt', 'q-knowledge-base');
        }
    }
}

// Initialize the class
add_action('plugins_loaded', function() {
    new QKB_OpenAI_Prompt_Versions();
});
