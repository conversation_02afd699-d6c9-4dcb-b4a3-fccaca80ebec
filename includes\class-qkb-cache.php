<?php

class QKB_Cache
{
    private $cache = [];
    private $cache_prefix = 'qkb_cache_';
    private $default_expiration = 3600; // 1 hour
    private $performance_cache_expiration = 300; // 5 minutes for performance-critical data

    public function get($key)
    {
        // First check in-memory cache
        if (isset($this->cache[$key])) {
            return $this->cache[$key];
        }

        // Then check WordPress transients for persistent cache
        $transient_key = $this->cache_prefix . md5($key);
        $cached_data = get_transient($transient_key);

        if ($cached_data !== false) {
            // Store in memory cache for faster subsequent access
            $this->cache[$key] = $cached_data;
            return $cached_data;
        }

        return false;
    }

    public function set($key, $value, $expiration = null)
    {
        if ($expiration === null) {
            $expiration = $this->default_expiration;
        }

        // Store in memory cache
        $this->cache[$key] = $value;

        // Store in WordPress transients for persistence
        $transient_key = $this->cache_prefix . md5($key);
        set_transient($transient_key, $value, $expiration);
    }

    public function delete($key)
    {
        // Remove from memory cache
        if (isset($this->cache[$key])) {
            unset($this->cache[$key]);
        }

        // Remove from WordPress transients
        $transient_key = $this->cache_prefix . md5($key);
        delete_transient($transient_key);
    }

    public function set_ml_pattern($key, $value, $metadata = [])
    {
        $cache_data = [
            'value' => $value,
            'metadata' => array_merge($metadata, [
                'timestamp' => time(),
                'usage_count' => 0,
                'success_rate' => 0
            ])
        ];

        $this->set($key, $cache_data, $this->default_expiration);
        $this->update_pattern_statistics($key);
    }

    /**
     * Set cache with performance-optimized expiration
     */
    public function set_performance($key, $value)
    {
        $this->set($key, $value, $this->performance_cache_expiration);
    }

    /**
     * Get multiple cache keys at once
     */
    public function get_multiple($keys)
    {
        $results = [];
        foreach ($keys as $key) {
            $results[$key] = $this->get($key);
        }
        return $results;
    }

    /**
     * Set multiple cache keys at once
     */
    public function set_multiple($data, $expiration = null)
    {
        foreach ($data as $key => $value) {
            $this->set($key, $value, $expiration);
        }
    }

    /**
     * Clear all cache entries with our prefix
     */
    public function clear_all()
    {
        global $wpdb;

        // Clear memory cache
        $this->cache = [];

        // Clear WordPress transients
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
                '_transient_' . $this->cache_prefix . '%',
                '_transient_timeout_' . $this->cache_prefix . '%'
            )
        );
    }

    /**
     * Get cache statistics
     */
    public function get_stats()
    {
        global $wpdb;

        $transient_count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_' . $this->cache_prefix . '%'
            )
        );

        return [
            'memory_cache_count' => count($this->cache),
            'persistent_cache_count' => $transient_count,
            'cache_prefix' => $this->cache_prefix
        ];
    }

    /**
     * Update pattern statistics for ML patterns
     */
    private function update_pattern_statistics($key)
    {
        // This method can be implemented to track ML pattern usage
        // For now, it's a placeholder to maintain compatibility
        do_action('qkb_cache_pattern_updated', $key);
    }

    /**
     * Check if cache key exists
     */
    public function exists($key)
    {
        return $this->get($key) !== false;
    }

    /**
     * Increment a cached counter
     */
    public function increment($key, $step = 1, $expiration = null)
    {
        $current = $this->get($key);
        $new_value = ($current !== false ? intval($current) : 0) + $step;
        $this->set($key, $new_value, $expiration);
        return $new_value;
    }

    /**
     * Get cache key with namespace
     */
    public function get_namespaced_key($namespace, $key)
    {
        return $namespace . '_' . $key;
    }
}