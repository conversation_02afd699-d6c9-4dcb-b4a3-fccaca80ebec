<?php

class QKB_Rate_Limiter {
    private $last_request = 0;
    private $delay = 1; // Delay in seconds between requests

    public function throttle() {
        $current_time = microtime(true);
        $time_passed = $current_time - $this->last_request;
        
        if ($time_passed < $this->delay) {
            $sleep_time = (int)(($this->delay - $time_passed) * 1000000);
            if ($sleep_time > 0) {
                usleep($sleep_time);
            }
        }
        
        $this->last_request = microtime(true);
    }

    public function set_delay($seconds) {
        $this->delay = max(0.5, floatval($seconds));
    }
} 