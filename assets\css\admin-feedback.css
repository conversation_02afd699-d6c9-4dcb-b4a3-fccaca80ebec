/* Feedback Analytics Styles - Modern Design */
:root {
  --qkb-primary: #4f46e5;
  --qkb-primary-light: #818cf8;
  --qkb-primary-dark: #3730a3;
  --qkb-success: #10b981;
  --qkb-success-light: #d1fae5;
  --qkb-danger: #ef4444;
  --qkb-danger-light: #fee2e2;
  --qkb-gray-50: #f9fafb;
  --qkb-gray-100: #f3f4f6;
  --qkb-gray-200: #e5e7eb;
  --qkb-gray-300: #d1d5db;
  --qkb-gray-400: #9ca3af;
  --qkb-gray-500: #6b7280;
  --qkb-gray-600: #4b5563;
  --qkb-gray-700: #374151;
  --qkb-gray-800: #1f2937;
  --qkb-gray-900: #111827;
  --qkb-border-radius: 12px;
  --qkb-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --qkb-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --qkb-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --qkb-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --qkb-transition: all 0.3s ease;
}

/* Main layout */
.qkb-analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin: 24px 0;
}

/* Card styling */
.qkb-analytics-card {
  background: #fff;
  padding: 24px;
  border-radius: var(--qkb-border-radius);
  box-shadow: var(--qkb-shadow);
  transition: var(--qkb-transition);
  border: 1px solid var(--qkb-gray-100);
  position: relative;
  overflow: hidden;
}

.qkb-analytics-card:hover {
  box-shadow: var(--qkb-shadow-md);
  transform: translateY(-2px);
}

.qkb-analytics-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--qkb-primary),
    var(--qkb-primary-light)
  );
}

.qkb-analytics-card h3 {
  margin: 0 0 16px;
  color: var(--qkb-gray-800);
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.qkb-stat {
  font-size: 28px;
  font-weight: 700;
  color: var(--qkb-primary);
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.qkb-trend {
  font-size: 14px;
  padding: 6px 10px;
  border-radius: 20px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  transition: var(--qkb-transition);
}

.qkb-trend.positive {
  background: var(--qkb-success-light);
  color: var(--qkb-success);
}

.qkb-trend.negative {
  background: var(--qkb-danger-light);
  color: var(--qkb-danger);
}

.qkb-feedback-table {
  grid-column: 1 / -1;
  background: #fff;
  padding: 24px;
  border-radius: var(--qkb-border-radius);
  box-shadow: var(--qkb-shadow);
  border: 1px solid var(--qkb-gray-100);
}

.qkb-feedback-indicator {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  gap: 6px;
}

.qkb-feedback-indicator.positive {
  background: var(--qkb-success-light);
  color: var(--qkb-success);
}

.qkb-feedback-indicator.negative {
  background: var(--qkb-danger-light);
  color: var(--qkb-danger);
}

/* Chart containers */
.qkb-chart-container {
  height: 300px;
  margin-bottom: 20px;
  position: relative;
  transition: var(--qkb-transition);
}

/* Filters and controls */
.qkb-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;
  background: #fff;
  padding: 16px 20px;
  border-radius: var(--qkb-border-radius);
  box-shadow: var(--qkb-shadow-sm);
  border: 1px solid var(--qkb-gray-100);
}

.qkb-controls > div {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.qkb-controls label {
  font-weight: 500;
  color: var(--qkb-gray-700);
  font-size: 13px;
}

.qkb-controls select,
.qkb-controls input[type="text"] {
  min-width: 200px;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid var(--qkb-gray-300);
  background-color: var(--qkb-gray-50);
  transition: var(--qkb-transition);
}

.qkb-controls select:focus,
.qkb-controls input[type="text"]:focus {
  border-color: var(--qkb-primary);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
  outline: none;
}

.qkb-controls .button-primary {
  margin-left: auto;
  background: var(--qkb-primary);
  border-color: var(--qkb-primary-dark);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: var(--qkb-transition);
  display: flex;
  align-items: center;
  gap: 8px;
  height: auto;
  line-height: 1.5;
}

.qkb-controls .button-primary:hover {
  background: var(--qkb-primary-dark);
  transform: translateY(-1px);
}

/* Clear feedback button styling */
#qkb-clear-feedback {
  background: white;
  border: 1px solid var(--qkb-danger);
  color: var(--qkb-danger);
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: var(--qkb-transition);
  display: flex;
  align-items: center;
  gap: 8px;
  height: auto;
  line-height: 1.5;
  cursor: pointer;
}

#qkb-clear-feedback:hover {
  background: var(--qkb-danger);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

#qkb-clear-feedback:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

#qkb-clear-feedback .dashicons.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Pagination */
.qkb-pagination {
  display: flex;
  justify-content: center;
  margin: 24px 0;
  gap: 8px;
  align-items: center;
}

.qkb-pagination a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border: 1px solid var(--qkb-gray-200);
  text-decoration: none;
  border-radius: 6px;
  color: var(--qkb-gray-700);
  font-weight: 500;
  min-width: 36px;
  transition: var(--qkb-transition);
  background: white;
  height: 36px;
}

.qkb-pagination a:hover {
  background: var(--qkb-gray-50);
  border-color: var(--qkb-gray-300);
  color: var(--qkb-primary);
  transform: translateY(-1px);
  box-shadow: var(--qkb-shadow-sm);
}

.qkb-pagination a.current {
  background: var(--qkb-primary);
  color: white;
  border-color: var(--qkb-primary);
  box-shadow: 0 2px 5px rgba(79, 70, 229, 0.3);
}

.qkb-pagination-ellipsis {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  color: var(--qkb-gray-500);
  font-weight: 500;
}

.qkb-pagination-disabled {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border: 1px solid var(--qkb-gray-100);
  border-radius: 6px;
  color: var(--qkb-gray-300);
  min-width: 36px;
  background: var(--qkb-gray-50);
  height: 36px;
  cursor: not-allowed;
}

/* Modal */
.qkb-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 100000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.qkb-modal.active {
  opacity: 1;
}

.qkb-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(17, 24, 39, 0.7);
  backdrop-filter: blur(4px);
}

.qkb-modal-container {
  position: relative;
  width: 90%;
  max-width: 800px;
  max-height: 90%;
  margin: 40px auto;
  background: #fff;
  border-radius: var(--qkb-border-radius);
  overflow: hidden;
  box-shadow: var(--qkb-shadow-lg);
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.qkb-modal.active .qkb-modal-container {
  transform: translateY(0);
}

.qkb-modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--qkb-gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--qkb-gray-50);
}

.qkb-modal-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--qkb-gray-800);
}

.qkb-modal-close {
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--qkb-gray-500);
  transition: var(--qkb-transition);
  background: white;
  border: 1px solid var(--qkb-gray-200);
}

.qkb-modal-close:hover {
  background: var(--qkb-gray-100);
  color: var(--qkb-gray-700);
}

.qkb-modal-content {
  padding: 24px;
  overflow-y: auto;
  max-height: calc(90vh - 80px);
}

/* Action buttons */
.qkb-action-btn {
  padding: 8px 12px;
  background: white;
  border: 1px solid var(--qkb-gray-200);
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  text-decoration: none;
  color: var(--qkb-gray-700);
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: var(--qkb-transition);
  font-weight: 500;
}

.qkb-action-btn:hover {
  background: var(--qkb-gray-50);
  border-color: var(--qkb-gray-300);
  transform: translateY(-1px);
  box-shadow: var(--qkb-shadow-sm);
}

.qkb-action-btn.view {
  color: var(--qkb-primary);
}

.qkb-action-btn.view:hover {
  background: rgba(79, 70, 229, 0.05);
  border-color: rgba(79, 70, 229, 0.2);
}

/* Export options */
.qkb-export-options {
  display: flex;
  align-items: center;
  gap: 10px;
}

.qkb-export-options select {
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid var(--qkb-gray-300);
  background-color: var(--qkb-gray-50);
  transition: var(--qkb-transition);
}

.qkb-export-options button {
  background: white;
  border: 1px solid var(--qkb-gray-300);
  color: var(--qkb-gray-700);
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: var(--qkb-transition);
  cursor: pointer;
}

.qkb-export-options button:hover {
  background: var(--qkb-gray-50);
  border-color: var(--qkb-gray-400);
  transform: translateY(-1px);
  box-shadow: var(--qkb-shadow-sm);
}

/* Table styling */
.qkb-feedback-table table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  margin-top: 16px;
  border: 1px solid var(--qkb-gray-200);
  border-radius: 8px;
  overflow: hidden;
}

.qkb-feedback-table th {
  background: var(--qkb-gray-50);
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: var(--qkb-gray-700);
  border-bottom: 1px solid var(--qkb-gray-200);
  font-size: 14px;
}

.qkb-feedback-table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--qkb-gray-100);
  vertical-align: top;
}

.qkb-feedback-table tr:last-child td {
  border-bottom: none;
}

.qkb-feedback-table tr:hover td {
  background: var(--qkb-gray-50);
}

/* Response Cell Formatting */
.qkb-response-cell {
  max-width: 400px;
  overflow-x: auto;
  white-space: normal;
  line-height: 1.5;
  color: var(--qkb-gray-700);
}

.qkb-response-cell pre {
  background: var(--qkb-gray-50);
  padding: 12px;
  border-radius: 8px;
  margin: 12px 0;
  overflow-x: auto;
  border: 1px solid var(--qkb-gray-200);
  font-family: monospace;
}

.qkb-response-cell code {
  background: var(--qkb-gray-100);
  padding: 2px 4px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.9em;
  color: var(--qkb-primary-dark);
}

.qkb-response-cell ul,
.qkb-response-cell ol {
  margin-left: 20px;
  margin-bottom: 12px;
}

.qkb-response-cell p {
  margin-bottom: 12px;
}

.qkb-response-cell a {
  color: var(--qkb-primary);
  text-decoration: none;
  transition: var(--qkb-transition);
}

.qkb-response-cell a:hover {
  text-decoration: underline;
  color: var(--qkb-primary-dark);
}

.qkb-response-cell h1,
.qkb-response-cell h2,
.qkb-response-cell h3 {
  margin: 16px 0 12px;
  color: var(--qkb-gray-800);
  font-weight: 600;
}

/* Add assistant column styling */
.qkb-feedback-table th:nth-child(2),
.qkb-feedback-table td:nth-child(2) {
  width: 200px;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Feedback details styling */
.qkb-feedback-details {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

.qkb-feedback-meta {
  background: var(--qkb-gray-50);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--qkb-gray-200);
}

.qkb-feedback-meta p {
  margin: 8px 0;
}

.qkb-feedback-content {
  display: grid;
  gap: 16px;
}

.qkb-feedback-content h3 {
  margin: 0 0 8px;
  font-size: 16px;
  color: var(--qkb-gray-700);
}

.qkb-feedback-query,
.qkb-feedback-response {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--qkb-gray-200);
}

/* No data message */
.qkb-no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background-color: var(--qkb-gray-50);
  border-radius: 8px;
  border: 1px dashed var(--qkb-gray-300);
  text-align: center;
}

.qkb-no-data .dashicons {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: var(--qkb-gray-400);
  margin-bottom: 16px;
}

.qkb-no-data p {
  font-size: 16px;
  color: var(--qkb-gray-600);
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 782px) {
  .qkb-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .qkb-controls .button-primary {
    margin-left: 0;
    margin-top: 8px;
  }

  .qkb-analytics-grid {
    grid-template-columns: 1fr;
  }
}
