/* Chat Input Styling */

.qi-input-container textarea {
  width: 100%;
  border: none;
  border-style: none;
  background: transparent;
  font-size: var(--qkb-font-size-sm);
  color: var(--qkb-text);
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  resize: none;
  transition: var(--qkb-transition);
  padding: 0;
  overflow-x: hidden;
}

.qi-input-container textarea::placeholder {
  color: var(--qkb-text-light);
  opacity: 0.8;
  transition: var(--qkb-transition);
  font-size: var(--qkb-font-size-sm);
}

.qi-input-container textarea:focus::placeholder {
  opacity: 0.5;
  transform: translateX(4px);
}

.qi-send-button svg {
  width: 15px;
  height: 15px;
  stroke: var(--qkb-bg);
  stroke-width: 2.5px;
  transition: var(--qkb-transition);
  -webkit-transition: var(--qkb-transition);
  -moz-transition: var(--qkb-transition);
  -ms-transition: var(--qkb-transition);
  -o-transition: var(--qkb-transition);
}

.qi-character-count {
  position: absolute;
  bottom: 10px;
  right: 70px;
  font-size: 12px;
  color: var(--qkb-text-light);
  opacity: 0.7;
  transition: all 0.3s var(--qkb-transition);
  display: flex;
  align-items: center;
  gap: 6px;
  z-index: 5;
}

.qi-input-container:focus-within .qi-character-count {
  opacity: 1;
  transform: translateY(-2px);
}

.qi-character-count.qi-limit-warning {
  color: var(--qkb-warning, #f59e0b);
  background-color: rgba(var(--qkb-warning-rgb, 234, 179, 8), 0.1);
}

.qi-character-count.qi-limit-warning {
  stroke: var(--qkb-warning, #f59e0b);
}

.qi-character-count.qi-limit-exceeded {
  color: var(--qkb-error, #ef4444);
  background-color: rgba(var(--qkb-error-rgb, 220, 38, 38), 0.1);
  animation: pulse 1.5s infinite;
}

.qi-character-count.qi-limit-exceeded {
  stroke: var(--qkb-error, #ef4444);
}

.qi-utility-buttons {
  display: flex;
  gap: var(--qkb-gap);
  margin-right: var(--qkb-gap-md);
  z-index: 10;
  transition: all 0.3s var(--qkb-transition);
  position: relative;
}

.qi-utility-button {
  width: 36px;
  height: 36px;
  border-radius: var(--qkb-radius-full);
  background: var(--qkb-bg);
  border: 1px solid var(--qkb-border-light);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s var(--qkb-transition);
  box-shadow: var(--qkb-shadow-sm);
  position: relative;
  overflow: hidden;
  color: var(--qkb-text-light);
  margin: 0 2px;
}

.qi-utility-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--qkb-shadow-md);
  border-color: var(--qkb-primary);
  color: var(--qkb-primary);
}

.qi-utility-button:active {
  transform: translateY(0);
  box-shadow: var(--qkb-shadow-sm);
}

.qi-utility-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    120deg,
    rgba(var(--qkb-primary-rgb, 59, 130, 246), 0.1),
    rgba(var(--qkb-primary-rgb, 59, 130, 246), 0),
    rgba(var(--qkb-primary-rgb, 59, 130, 246), 0.1)
  );
  transform: translateX(-100%);
  transition: transform 0.6s var(--qkb-transition);
}

.qi-utility-button:hover::before {
  transform: translateX(100%);
}

.qi-utility-buttons button {
  background: transparent;
  border: none;
  color: var(--qkb-text-light);
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
}

.qi-utility-buttons button:hover {
  background-color: var(--qkb-bg-light);
  color: var(--qkb-primary);
}

.qi-button-pulse {
  animation: tools-pulse 0.3s ease;
}

.qi-send-button {
  border-radius: var(--qkb-radius-xs);
  background: var(--qkb-primary);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--qkb-transition);
  overflow: hidden;
  z-index: 10;
  transform-origin: center center;
}

.qi-send-button:hover {
  background: var(--qkb-primary-light);
}


/* Send button animation */
.qi-send-button.qi-sending {
  animation: send-pulse 1s ease;
}

.qi-submit-buttons {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-end !important;
}

.qi-input-container {
  border-radius: var(--qkb-radius-sm);
  border: 2px solid var(--qkb-primary-light);
  transition: var(--qkb-transition);
  overflow: hidden;
  width: 80%;
  margin: 10px auto;
  padding: var(--qkb-padding-sm);
  min-height: 120px;
  max-height: 200px;
  contain: layout;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex-shrink: 0;
  z-index: 1000;
  -webkit-border-radius: var(--qkb-radius-sm);
  -moz-border-radius: var(--qkb-radius-sm);
  -ms-border-radius: var(--qkb-radius-sm);
  -o-border-radius: var(--qkb-radius-sm);
  -webkit-transition: ;
  -moz-transition: ;
  -ms-transition: ;
  -o-transition: ;
}

.qi-input-area {
  position: relative;
  width: 100%;
  transition: all 0.3s var(--qkb-transition);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
}

.qi-input-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 5;
}

.qi-keyboard-shortcut {
  position: absolute;
  bottom: 60px;
  right: 16px;
  font-size: 11px;
  color: var(--qkb-text-light);
  opacity: 0.7;
  background-color: var(--qkb-bg-light);
  padding: 3px 6px;
  border-radius: 4px;
  pointer-events: none;
  transition: all 0.3s var(--qkb-transition);
  display: none;
}

/* File attachment functionality has been removed */

/* Tooltip styling */
.qi-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.2s ease;
  z-index: 1000;
  white-space: nowrap;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.qi-tooltip::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid rgba(0, 0, 0, 0.8);
}

.qi-tooltip-visible {
  opacity: 1;
  transform: translateY(0);
}

/* Send button ready state */
.qi-send-ready {
  animation: send-ready-pulse 2s infinite;
}
