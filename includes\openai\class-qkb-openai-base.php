<?php
if (!defined('ABSPATH')) {
    exit;
}

class QKB_OpenAI_Base {
    protected $api_key;
    protected $model;
    protected $max_tokens;
    protected $temperature;
    protected $embedding_model;
    protected $presence_penalty;
    protected $frequency_penalty;
    protected $rate_limiter;
    protected $error_handler;
    protected $cache;
    protected $kb_handler;
    protected $nlp_handler;

    public function __construct() {
        $this->api_key = get_option('qkb_openai_api_key');
        $this->model = get_option('qkb_openai_model', 'gpt-4o');
        $this->max_tokens = intval(get_option('qkb_openai_max_tokens', 2000));
        $this->temperature = floatval(get_option('qkb_openai_temperature', 0.7));
        $this->embedding_model = get_option('qkb_openai_embedding_model', 'text-embedding-3-small');
        $this->presence_penalty = floatval(get_option('qkb_openai_presence_penalty', 0.1));
        $this->frequency_penalty = floatval(get_option('qkb_openai_frequency_penalty', 0.1));

        try {
            $this->rate_limiter = new QKB_Rate_Limiter();
            $this->error_handler = new QKB_Error_Handler();
            $this->cache = new QKB_Cache();
            $this->kb_handler = QKB_Knowledge_Base::get_instance();
            $this->nlp_handler = new QKB_NLP_Handler();
        } catch (Exception $e) {
            error_log('QKB OpenAI Handler initialization error: ' . $e->getMessage());
        }
    }

    protected function make_request($endpoint, $data, $timeout = 30) {
        $response = wp_remote_post("https://api.openai.com/v1/$endpoint", [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->api_key,
                'Content-Type' => 'application/json',
            ],
            'body' => wp_json_encode($data),
            'timeout' => $timeout,
        ]);

        // Track API usage
        do_action('qkb_after_openai_request', $response, $endpoint);

        return $response;
    }
}
