/* Chat Window Styling */

.qi-main-chat {
    grid-area: main;
    min-width: 0;
    height: 100%;
    overflow: hidden;
    background-color: var(--qkb-bg);
    position: relative;
    contain: layout;
    flex: 1;
    max-width: 100%;
    display: flex;
    flex-direction: column;
}

.qi-chat-wrapper {
    overflow: hidden;
    width: 100%;
    height: 100%;
    transition: var(--qkb-transition);
    position: relative;
    min-height: 0;
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
}


/* Particles.js container */
.qi-particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Welcome message content container */
.qi-welcome-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    height: auto;
    margin: auto;
    flex: 1;
    width: 100%;
    padding: var(--qkb-padding);
}

.qi-welcome-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.qi-welcome-title {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.qi-welcome-title h2 {
    font-size: var(--qkb-font-size-xl);
    font-weight: var(--qkb-font-weight-bold);
    color: var(--qkb-text);
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, var(--qkb-primary), var(--qkb-error));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    position: relative;
}

.qi-welcome-title span {
    font-size: calc(var(--qkb-font-size-sm) + var(--qkb-font-size-sm) );
    color: var(--qkb-text);
    margin-top: var(--qkb-gap-md);
    line-height: 1.4;
    font-weight: var(--qkb-font-weight-medium);
    animation-delay: 0.3s;
}

/* Typing Indicator */


.qi-typing-content {
    display: flex;
    align-items: center;
    gap: var(--qkb-gap);
}

.qi-typing-indicator {
    margin: 8px 0;
}

.qi-typing-text {
    color: var(--qkb-text);
    font-size: var(--qkb-font-size-sm);
}

.qi-typing-dots {
    display: flex;
    gap: 4px;
}

.qi-typing-dot {
    width: 6px;
    height: 6px;
    background: var(--qkb-primary);
    border-radius: 50%;
    opacity: 0.4;
}

.qi-typing-dot:nth-child(1) {
    animation: qkbDotBounce 1.4s infinite;
}

.qi-typing-dot:nth-child(2) {
    animation: qkbDotBounce 1.4s infinite 0.2s;
}

.qi-typing-dot:nth-child(3) {
    animation: qkbDotBounce 1.4s infinite 0.4s;
}

/* Scrollbar Styling */

.qi-input-container::-webkit-scrollbar,
.qi-chat-messages::-webkit-scrollbar,
.qi-assistants-wrapper::-webkit-scrollbar,
.qi-chat-input textarea::-webkit-scrollbar,
.qi-history-wrapper::-webkit-scrollbar {
    width: var(--qkb-gap);
}

.qi-input-container::-webkit-scrollbar-track,
.qi-chat-messages::-webkit-scrollbar-track,
.qi-assistants-wrapper::-webkit-scrollbar-track,
.qi-chat-input textarea::-webkit-scrollbar-track,
.qi-history-wrapper::-webkit-scrollbar-track {
    background: transparent;
    border-radius: var(--qkb-radius-sm);
}

.qi-input-container::-webkit-scrollbar-thumb,
.qi-chat-messages::-webkit-scrollbar-thumb,
.qi-assistants-wrapper::-webkit-scrollbar-thumb,
.qi-chat-input textarea::-webkit-scrollbar-thumb,
.qi-history-wrapper::-webkit-scrollbar-thumb {
    background: var(--qkb-primary);
    border-radius: var(--qkb-radius-sm);
}

/* End Scrollbar Styling */