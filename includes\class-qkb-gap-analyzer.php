<?php
if (!defined('ABSPATH')) {
    exit;
}

class QKB_Gap_Analyzer {
    private $ml_handler;
    private $nlp_handler;
    private $openai_handler;
    private $db;
    private $gap_threshold = 0.4; // Confidence threshold for identifying gaps

    public function __construct() {
        global $wpdb;
        $this->db = $wpdb;
        $this->ml_handler = QKB_ML_Handler::get_instance();
        $this->nlp_handler = new QKB_NLP_Handler();
        $this->openai_handler = new QKB_OpenAI_Handler();

        $this->init_tables();
    }

    private function init_tables() {
        $charset_collate = $this->db->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS {$this->db->prefix}qkb_content_gaps (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            topic varchar(255) NOT NULL,
            frequency int(11) DEFAULT 1,
            confidence_score float DEFAULT 0,
            first_detected datetime DEFAULT CURRENT_TIMESTAMP,
            last_detected datetime DEFAULT CURRENT_TIMESTAMP,
            status varchar(20) DEFAULT 'pending',
            suggested_content text,
            PRIMARY KEY  (id),
            KEY topic (topic),
            KEY status (status)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    public function analyze_query($query, $search_results) {
        // Skip analysis if good results found
        if (!empty($search_results['confidence_score']) && $search_results['confidence_score'] > 0.7) {
            return;
        }

        // Extract key topics using NLP
        $analysis = $this->nlp_handler->analyze_query($query);
        $keywords = $analysis['keywords'];

        // Get topic clusters
        $topics = $this->extract_topics($query, $keywords);

        foreach ($topics as $topic) {
            $this->record_content_gap($topic, $search_results['confidence_score'] ?? 0);
        }
    }

    private function extract_topics($query, $keywords) {
        $messages = [
            [
                'role' => 'system',
                'content' => "Extract key topics that should be covered in a knowledge base from the given query. Return topics as a JSON array of strings. Focus on specific, actionable topics."
            ],
            [
                'role' => 'user',
                'content' => "Query: $query\nKeywords: " . implode(', ', $keywords)
            ]
        ];

        try {
            $response = $this->openai_handler->get_completion_raw($messages);
            $topics = json_decode($response, true);
            return is_array($topics) ? $topics : [];
        } catch (Exception $e) {
            error_log('Topic extraction failed: ' . $e->getMessage());
            return [];
        }
    }

    private function record_content_gap($topic, $confidence_score) {
        $existing = $this->db->get_row($this->db->prepare(
            "SELECT * FROM {$this->db->prefix}qkb_content_gaps WHERE topic = %s",
            $topic
        ));

        if ($existing) {
            $this->db->update(
                $this->db->prefix . 'qkb_content_gaps',
                [
                    'frequency' => $existing->frequency + 1,
                    'confidence_score' => ($existing->confidence_score + $confidence_score) / 2,
                    'last_detected' => current_time('mysql')
                ],
                ['id' => $existing->id]
            );
        } else {
            $this->db->insert(
                $this->db->prefix . 'qkb_content_gaps',
                [
                    'topic' => $topic,
                    'confidence_score' => $confidence_score,
                    'status' => 'pending'
                ]
            );
        }
    }

    public function get_content_gaps($status = 'pending', $limit = 10) {
        return $this->db->get_results($this->db->prepare(
            "SELECT * FROM {$this->db->prefix}qkb_content_gaps
            WHERE status = %s
            ORDER BY frequency DESC, confidence_score ASC
            LIMIT %d",
            $status,
            $limit
        ));
    }


    public function analyze_content_performance() {
        $gaps = $this->get_content_gaps();
        $recommendations = [];

        foreach ($gaps as $gap) {
            // Analyze user engagement patterns
            $engagement_metrics = $this->analyze_user_engagement($gap->topic);

            // Calculate priority score
            $priority_score = $this->calculate_priority_score($gap, $engagement_metrics);

            // Generate content suggestion
            $content_suggestion = $this->generate_content_suggestion($gap->topic);

            $recommendations[] = [
                'topic' => $gap->topic,
                'priority_score' => $priority_score,
                'suggested_content' => $content_suggestion,
                'metrics' => [
                    'frequency' => $gap->frequency,
                    'confidence_score' => $gap->confidence_score,
                    'user_engagement' => $engagement_metrics
                ]
            ];
        }

        return $recommendations;
    }

    /**
     * Generate content suggestion for a topic
     *
     * @param string $topic The topic to generate content for
     * @return string The suggested content
     */
    public function generate_content_suggestion($topic) {
        $messages = [
            [
                'role' => 'system',
                'content' => "You are a content strategist helping to create knowledge base articles. "
                    . "Generate a detailed outline for a knowledge base article on the given topic. "
                    . "Include suggested headings, key points to cover, and any relevant information "
                    . "that would make this a comprehensive resource. Format your response in markdown."
            ],
            [
                'role' => 'user',
                'content' => "Topic: $topic"
            ]
        ];

        try {
            $suggestion = $this->openai_handler->get_completion_raw($messages);
            return $suggestion;
        } catch (Exception $e) {
            error_log('Content suggestion generation failed: ' . $e->getMessage());
            return "# $topic\n\nSuggested article outline could not be generated. Please create content manually.";
        }
    }

    private function analyze_user_engagement($topic) {
        global $wpdb;

        // Get interaction metrics for the topic
        $metrics = $wpdb->get_row($wpdb->prepare(
            "SELECT
                COUNT(*) as total_queries,
                AVG(CASE WHEN feedback > 0 THEN 1 ELSE 0 END) as satisfaction_rate,
                AVG(confidence_score) as avg_confidence
            FROM {$wpdb->prefix}qkb_content_gaps
            WHERE topic = %s",
            $topic
        ));

        return [
            'query_volume' => $metrics->total_queries,
            'satisfaction_rate' => $metrics->satisfaction_rate ?: 0,
            'confidence_score' => $metrics->avg_confidence ?: 0,
            'trend' => $this->calculate_topic_trend($topic)
        ];
    }

    /**
     * Calculate trend for a specific topic
     *
     * @param string $topic The topic to calculate trend for
     * @return float Trend value between -1 and 1
     */
    private function calculate_topic_trend($topic) {
        global $wpdb;

        // Get query count from last 7 days
        $recent_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}qkb_ml_interactions
            WHERE query LIKE %s
            AND timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)",
            '%' . $wpdb->esc_like($topic) . '%'
        ));

        // Get query count from 8-14 days ago
        $previous_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}qkb_ml_interactions
            WHERE query LIKE %s
            AND timestamp >= DATE_SUB(NOW(), INTERVAL 14 DAY)
            AND timestamp < DATE_SUB(NOW(), INTERVAL 7 DAY)",
            '%' . $wpdb->esc_like($topic) . '%'
        ));

        // Calculate trend
        if ($previous_count > 0) {
            $trend = ($recent_count - $previous_count) / $previous_count;
            // Normalize between -1 and 1
            return max(-1, min(1, $trend));
        } elseif ($recent_count > 0) {
            // New topic with recent queries
            return 1;
        }

        return 0;
    }

    private function calculate_priority_score($gap, $engagement_metrics) {
        // Weight factors for priority calculation
        $weights = [
            'frequency' => 0.3,
            'satisfaction' => 0.25,
            'confidence' => 0.25,
            'trend' => 0.2
        ];

        return (
            ($gap->frequency * $weights['frequency']) +
            ((1 - $engagement_metrics['satisfaction_rate']) * $weights['satisfaction']) +
            ((1 - $engagement_metrics['confidence_score']) * $weights['confidence']) +
            ($engagement_metrics['trend'] * $weights['trend'])
        );
    }
}