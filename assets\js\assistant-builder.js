/**
 * Assistant Builder JavaScript
 */
(function($) {
    'use strict';

    // Assistant Builder class
    class AssistantBuilder {
        constructor() {
            this.init();
        }

        /**
         * Initialize the assistant builder
         */
        init() {
            this.currentAssistantId = 0;
            this.assistants = [];
            this.templates = [];
            this.formidableForms = [];
            this.knowledgeContent = [];

            this.initElements();
            this.bindEvents();
            this.loadAssistants();
            this.loadSystemTemplates();
            this.loadFormidableForms();
            this.loadKnowledgeContent();
        }

        /**
         * Initialize elements
         */
        initElements() {
            this.$assistantsList = $('.qkb-assistants');
            this.$assistantForm = $('#qkb-assistant-form');
            this.$assistantIdField = $('#qkb-assistant-id');
            this.$openaiAssistantIdField = $('#qkb-openai-assistant-id');
            this.$assistantNameField = $('#qkb-assistant-name');
            this.$assistantDescriptionField = $('#qkb-assistant-description');
            this.$assistantIconField = $('#qkb-assistant-icon');
            this.$assistantModelField = $('#qkb-assistant-model');
            this.$assistantExpertiseField = $('#qkb-assistant-expertise');
            this.$assistantCategoriesField = $('#qkb-assistant-categories');
            this.$assistantTemplateField = $('#qkb-assistant-template');
            this.$assistantInstructionsField = $('#qkb-assistant-instructions');
            this.$formidableFormsField = $('#qkb-formidable-forms');
            this.$knowledgeList = $('.qkb-knowledge-list');
            this.$knowledgeSearch = $('#qkb-knowledge-search');
            this.$suggestedPromptsContainer = $('.qkb-suggested-prompts-container');
            this.$addSuggestedPromptButton = $('.qkb-add-suggested-prompt');
            this.$assistantActionsContainer = $('.qkb-assistant-actions-container');
            this.$addAssistantActionButton = $('.qkb-add-assistant-action');
            this.$saveAssistantButton = $('.qkb-save-assistant');
            this.$deleteAssistantButton = $('.qkb-delete-assistant');
            this.$addAssistantButton = $('.qkb-add-assistant');
            this.$editorTitle = $('.qkb-assistant-editor-title');
            this.$tabButtons = $('.qkb-tab-button');
            this.$tabPanes = $('.qkb-tab-pane');
        }

        /**
         * Bind events
         */
        bindEvents() {
            // Tab navigation
            this.$tabButtons.on('click', (e) => {
                const $button = $(e.currentTarget);
                const tab = $button.data('tab');

                this.$tabButtons.removeClass('active');
                this.$tabPanes.removeClass('active');

                $button.addClass('active');
                $(`.qkb-tab-pane[data-tab="${tab}"]`).addClass('active');
            });

            // Assistant selection
            this.$assistantsList.on('click', '.qkb-assistant-item', (e) => {
                const assistantId = $(e.currentTarget).data('id');
                this.loadAssistant(assistantId);
            });

            // Add assistant
            this.$addAssistantButton.on('click', () => {
                this.resetForm();
                this.$editorTitle.text('New Assistant');
                this.$deleteAssistantButton.hide();
            });

            // Save assistant
            this.$saveAssistantButton.on('click', () => {
                this.saveAssistant();
            });

            // Delete assistant
            this.$deleteAssistantButton.on('click', () => {
                this.deleteAssistant();
            });

            // Template selection
            this.$assistantTemplateField.on('change', () => {
                const templateId = this.$assistantTemplateField.val();
                if (templateId) {
                    const template = this.templates.find(t => t.id == templateId);
                    if (template) {
                        this.$assistantInstructionsField.val(template.content);
                    }
                }
            });

            // Knowledge search
            this.$knowledgeSearch.on('input', this.debounce(() => {
                this.filterKnowledgeContent();
            }, 300));

            // Add suggested prompt
            this.$addSuggestedPromptButton.on('click', () => {
                this.addSuggestedPrompt('');
            });

            // Remove suggested prompt
            this.$suggestedPromptsContainer.on('click', '.qkb-remove-suggested-prompt', (e) => {
                $(e.currentTarget).closest('.qkb-suggested-prompt-item').remove();
            });

            // Add assistant action
            this.$addAssistantActionButton.on('click', () => {
                this.addAssistantAction({
                    index: this.getNextAssistantActionIndex(),
                    name: '',
                    content: '',
                    show_in_modal: false,
                    modal_title: ''
                });
            });

            // Remove assistant action
            this.$assistantActionsContainer.on('click', '.qkb-remove-assistant-action', (e) => {
                $(e.currentTarget).closest('.qkb-assistant-action-item').remove();
            });

            // Toggle modal options
            this.$assistantActionsContainer.on('change', '.qkb-modal-toggle', (e) => {
                const $checkbox = $(e.currentTarget);
                const $modalTitle = $checkbox.closest('.qkb-assistant-action-item').find('.qkb-assistant-action-modal-title');

                if ($checkbox.is(':checked')) {
                    $modalTitle.show();
                } else {
                    $modalTitle.hide();
                }
            });

            // Remove assistant action
            this.$assistantActionsContainer.on('click', '.qkb-remove-assistant-action', (e) => {
                $(e.currentTarget).closest('.qkb-assistant-action-item').remove();
            });

            // Toggle modal options
            this.$assistantActionsContainer.on('change', '.qkb-modal-toggle', (e) => {
                const $checkbox = $(e.currentTarget);
                const $modalTitle = $checkbox.closest('.qkb-assistant-action-item').find('.qkb-assistant-action-modal-title');

                if ($checkbox.is(':checked')) {
                    $modalTitle.show();
                } else {
                    $modalTitle.hide();
                }
            });
        }

        /**
         * Load assistants
         */
        loadAssistants() {
            this.$assistantsList.empty().append('<li class="qkb-loading">Loading Assistants...</li>');

            // Get assistants
            const data = {
                action: 'qkb_get_terms',
                nonce: qkbAssistantBuilder.nonce,
                taxonomy: 'kb_assistant'
            };

            $.post(qkbAssistantBuilder.ajaxUrl, data, (response) => {
                this.$assistantsList.empty();

                if (response.success && response.data.terms.length > 0) {
                    this.assistants = response.data.terms;

                    // Add assistants to list
                    this.assistants.forEach((assistant) => {
                        const $item = $('<li class="qkb-assistant-item"></li>')
                            .attr('data-id', assistant.term_id)
                            .text(assistant.name);

                        this.$assistantsList.append($item);
                    });
                } else {
                    this.$assistantsList.append(`<li class="qkb-no-items">${qkbAssistantBuilder.strings.noAssistants}</li>`);
                }
            }).fail(() => {
                this.$assistantsList.empty().append(`<li class="qkb-error">${qkbAssistantBuilder.strings.loadError}</li>`);
            });
        }

        /**
         * Load system templates
         */
        loadSystemTemplates() {
            // Get system templates
            const data = {
                action: 'qkb_get_system_templates',
                nonce: qkbAssistantBuilder.nonce
            };

            $.post(qkbAssistantBuilder.ajaxUrl, data, (response) => {
                this.$assistantTemplateField.empty().append('<option value="">Select a template</option>');

                if (response.success && response.data.templates.length > 0) {
                    this.templates = response.data.templates;

                    // Add templates to select
                    this.templates.forEach((template) => {
                        const $option = $('<option></option>')
                            .attr('value', template.id)
                            .text(template.name);

                        this.$assistantTemplateField.append($option);
                    });
                } else {
                    this.$assistantTemplateField.append(`<option value="" disabled>${qkbAssistantBuilder.strings.noTemplates}</option>`);
                }
            }).fail(() => {
                this.$assistantTemplateField.empty().append(`<option value="" disabled>${qkbAssistantBuilder.strings.loadError}</option>`);
            });
        }

        /**
         * Load Formidable Forms
         */
        loadFormidableForms() {
            // Check if Formidable Forms integration is enabled
            if (!qkbAssistantBuilder.formidableFormsEnabled) {
                return;
            }

            // Get Formidable Forms
            const data = {
                action: 'qkb_get_formidable_forms',
                nonce: qkbAssistantBuilder.nonce
            };

            $.post(qkbAssistantBuilder.ajaxUrl, data, (response) => {
                this.$formidableFormsField.empty().append('<option value="">Select a form</option>');

                if (response.success && response.data.forms && response.data.forms.length > 0) {
                    this.formidableForms = response.data.forms;

                    // Add forms to select
                    this.formidableForms.forEach((form) => {
                        const $option = $('<option></option>')
                            .attr('value', form.id)
                            .text(form.name);

                        this.$formidableFormsField.append($option);
                    });
                } else {
                    this.$formidableFormsField.append('<option value="" disabled>No forms found</option>');
                }
            }).fail(() => {
                this.$formidableFormsField.empty().append('<option value="" disabled>Error loading forms</option>');
            });
        }


        /**
         * Load knowledge content
         */
        loadKnowledgeContent() {
            // Get knowledge content
            const data = {
                action: 'qkb_get_knowledge_content',
                nonce: qkbAssistantBuilder.nonce
            };

            $.post(qkbAssistantBuilder.ajaxUrl, data, (response) => {
                this.$knowledgeList.empty();

                if (response.success && response.data.content.length > 0) {
                    this.knowledgeContent = response.data.content;

                    // Add content to list
                    this.knowledgeContent.forEach((content) => {
                        const $content = $(`
                            <div class="qkb-knowledge-item">
                                <label>
                                    <input type="checkbox" name="kb_content_${content.id}" value="${content.id}">
                                    ${content.title}
                                </label>
                                <p class="qkb-knowledge-type">${content.type}</p>
                            </div>
                        `);

                        this.$knowledgeList.append($content);
                    });
                } else {
                    this.$knowledgeList.append('<p class="qkb-no-items">No knowledge content found</p>');
                }
            }).fail(() => {
                this.$knowledgeList.empty().append('<p class="qkb-error">Error loading knowledge content</p>');
            });
        }

        /**
         * Filter knowledge content
         */
        filterKnowledgeContent() {
            const query = this.$knowledgeSearch.val().toLowerCase();

            $('.qkb-knowledge-item').each(function() {
                const title = $(this).find('label').text().toLowerCase();

                if (title.includes(query)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }

        /**
         * Load assistant
         *
         * @param {number} assistantId Assistant ID
         */
        loadAssistant(assistantId) {
            // Get assistant data
            const data = {
                action: 'qkb_get_assistant_data',
                nonce: qkbAssistantBuilder.nonce,
                assistant_id: assistantId
            };

            $.post(qkbAssistantBuilder.ajaxUrl, data, (response) => {
                if (response.success) {
                    const assistant = response.data;

                    // Set current assistant ID
                    this.currentAssistantId = assistant.id;

                    // Update form fields
                    this.$assistantIdField.val(assistant.id);
                    this.$openaiAssistantIdField.val(assistant.openai_assistant_id);
                    this.$assistantNameField.val(assistant.name);
                    this.$assistantDescriptionField.val(assistant.description);
                    this.$assistantIconField.val(assistant.icon);
                    this.$assistantModelField.val(assistant.model);
                    this.$assistantExpertiseField.val(assistant.expertise);
                    this.$assistantCategoriesField.val(assistant.categories);
                    this.$assistantTemplateField.val(assistant.template_id);
                    this.$assistantInstructionsField.val(assistant.instructions);

                    // Update knowledge content
                    $('.qkb-knowledge-item input').prop('checked', false);
                    if (assistant.kb_content && assistant.kb_content.length > 0) {
                        assistant.kb_content.forEach((contentId) => {
                            $(`.qkb-knowledge-item input[value="${contentId}"]`).prop('checked', true);
                        });
                    }

                    // Update suggested prompts
                    this.$suggestedPromptsContainer.empty();
                    if (assistant.suggested_prompts && assistant.suggested_prompts.length > 0) {
                        assistant.suggested_prompts.forEach((prompt) => {
                            this.addSuggestedPrompt(prompt);
                        });
                    }

                    // Update assistant actions
                    this.$assistantActionsContainer.empty();
                    if (assistant.assistant_actions && assistant.assistant_actions.length > 0) {
                        assistant.assistant_actions.forEach((action, index) => {
                            this.addAssistantAction({
                                index: index,
                                name: action.name,
                                content: action.content,
                                show_in_modal: action.show_in_modal,
                                modal_title: action.modal_title
                            });
                        });
                    }

                    // Update editor title
                    this.$editorTitle.text(assistant.name);

                    // Show delete button
                    this.$deleteAssistantButton.show();

                    // Update assistant list
                    $('.qkb-assistant-item').removeClass('active');
                    $(`.qkb-assistant-item[data-id="${assistant.id}"]`).addClass('active');
                } else {
                    alert(response.data || qkbAssistantBuilder.strings.loadError);
                }
            }).fail(() => {
                alert(qkbAssistantBuilder.strings.loadError);
            });
        }

        /**
         * Save assistant
         */
        saveAssistant() {
            // Validate form
            if (!this.$assistantNameField.val()) {
                alert('Assistant name is required');
                return;
            }

            // Get form data
            const formData = {
                action: 'qkb_save_assistant',
                nonce: qkbAssistantBuilder.nonce,
                assistant_id: this.$assistantIdField.val(),
                name: this.$assistantNameField.val(),
                description: this.$assistantDescriptionField.val(),
                openai_assistant_id: this.$openaiAssistantIdField.val(),
                icon: this.$assistantIconField.val(),
                model: this.$assistantModelField.val(),
                expertise: this.$assistantExpertiseField.val(),
                categories: this.$assistantCategoriesField.val(),
                template_id: this.$assistantTemplateField.val(),
                instructions: this.$assistantInstructionsField.val(),
                formidable_forms: JSON.stringify(this.$formidableFormsField.val() || []),
                kb_content: JSON.stringify(this.getSelectedKnowledgeContent()),
                suggested_prompts: JSON.stringify(this.getSuggestedPrompts()),
                assistant_actions: JSON.stringify(this.getAssistantActions())
            };

            // Disable save button
            this.$saveAssistantButton.prop('disabled', true).text('Saving...');

            // Save assistant
            $.post(qkbAssistantBuilder.ajaxUrl, formData, (response) => {
                if (response.success) {
                    // Update assistant ID
                    this.$assistantIdField.val(response.data.assistant_id);
                    this.currentAssistantId = response.data.assistant_id;

                    // Update editor title
                    this.$editorTitle.text(this.$assistantNameField.val());

                    // Show delete button
                    this.$deleteAssistantButton.show();

                    // Reload assistants
                    this.loadAssistants();

                    // Show success message
                    alert(response.data.message || qkbAssistantBuilder.strings.saveSuccess);
                } else {
                    alert(response.data || qkbAssistantBuilder.strings.saveError);
                }
            }).fail(() => {
                alert(qkbAssistantBuilder.strings.saveError);
            }).always(() => {
                // Enable save button
                this.$saveAssistantButton.prop('disabled', false).text('Save');
            });
        }

        /**
         * Delete assistant
         */
        deleteAssistant() {
            if (!this.currentAssistantId) {
                return;
            }

            if (!confirm(qkbAssistantBuilder.strings.confirmDelete)) {
                return;
            }

            // Get assistant data
            const data = {
                action: 'qkb_delete_assistant',
                nonce: qkbAssistantBuilder.nonce,
                assistant_id: this.currentAssistantId
            };

            // Disable delete button
            this.$deleteAssistantButton.prop('disabled', true).text('Deleting...');

            // Delete assistant
            $.post(qkbAssistantBuilder.ajaxUrl, data, (response) => {
                if (response.success) {
                    // Reset form
                    this.resetForm();

                    // Reload assistants
                    this.loadAssistants();

                    // Show success message
                    alert(response.data.message || qkbAssistantBuilder.strings.deleteSuccess);
                } else {
                    alert(response.data || qkbAssistantBuilder.strings.deleteError);
                }
            }).fail(() => {
                alert(qkbAssistantBuilder.strings.deleteError);
            }).always(() => {
                // Enable delete button
                this.$deleteAssistantButton.prop('disabled', false).text('Delete');
            });
        }

        /**
         * Reset form
         */
        resetForm() {
            this.currentAssistantId = 0;
            this.$assistantForm[0].reset();
            this.$assistantIdField.val('');
            this.$openaiAssistantIdField.val('');
            this.$assistantInstructionsField.val('');
            $('.qkb-knowledge-item input').prop('checked', false);
            this.$suggestedPromptsContainer.empty();
            this.$assistantActionsContainer.empty();
            this.$deleteAssistantButton.hide();
            $('.qkb-assistant-item').removeClass('active');
        }

        /**
         * Add suggested prompt
         *
         * @param {string} prompt Prompt text
         */
        addSuggestedPrompt(prompt) {
            const template = wp.template('suggested-prompt');
            const $prompt = $(template({ prompt }));
            this.$suggestedPromptsContainer.append($prompt);
        }

        /**
         * Add assistant action
         *
         * @param {Object} action Assistant action data
         */
        addAssistantAction(action) {
            const template = wp.template('assistant-action');
            const $action = $(template(action));
            this.$assistantActionsContainer.append($action);
        }

        /**
         * Get next assistant action index
         *
         * @returns {number} Next index
         */
        getNextAssistantActionIndex() {
            return this.$assistantActionsContainer.find('.qkb-assistant-action-item').length;
        }


        /**
         * Get selected knowledge content
         *
         * @returns {Array} Selected knowledge content IDs
         */
        getSelectedKnowledgeContent() {
            const content = [];
            $('.qkb-knowledge-item input:checked').each(function() {
                content.push($(this).val());
            });
            return content;
        }


        /**
         * Get suggested prompts
         *
         * @returns {Array} Suggested prompts
         */
        getSuggestedPrompts() {
            const prompts = [];
            $('.qkb-suggested-prompt-item input').each(function() {
                const prompt = $(this).val().trim();
                if (prompt) {
                    prompts.push(prompt);
                }
            });
            return prompts;
        }

        /**
         * Get assistant actions
         *
         * @returns {Array} Assistant actions
         */
        getAssistantActions() {
            const actions = [];
            $('.qkb-assistant-action-item').each(function() {
                const $item = $(this);
                const name = $item.find('input[name^="assistant_actions"][name$="[name]"]').val().trim();
                const content = $item.find('textarea[name^="assistant_actions"][name$="[content]"]').val().trim();
                const showInModal = $item.find('input[name^="assistant_actions"][name$="[show_in_modal]"]').is(':checked');
                const modalTitle = $item.find('input[name^="assistant_actions"][name$="[modal_title]"]').val().trim();

                if (name && content) {
                    actions.push({
                        name: name,
                        content: content,
                        show_in_modal: showInModal,
                        modal_title: modalTitle
                    });
                }
            });
            return actions;
        }

        /**
         * Debounce function
         *
         * @param {Function} func Function to debounce
         * @param {number} wait Wait time in milliseconds
         * @returns {Function} Debounced function
         */
        debounce(func, wait) {
            let timeout;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    func.apply(context, args);
                }, wait);
            };
        }
    }

    // Initialize on document ready
    $(document).ready(() => {
        new AssistantBuilder();
    });
})(jQuery);
