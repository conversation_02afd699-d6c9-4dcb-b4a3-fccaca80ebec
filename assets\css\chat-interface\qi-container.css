/* Container Styling */

.qi-chat-container {
    display: grid;
    grid-template-columns: auto 1fr;
    grid-template-rows: 1fr;
    grid-template-areas: "sidebar main";
    background: var(--qkb-bg);
    contain: layout;
    will-change: transform;
    backface-visibility: hidden;
    font-family: var(--qkb-font);
    overflow: hidden;
    height: 100vh;
    max-height: 100%;
    position: relative;
    transition: all 0.3s var(--qkb-transition);
}

.qi-chat-container[data-fullscreen="true"] {
    position: fixed;
    inset: 0;
    margin: 0;
    z-index: var(--qkb-z-index-top);
    background-color: var(--qkb-bg);
    width: 100vw;
    transition: all 0.4s var(--qkb-transition);
    animation: fullscreen-fade-in 0.4s var(--qkb-transition);
}

@keyframes fullscreen-fade-in {
    from {
        opacity: 0.8;
        transform: scale(0.98);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

.qi-chat-container[data-sidebar-hidden="true"] {
    grid-template-columns: 0 1fr;
}

.qi-chat-container[data-sidebar-hidden="true"] .qi-sidebar {
    transform: translateX(-100%);
    width: 0;
    opacity: 0;
}