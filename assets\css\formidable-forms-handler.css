/**
 * Styles for Formidable Forms in Quick Action modals
 */

/* Loading overlay */
.qkb-form-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 100;
}

.qkb-form-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--qkb-primary, #2271b1);
    animation: qkb-spin 1s ease-in-out infinite;
    margin-bottom: 10px;
}

@keyframes qkb-spin {
    to {
        transform: rotate(360deg);
    }
}

.qkb-form-loading-text {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

/* Success message */
.qkb-form-success {
    background-color: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 1.5;
}

/* Error message */
.qkb-form-error {
    background-color: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 1.5;
}

/* Close button */
.qkb-form-close-button {
    display: inline-block;
    background-color: var(--qkb-primary, #2271b1);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    border: none;
    margin-top: 10px;
    transition: background-color 0.2s ease;
}

.qkb-form-close-button:hover {
    background-color: var(--qkb-primary-dark, #185a8c);
}

/* Make sure forms in modals have proper spacing */
.qkb-quick-access-modal .frm_forms {
    margin: 0;
    padding: 0;
}

.qkb-quick-access-modal .frm_form_field {
    margin-bottom: 15px;
}

/* Fix for Formidable Forms in modals */
.qkb-quick-access-modal .frm_forms.with_frm_style .frm_submit button,
.qkb-quick-access-modal .frm_forms.with_frm_style .frm_submit input[type=submit] {
    background-color: var(--qkb-primary, #2271b1);
    color: white;
    border-color: var(--qkb-primary, #2271b1);
}

.qkb-quick-access-modal .frm_forms.with_frm_style .frm_submit button:hover,
.qkb-quick-access-modal .frm_forms.with_frm_style .frm_submit input[type=submit]:hover {
    background-color: var(--qkb-primary-dark, #185a8c);
    border-color: var(--qkb-primary-dark, #185a8c);
}

/* Fix for form container */
.qkb-form-overlay-container {
    position: relative;
    min-height: 50px;
    width: 100%;
}
