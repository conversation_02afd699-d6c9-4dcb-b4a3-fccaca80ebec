/**
 * Assistant Builder CSS
 */

/* Container */
.qkb-assistant-builder-container {
    display: flex;
    margin-top: 20px;
    background: #fff;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

/* Sidebar */
.qkb-assistant-sidebar {
    width: 250px;
    border-right: 1px solid #ccd0d4;
    background: #f9f9f9;
}

.qkb-assistant-list-header {
    padding: 15px;
    border-bottom: 1px solid #ccd0d4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.qkb-assistant-list-header h2 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.qkb-assistant-list {
    padding: 10px 0;
}

.qkb-assistants {
    margin: 0;
    padding: 0;
    list-style: none;
}

.qkb-assistant-item {
    padding: 10px 15px;
    cursor: pointer;
    border-left: 3px solid transparent;
    transition: background 0.2s ease;
}

.qkb-assistant-item:hover {
    background: #f1f1f1;
}

.qkb-assistant-item.active {
    background: #f1f1f1;
    border-left-color: #2271b1;
    font-weight: 600;
}

.qkb-loading,
.qkb-no-items,
.qkb-error {
    padding: 10px 15px;
    color: #666;
    font-style: italic;
}

.qkb-error {
    color: #d63638;
}

/* Editor */
.qkb-assistant-editor {
    flex: 1;
    min-width: 0;
}

.qkb-assistant-editor-header {
    padding: 15px;
    border-bottom: 1px solid #ccd0d4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.qkb-assistant-editor-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.qkb-assistant-actions {
    display: flex;
    gap: 10px;
}

.qkb-assistant-editor-content {
    padding: 20px;
}

/* Tabs */
.qkb-assistant-tabs {
    margin-top: 20px;
}

.qkb-tab-nav {
    display: flex;
    border-bottom: 1px solid #ccd0d4;
    margin-bottom: 20px;
}

.qkb-tab-button {
    padding: 10px 15px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 600;
    color: #646970;
    transition: all 0.2s ease;
}

.qkb-tab-button:hover {
    color: #2271b1;
}

.qkb-tab-button.active {
    color: #2271b1;
    border-bottom-color: #2271b1;
}

.qkb-tab-pane {
    display: none;
}

.qkb-tab-pane.active {
    display: block;
}

/* Form */
.qkb-form-row {
    margin-bottom: 20px;
}

.qkb-form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.qkb-form-row input[type="text"],
.qkb-form-row textarea,
.qkb-form-row select {
    width: 100%;
    max-width: 100%;
}

.qkb-form-row textarea {
    min-height: 100px;
}

.qkb-form-row .description {
    color: #646970;
    font-style: italic;
    margin-top: 5px;
}









/* Knowledge */
.qkb-knowledge-selector {
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.qkb-knowledge-search {
    padding: 10px;
    background: #f9f9f9;
    border-bottom: 1px solid #ddd;
}

.qkb-knowledge-search input {
    width: 100%;
}

.qkb-knowledge-list {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
}

.qkb-knowledge-item {
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.qkb-knowledge-item:last-child {
    border-bottom: none;
}

.qkb-knowledge-type {
    margin: 0 0 0 25px;
    font-size: 12px;
    color: #646970;
}

/* Suggested Prompts */
.qkb-suggested-prompts-list {
    margin-top: 15px;
}

.qkb-suggested-prompts-container {
    margin-bottom: 15px;
}

.qkb-suggested-prompt-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.qkb-suggested-prompt-item input {
    flex: 1;
}

/* Assistant Actions */
.qkb-assistant-actions-list {
    margin-top: 15px;
}

.qkb-assistant-actions-container {
    margin-bottom: 15px;
}

.qkb-assistant-action-item {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.qkb-assistant-action-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.qkb-assistant-action-name {
    flex: 1;
}

.qkb-assistant-action-content {
    margin-bottom: 15px;
}

.qkb-assistant-action-options {
    margin-bottom: 15px;
}

.qkb-assistant-action-modal-title {
    margin-bottom: 15px;
}

.qkb-remove-assistant-action {
    background: #d63638;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
}

.qkb-remove-assistant-action:hover {
    background: #b32d2e;
}


