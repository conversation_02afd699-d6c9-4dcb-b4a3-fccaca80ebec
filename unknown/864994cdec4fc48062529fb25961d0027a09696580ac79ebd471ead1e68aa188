/**
 * Modern Prompt Templates CSS
 *
 * Enhances the Q Knowledge Base prompt templates page with a modern, clean design
 */

/* Import Inter font if not already imported */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Main container styling */
.wrap {
    max-width: 1200px;
    margin: 20px auto;
    font-family: var(--qkb-font, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
}

.wrap h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 25px;
    color: var(--qkb-text-dark, #1f2328);
    padding-bottom: 15px;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
}

/* Templates list container */
.qkb-templates-list {
    margin-top: 25px;
}

/* Add New Template button */
.wrap .button.button-primary {
    background: var(--qkb-primary, #2271b1);
    border-color: var(--qkb-primary, #2271b1);
    color: #fff;
    padding: 6px 14px;
    height: auto;
    font-size: 14px;
    font-weight: 500;
    border-radius: var(--qkb-radius-sm, 6px);
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.wrap .button.button-primary:hover {
    background: var(--qkb-primary-dark, #135e96);
    border-color: var(--qkb-primary-dark, #135e96);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.wrap .button {
    padding: 6px 14px;
    height: auto;
    font-size: 14px;
    font-weight: 500;
    border-radius: var(--qkb-radius-sm, 6px);
    transition: all 0.2s ease;
}

.wrap .button:hover {
    transform: translateY(-1px);
}

/* Templates table styling */
.qkb-templates-list table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border: 1px solid var(--qkb-border, #e1e4e8);
    border-radius: var(--qkb-radius-md, 10px);
    overflow: hidden;
    box-shadow: var(--qkb-shadow-sm, 0 4px 6px rgba(0, 0, 0, 0.05));
    margin-top: 20px;
}

.qkb-templates-list table thead th {
    background: var(--qkb-bg-alt, #f8f9fa);
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
    color: var(--qkb-text-dark, #1f2328);
    font-weight: 600;
    padding: 12px 16px;
    text-align: left;
    font-size: 14px;
}

.qkb-templates-list table tbody td {
    padding: 14px 16px;
    vertical-align: middle;
    border-bottom: 1px solid var(--qkb-border-light, #f0f0f0);
    color: var(--qkb-text, #2c3338);
    font-size: 14px;
}

.qkb-templates-list table tbody tr:last-child td {
    border-bottom: none;
}

.qkb-templates-list table tbody tr:hover {
    background-color: var(--qkb-bg-hover, #f9fafb);
}

/* Action buttons in table */
.qkb-templates-list .button.button-small {
    padding: 4px 10px;
    font-size: 13px;
    margin-right: 6px;
}

/* Form styling */
.wrap form {
    background: #fff;
    border-radius: var(--qkb-radius-md, 10px);
    box-shadow: var(--qkb-shadow-sm, 0 4px 6px rgba(0, 0, 0, 0.05));
    padding: 24px;
    border: 1px solid var(--qkb-border, #e1e4e8);
    margin-top: 20px;
}

.wrap h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
    color: var(--qkb-text-dark, #1f2328);
}

/* Form table */
.form-table {
    border-collapse: collapse;
    margin-top: 0;
    width: 100%;
}

.form-table th {
    font-weight: 500;
    text-align: left;
    padding: 15px 10px 15px 0;
    width: 200px;
    vertical-align: top;
    color: var(--qkb-text, #2c3338);
    font-size: 14px;
}

.form-table td {
    padding: 15px 0;
    vertical-align: top;
}

/* Input styling */
.wrap input[type="text"],
.wrap textarea,
.wrap select {
    border: 1px solid var(--qkb-border, #e1e4e8);
    border-radius: var(--qkb-radius-sm, 6px);
    padding: 8px 12px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    width: 100%;
    max-width: 100%;
    color: var(--qkb-text, #2c3338);
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

.wrap input[type="text"]:focus,
.wrap textarea:focus,
.wrap select:focus {
    border-color: var(--qkb-primary, #2271b1);
    box-shadow: 0 0 0 1px var(--qkb-primary-light, rgba(34, 113, 177, 0.2));
    outline: none;
}

.wrap textarea {
    min-height: 120px;
}

.wrap textarea.code {
    font-family: monospace;
    font-size: 13px;
    line-height: 1.5;
}

/* Description text */
.wrap .description {
    color: var(--qkb-text-light, #646970);
    font-size: 13px;
    margin-top: 8px;
    font-style: italic;
}

.wrap .description code {
    background: var(--qkb-bg-alt, #f8f9fa);
    border: 1px solid var(--qkb-border-light, #f0f0f0);
    border-radius: 3px;
    padding: 2px 4px;
    font-size: 12px;
    color: var(--qkb-text, #2c3338);
}

/* Submit button area */
.wrap .submit {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid var(--qkb-border-light, #f0f0f0);
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
    .form-table th {
        width: 100%;
        display: block;
        padding-bottom: 0;
    }
    
    .form-table td {
        width: 100%;
        display: block;
        padding-top: 8px;
    }
    
    .wrap form {
        padding: 16px;
    }
}

/* Success message */
.wrap .notice {
    border-radius: var(--qkb-radius-sm, 6px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

/* Animation for new/updated items */
@keyframes highlightFade {
    0% { background-color: rgba(34, 113, 177, 0.1); }
    100% { background-color: transparent; }
}

.qkb-highlight {
    animation: highlightFade 2s ease-out;
}
