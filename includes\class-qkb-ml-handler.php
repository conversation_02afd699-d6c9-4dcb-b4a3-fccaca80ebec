<?php
/**
 * Machine Learning Handler for Q Knowledge Base
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include WordPress core
require_once(ABSPATH . 'wp-includes/pluggable.php');
require_once(ABSPATH . 'wp-includes/functions.php');
require_once(ABSPATH . 'wp-admin/includes/plugin.php');

class QKB_ML_Handler
{
    private static $instance = null;
    private $db;
    private $patterns_table;
    private $interactions_table;
    private $nlp_handler;

    public static function get_instance()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function __construct()
    {
        // Remove singleton check from constructor since get_instance handles it
        global $wpdb;
        $this->db = $wpdb;
        $this->patterns_table = $wpdb->prefix . 'qkb_ml_patterns';
        $this->interactions_table = $wpdb->prefix . 'qkb_ml_interactions';

        // Initialize NLP Handler with better error handling
        try {
            if (!class_exists('QKB_NLP_Handler')) {
                $nlp_handler_path = QKB_PLUGIN_DIR . 'includes/class-qkb-nlp-handler.php';
                if (!file_exists($nlp_handler_path)) {
                    throw new Exception('NLP Handler file not found at: ' . $nlp_handler_path);
                }
                require_once $nlp_handler_path;
            }

            if (!class_exists('QKB_NLP_Handler')) {
                throw new Exception('NLP Handler class not found after including file');
            }

            $this->nlp_handler = new QKB_NLP_Handler();

            if (!method_exists($this->nlp_handler, 'analyze_sentiment')) {
                throw new Exception('Required method analyze_sentiment not found in NLP Handler');
            }
        } catch (Exception $e) {
            error_log('QKB ML Handler - Failed to initialize NLP Handler: ' . $e->getMessage());
            $this->nlp_handler = null;
        }

        // Add actions
        add_action('init', array($this, 'create_tables'));
        add_action('wp_ajax_qkb_submit_feedback', array($this, 'handle_feedback'));
        add_action('wp_ajax_nopriv_qkb_submit_feedback', array($this, 'handle_feedback'));
        add_action('admin_menu', array($this, 'add_feedback_menu'), 99);
        add_action('wp_ajax_qkb_get_ml_patterns', array($this, 'ajax_get_ml_patterns'));
        add_action('wp_ajax_nopriv_qkb_get_ml_patterns', array($this, 'ajax_get_ml_patterns'));
    }

    public function handle_feedback()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        $message_id = sanitize_text_field($_POST['message_id'] ?? '');
        $feedback = intval($_POST['feedback'] ?? 0);
        $query = sanitize_text_field($_POST['query'] ?? '');
        $response = sanitize_text_field($_POST['response'] ?? '');
        $assistant_id = intval($_POST['assistant_id'] ?? 0);

        if (empty($message_id) || empty($query) || empty($response)) {
            error_log('QKB Feedback Error: Missing required fields');
            wp_send_json_error('Missing required fields');
            return;
        }

        // Verify table exists
        if (!$this->verify_table_structure()) {
            error_log('QKB Feedback Error: Interactions table structure is invalid');
            wp_send_json_error('Database error');
            return;
        }

        // Insert feedback
        $result = $this->db->insert(
            $this->interactions_table,
            [
                'message_id' => $message_id,
                'query' => $query,
                'response' => $response,
                'feedback' => $feedback,
                'assistant_id' => $assistant_id,
                'created_at' => current_time('mysql')
            ],
            ['%s', '%s', '%s', '%d', '%d', '%s']
        );

        if (!$result) {
            error_log('QKB Feedback Error: Failed to insert feedback. DB Error: ' . $this->db->last_error);
            wp_send_json_error('Failed to record feedback');
            return;
        }

        wp_send_json_success();
    }

    /**
     * Verify the feedback table structure
     *
     * Checks if the feedback table exists and has the correct structure
     *
     * @return bool True if the table structure is valid, false otherwise
     */
    public function verify_table_structure()
    {
        $table = $this->interactions_table;

        // Check if table exists
        if ($this->db->get_var("SHOW TABLES LIKE '$table'") != $table) {
            error_log("QKB Table Verification Error: Table $table does not exist");
            return false;
        }

        try {
            $columns = $this->db->get_results("SHOW COLUMNS FROM $table", ARRAY_A);

            if ($this->db->last_error) {
                error_log("QKB Table Verification Error: Failed to get columns: {$this->db->last_error}");
                return false;
            }

            if (empty($columns)) {
                error_log("QKB Table Verification Error: No columns found in table $table");
                return false;
            }

            $expected_columns = [
                'id' => [
                    'Type' => 'bigint(20)',
                    'Null' => 'NO',
                    'Key' => 'PRI',
                    'Extra' => 'auto_increment'
                ],
                'message_id' => [
                    'Type' => 'varchar(255)',
                    'Null' => 'NO'
                ],
                'query' => [
                    'Type' => 'text',
                    'Null' => 'NO'
                ],
                'response' => [
                    'Type' => 'text',
                    'Null' => 'NO'
                ],
                'feedback' => [
                    'Type' => 'int(11)',
                    'Null' => 'NO'
                ],
                'assistant_id' => [
                    'Type' => 'bigint(20)',
                    'Null' => 'NO'
                ],
                'created_at' => [
                    'Type' => 'datetime',
                    'Null' => 'NO'
                ]
            ];

            // Create a map of column names for easier lookup
            $column_map = [];
            foreach ($columns as $col) {
                $column_map[$col['Field']] = $col;
            }

            foreach ($expected_columns as $name => $expected) {
                if (!isset($column_map[$name])) {
                    error_log("QKB Table Verification Error: Missing column: $name");
                    return false;
                }

                $col = $column_map[$name];

                // Verify column properties with more flexible type checking
                // Some MySQL versions/configurations might return slightly different type formats

                // For type checking, we need to be more flexible
                $type_check = false;

                // Check if the type starts with the expected type (ignoring size)
                // For example, 'bigint' should match 'bigint(20)' and vice versa
                if (strpos($expected['Type'], '(') !== false) {
                    $base_type = substr($expected['Type'], 0, strpos($expected['Type'], '('));
                    // Check if column type starts with base type or is exactly the base type
                    $type_check = (strpos($col['Type'], $base_type) === 0);
                } else if (strpos($col['Type'], '(') !== false) {
                    // Expected type doesn't have size but column type does
                    $col_base_type = substr($col['Type'], 0, strpos($col['Type'], '('));
                    $type_check = ($col_base_type === $expected['Type']);
                } else {
                    // Neither has size specification, do direct comparison
                    $type_check = ($col['Type'] === $expected['Type']);
                }

                if (
                    !$type_check ||
                    $col['Null'] !== $expected['Null'] ||
                    (isset($expected['Key']) && $col['Key'] !== $expected['Key']) ||
                    (isset($expected['Extra']) && $col['Extra'] !== $expected['Extra'])
                ) {
                    error_log("QKB Table Verification Error: Column $name has invalid properties. Expected: " .
                        json_encode($expected) . ", Got: " . json_encode($col));
                    return false;
                }
            }

            return true;
        } catch (Exception $e) {
            error_log("QKB Table Verification Error: Exception: " . $e->getMessage());
            return false;
        }
    }

    public function create_tables()
    {
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        $charset_collate = $this->db->get_charset_collate();

        // Interactions table
        $sql = "CREATE TABLE {$this->interactions_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            message_id varchar(255) NOT NULL,
            query text NOT NULL,
            response text NOT NULL,
            feedback int(11) NOT NULL,
            assistant_id bigint(20) NOT NULL,
            created_at datetime NOT NULL,
            PRIMARY KEY  (id),
            KEY assistant_id (assistant_id),
            KEY created_at (created_at)
        ) $charset_collate;";

        try {
            // First check if table exists
            $table_exists = $this->db->get_var("SHOW TABLES LIKE '{$this->interactions_table}'") == $this->interactions_table;

            if ($table_exists) {
                // If table exists but structure is invalid, drop it first
                if (!$this->verify_table_structure()) {
                    error_log("QKB Table Creation: Table exists but has invalid structure. Dropping and recreating.");
                    $this->db->query("DROP TABLE IF EXISTS {$this->interactions_table}");
                    if ($this->db->last_error) {
                        error_log('QKB Table Drop Error: ' . $this->db->last_error);
                        return false;
                    }
                } else {
                    // Table exists and has valid structure
                    return true;
                }
            }

            // Create or recreate the table
            $result = dbDelta($sql);

            if ($this->db->last_error) {
                error_log('QKB Table Creation Error: ' . $this->db->last_error);
                return false;
            }

            // Verify table was created
            if (!$this->verify_table_structure()) {
                error_log('QKB Table Creation Failed: Table structure verification failed after creation');
                return false;
            }

            error_log('QKB Table Creation: Successfully created or updated table structure');
            return true;
        } catch (Exception $e) {
            error_log('QKB Table Creation Exception: ' . $e->getMessage());
            return false;
        }
    }

    public function learn_from_interaction($query, $response, $feedback)
    {
        $nlp_handler = new QKB_NLP_Handler();
        $query_analysis = $nlp_handler->analyze_query($query);

        $pattern_data = [
            'pattern' => $this->extract_pattern($query),
            'response' => $response,
            'feedback' => $feedback,
            'context' => [
                'intent' => $query_analysis['intent'],
                'sentiment' => $query_analysis['sentiment'],
                'keywords' => $query_analysis['keywords'],
                'timestamp' => time(),
                'session_data' => $this->get_session_context()
            ]
        ];

        // Store pattern in database
        $this->store_pattern($pattern_data);

        // Update success metrics
        $this->update_pattern_metrics($pattern_data);
    }

    private function extract_pattern($query)
    {
        // Check if NLP handler is available
        if (!$this->nlp_handler) {
            error_log('NLP Handler not available for pattern extraction');
            return strtolower(trim($query)); // Fallback to basic pattern extraction
        }

        $processed = $this->nlp_handler->preprocess_text($query);
        $keywords = $this->nlp_handler->extract_keywords($processed);

        // Create pattern template
        return implode(' ', array_map(function ($word) use ($keywords) {
            return in_array($word, $keywords) ? $word : '{*}';
        }, explode(' ', $processed)));
    }

    private function get_context()
    {
        return array(
            'url' => $_SERVER['HTTP_REFERER'] ?? '',
            'user_assistant' => $_SERVER['HTTP_USER_assistant'] ?? '',
            'timestamp' => current_time('timestamp')
        );
    }

    /**
     * Get response suggestions with fallback behavior
     */
    public function get_response_suggestions($query, $page = 1)
    {
        try {
            // Ensure page is positive
            $page = max(1, intval($page));

            $cache_key = 'qkb_suggestions_' . md5($query) . '_' . $page;
            $cached_suggestions = wp_cache_get($cache_key);

            if (false !== $cached_suggestions) {
                return $cached_suggestions;
            }

            $processed_query = $this->nlp_handler->preprocess_text($query);
            $query_sentiment = $this->nlp_handler->analyze_sentiment($query);

            // Calculate offset from page number
            $per_page = 10;
            $offset = ($page - 1) * $per_page;

            // Get patterns with validated offset
            $patterns = $this->get_patterns_from_db($offset, $per_page);

            if (empty($patterns)) {
                return [];
            }

            $suggestions = $this->process_patterns($patterns, $processed_query, $query_sentiment);

            // Sort by confidence and limit results
            usort($suggestions, function ($a, $b) {
                return $b['confidence'] <=> $a['confidence'];
            });

            $suggestions = array_slice($suggestions, 0, 5);

            wp_cache_set($cache_key, $suggestions, '', 3600);

            return $suggestions;

        } catch (Exception $e) {
            error_log('QKB ML Handler - Error getting suggestions: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get basic suggestions with database error handling
     */
    private function get_basic_suggestions($query, $page)
    {
        if (!$this->db) {
            error_log('QKB ML Handler - Database connection not initialized');
            return [];
        }

        try {
            $offset = ($page - 1) * 10;
            $patterns = $this->db->get_results(
                $this->db->prepare(
                    "SELECT pattern, response, context
                    FROM {$this->patterns_table}
                    WHERE LOWER(pattern) LIKE %s
                    ORDER BY success_count DESC
                    LIMIT %d, 10",
                    '%' . $this->db->esc_like(strtolower($query)) . '%',
                    $offset
                )
            );

            if ($patterns === null) {
                error_log('QKB ML Handler - No patterns found in database');
                return [];
            }

            return array_map(function ($pattern) {
                return [
                    'response' => $pattern->response,
                    'confidence' => 0.7,
                    'context' => json_decode($pattern->context, true)
                ];
            }, $patterns);
        } catch (Exception $e) {
            error_log('QKB ML Handler - Error getting basic suggestions: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get patterns from database with error handling
     */
    protected function get_patterns_from_db($offset = 0, $limit = 10)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'qkb_ml_patterns';

        // Ensure offset is not negative
        $offset = max(0, intval($offset));
        $limit = max(1, intval($limit));

        // Calculate success rate from success_count and fail_count
        $query = $wpdb->prepare(
            "SELECT pattern, response,
                CASE
                    WHEN (success_count + fail_count) > 0
                    THEN success_count / (success_count + fail_count)
                    ELSE 0
                END as confidence,
                usage_count
            FROM {$table_name}
            WHERE success_count > 0
            ORDER BY
                confidence DESC,
                usage_count DESC
            LIMIT %d, %d",
            $offset,
            $limit
        );

        return $wpdb->get_results($query, ARRAY_A);
    }

    /**
     * Process patterns with optimized memory usage
     */
    private function process_patterns($patterns, $processed_query, $query_sentiment)
    {
        $suggestions = [];

        foreach ($patterns as $pattern) {
            $similarity_score = $this->calculate_pattern_similarity(
                $processed_query,
                $pattern['pattern'],
                $query_sentiment,
                0 // Default sentiment score since we're not using context anymore
            );

            if ($similarity_score > 0.7) {
                $suggestions[] = [
                    'response' => $pattern['response'],
                    'confidence' => $pattern['confidence'] * $similarity_score, // Combine both scores
                    'type' => 'ml'
                ];
            }
        }

        return $suggestions;
    }

    /**
     * Calculate similarity between query and pattern
     *
     * @param string $query Processed query text
     * @param string $pattern Pattern to compare against
     * @param array $query_sentiment Query sentiment analysis
     * @param float $pattern_sentiment Pattern sentiment score
     * @return float Similarity score between 0 and 1
     */
    private function calculate_pattern_similarity($query, $pattern, $query_sentiment, $pattern_sentiment)
    {
        try {
            // Convert to lowercase and split into words
            $query_words = array_filter(explode(' ', strtolower(trim($query))));
            $pattern_words = array_filter(explode(' ', strtolower(trim($pattern))));

            if (empty($query_words) || empty($pattern_words)) {
                return 0;
            }

            // Calculate word overlap
            $matching_words = 0;
            $wildcard_matches = 0;
            $total_words = max(count($query_words), count($pattern_words));

            foreach ($query_words as $word) {
                if (in_array($word, $pattern_words)) {
                    $matching_words++;
                } elseif (in_array('{*}', $pattern_words)) {
                    $wildcard_matches++;
                }
            }

            // Base similarity on word matches and wildcards
            $word_similarity = ($matching_words + ($wildcard_matches * 0.5)) / $total_words;

            // Adjust for sentiment if available
            $sentiment_similarity = 1.0;
            if (isset($query_sentiment['score']) && isset($pattern_sentiment)) {
                $sentiment_diff = abs($query_sentiment['score'] - $pattern_sentiment);
                $sentiment_similarity = 1 - ($sentiment_diff / 2); // Normalize to 0-1 range
            }

            // Combine word and sentiment similarity
            $final_similarity = ($word_similarity * 0.7) + ($sentiment_similarity * 0.3);

            return min(1.0, max(0.0, $final_similarity));

        } catch (Exception $e) {
            error_log('Error calculating pattern similarity: ' . $e->getMessage());
            return 0;
        }
    }

    public function analyze_patterns()
    {
        return $this->db->get_results(
            "SELECT
                pattern,
                COUNT(*) as total_uses,
                AVG(success_count) as avg_success,
                AVG(fail_count) as avg_failure
            FROM {$this->patterns_table}
            GROUP BY pattern
            HAVING total_uses > 5
            ORDER BY avg_success DESC
            LIMIT 100"
        );
    }

    public function ajax_get_ml_patterns()
    {
        try {
            check_ajax_referer('qkb_ajax_nonce', 'nonce');

            $patterns = $this->db->get_results(
                "SELECT pattern, response, success_count, fail_count
                FROM {$this->patterns_table}
                WHERE success_count > fail_count
                ORDER BY success_count DESC
                LIMIT 100"
            );

            if ($patterns === null) {
                wp_send_json_error('No patterns found');
                return;
            }

            $formatted_patterns = array();
            foreach ($patterns as $pattern) {
                $formatted_patterns[$pattern->pattern] = array(
                    'response' => $pattern->response,
                    'success_rate' => $pattern->success_count / ($pattern->success_count + $pattern->fail_count),
                    'usage_count' => $pattern->success_count + $pattern->fail_count
                );
            }

            wp_send_json_success($formatted_patterns);

        } catch (Exception $e) {
            wp_send_json_error('Error retrieving ML patterns: ' . $e->getMessage());
        }
    }

    public function ajax_get_ml_suggestions()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        $query = sanitize_text_refield($_POST['query'] ?? '');
        if (empty($query)) {
            wp_send_json_error('Query is required');
            return;
        }

        $suggestions = $this->get_response_suggestions($query);
        wp_send_json_success($suggestions);
    }

    public function ajax_submit_ml_feedback()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        $message_id = sanitize_text_field($_POST['message_id'] ?? '');
        $feedback = intval($_POST['feedback'] ?? 0);
        $query = sanitize_text_field($_POST['query'] ?? '');
        $response = sanitize_text_field($_POST['response'] ?? '');

        if (empty($message_id) || empty($query) || empty($response)) {
            wp_send_json_error('Missing required fields');
            return;
        }

        $this->learn_from_interaction($query, $response, $feedback);
        wp_send_json_success();
    }

    private function enhance_pattern_analysis($query, $response, $feedback)
    {
        // Add contextual pattern learning
        $context = [
            'time_of_day' => current_time('H'),
            'day_of_week' => current_time('w'),
            'user_session_length' => $this->get_session_duration(),
            'previous_interactions' => $this->get_recent_interactions(5),
            'sentiment_score' => $this->nlp_handler->analyze_sentiment($query)
        ];

        // Store enhanced patterns with context
        $this->db->insert(
            $this->patterns_table,
            [
                'pattern' => $this->extract_pattern($query),
                'context' => json_encode($context),
                'response' => $response,
                'feedback_score' => $feedback,
                'usage_count' => 1,
                'success_rate' => $feedback > 0 ? 1 : 0
            ]
        );
    }

    public function add_feedback_menu()
    {
        // Only add menu if it doesn't exist
        global $submenu;
        if (isset($submenu['edit.php?post_type=kb_knowledge_base'])) {
            foreach ($submenu['edit.php?post_type=kb_knowledge_base'] as $item) {
                if ($item[2] === 'qkb-feedback-analytics') {
                    return; // Menu already exists
                }
            }
        }

        add_submenu_page(
            'edit.php?post_type=kb_knowledge_base',
            'Feedback Analytics',
            'Feedback Analytics',
            'manage_options',
            'qkb-feedback-analytics',
            array($this, 'render_feedback_page')
        );
    }

    public function render_feedback_page()
    {
        // Verify table exists
        if (!$this->verify_table_structure()) {
            echo '<div class="error"><p>Error: Feedback table structure is invalid. Please deactivate and reactivate the plugin.</p></div>';
            return;
        }

        // Enqueue required scripts and styles
        wp_enqueue_style('qkb-admin-feedback-css', plugin_dir_url(QKB_PLUGIN_FILE) . 'assets/css/admin-feedback.css', [], QKB_VERSION);
        wp_enqueue_script('chart-js', 'https://cdn.jsdelivr.net/npm/chart.js', [], '3.9.1', true);
        wp_enqueue_script('qkb-feedback-analytics', plugin_dir_url(QKB_PLUGIN_FILE) . 'assets/js/feedback-analytics.js', ['jquery', 'chart-js'], QKB_VERSION, true);

        // Localize script with data
        wp_localize_script('qkb-feedback-analytics', 'qkbFeedbackAnalytics', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('qkb_feedback_analytics_nonce'),
            'errorMessage' => __('An error occurred while fetching analytics data.', 'q-knowledge-base')
        ]);

        // Get assistants for filter
        $assistants = get_terms([
            'taxonomy' => 'kb_assistant',
            'hide_empty' => false
        ]);

        // Start output
        echo '<div class="wrap">';
        echo '<h1><span class="dashicons dashicons-chart-area"></span> Chatbot Feedback Analytics</h1>';
        echo '<p class="description">View and analyze user feedback for your chatbot interactions.</p>';

        // Controls and filters
        echo '<div class="qkb-controls">';

        // Date range filter
        echo '<div>';
        echo '<label for="qkb-date-range-filter"><span class="dashicons dashicons-calendar-alt"></span> Date Range:</label>';
        echo '<select id="qkb-date-range-filter" name="date_range">';
        echo '<option value="7">Last 7 Days</option>';
        echo '<option value="30" selected>Last 30 Days</option>';
        echo '<option value="90">Last 90 Days</option>';
        echo '<option value="all">All Time</option>';
        echo '</select>';
        echo '</div>';

        // Assistant filter
        echo '<div>';
        echo '<label for="qkb-assistant-filter"><span class="dashicons dashicons-admin-users"></span> Assistant:</label>';
        echo '<select id="qkb-assistant-filter" name="assistant_id">';
        echo '<option value="">All Assistants</option>';

        if (!is_wp_error($assistants) && !empty($assistants)) {
            foreach ($assistants as $assistant) {
                echo '<option value="' . esc_attr($assistant->term_id) . '">' . esc_html($assistant->name) . '</option>';
            }
        }

        echo '</select>';
        echo '</div>';

        // Search box
        echo '<div>';
        echo '<label for="qkb-feedback-search"><span class="dashicons dashicons-search"></span> Search:</label>';
        echo '<input type="text" id="qkb-feedback-search" placeholder="Search queries...">';
        echo '</div>';

        // Export options
        echo '<div class="qkb-export-options">';
        echo '<select id="qkb-export-format">';
        echo '<option value="csv">CSV</option>';
        echo '<option value="json">JSON</option>';
        echo '</select>';
        echo '<button id="qkb-export-feedback" class="button"><span class="dashicons dashicons-download"></span> Export</button>';
        echo '</div>';

        // Refresh button
        echo '<button id="qkb-refresh-analytics" class="button button-primary"><span class="dashicons dashicons-update"></span> Refresh</button>';

        // Clear feedback button
        echo '<button id="qkb-clear-feedback" class="button button-secondary" style="margin-left: 10px;"><span class="dashicons dashicons-trash"></span> Clear All Feedback</button>';

        echo '</div>'; // End controls

        // Analytics cards
        echo '<div class="qkb-analytics-grid">';

        // Total interactions
        echo '<div class="qkb-analytics-card">';
        echo '<h3><span class="dashicons dashicons-chart-bar"></span> Total Interactions</h3>';
        echo '<div class="qkb-stat" id="qkb-total-interactions">-</div>';
        echo '</div>';

        // Satisfaction rate
        echo '<div class="qkb-analytics-card">';
        echo '<h3><span class="dashicons dashicons-thumbs-up"></span> Satisfaction Rate</h3>';
        echo '<div class="qkb-stat" id="qkb-satisfaction-rate">-</div>';
        echo '</div>';

        // Feedback rate
        echo '<div class="qkb-analytics-card">';
        echo '<h3><span class="dashicons dashicons-feedback"></span> Feedback Rate</h3>';
        echo '<div class="qkb-stat" id="qkb-feedback-rate">-</div>';
        echo '</div>';

        // Average response length
        echo '<div class="qkb-analytics-card">';
        echo '<h3><span class="dashicons dashicons-editor-textcolor"></span> Avg. Response Length</h3>';
        echo '<div class="qkb-stat" id="qkb-avg-response-length">-</div>';
        echo '</div>';

        // Feedback trend chart
        echo '<div class="qkb-analytics-card" style="grid-column: span 2;">';
        echo '<h3><span class="dashicons dashicons-chart-line"></span> Feedback Trend</h3>';
        echo '<div class="qkb-chart-container"><canvas id="qkb-feedback-trend-chart"></canvas></div>';
        echo '</div>';

        // Feedback by assistant chart
        echo '<div class="qkb-analytics-card" style="grid-column: span 2;">';
        echo '<h3><span class="dashicons dashicons-groups"></span> Satisfaction by Assistant</h3>';
        echo '<div class="qkb-chart-container"><canvas id="qkb-feedback-by-assistant-chart"></canvas></div>';
        echo '</div>';

        echo '</div>'; // End analytics grid

        // Feedback table
        echo '<div class="qkb-feedback-table">';
        echo '<h2><span class="dashicons dashicons-list-view"></span> Recent Feedback</h2>';
        echo '<div id="qkb-feedback-table"><div class="spinner is-active" style="float:none;width:100%;height:100px;padding:20px 0;"></div></div>';
        echo '</div>';

        // Feedback details modal
        echo '<div id="qkb-feedback-details-modal" class="qkb-modal">';
        echo '<div class="qkb-modal-overlay"></div>';
        echo '<div class="qkb-modal-container">';
        echo '<div class="qkb-modal-header">';
        echo '<h2><span class="dashicons dashicons-visibility"></span> Feedback Details</h2>';
        echo '<span class="qkb-modal-close">&times;</span>';
        echo '</div>';
        echo '<div class="qkb-modal-content"></div>';
        echo '</div>';
        echo '</div>';

        echo '</div>'; // End wrap
    }

    private function get_feedback_analytics($current_page = 1, $per_page = 10)
    {
        // Calculate offset
        $offset = ($current_page - 1) * $per_page;

        // Get total items for pagination
        $total_items = $this->db->get_var(
            "SELECT COUNT(*) FROM {$this->interactions_table}"
        );

        // Get paginated results with markdown conversion
        $recent_feedback = $this->db->get_results(
            $this->db->prepare(
                "SELECT query, response, feedback, created_at
                FROM {$this->interactions_table}
                ORDER BY created_at DESC
                LIMIT %d OFFSET %d",
                array($per_page, $offset)
            )
        );

        // Convert markdown to HTML for each response
        foreach ($recent_feedback as &$feedback) {
            $feedback->response = $this->convert_markdown_to_html($feedback->response);
        }

        // Calculate metrics
        $total_responses = $this->db->get_var(
            "SELECT COUNT(*) FROM {$this->interactions_table}"
        );

        $positive_feedback = $this->db->get_var(
            "SELECT COUNT(*) FROM {$this->interactions_table} WHERE feedback > 0"
        );

        $responses_with_feedback = $this->db->get_var(
            "SELECT COUNT(*) FROM {$this->interactions_table} WHERE feedback != 0"
        );

        return array(
            'satisfaction_rate' => $total_responses > 0 ?
                round(($positive_feedback / $total_responses) * 100) : 0,
            'total_responses' => $total_responses,
            'feedback_rate' => $total_responses > 0 ?
                round(($responses_with_feedback / $total_responses) * 100) : 0,
            'trend' => $this->calculate_trend(),
            'recent_feedback' => $recent_feedback,
            'total_pages' => ceil($total_items / $per_page)
        );
    }

    private function calculate_trend()
    {
        $previous_period = $this->db->get_var(
            $this->db->prepare(
                "SELECT COUNT(*) FROM {$this->interactions_table}
                WHERE feedback > %d
                AND created_at BETWEEN DATE_SUB(NOW(), INTERVAL %d WEEK) AND DATE_SUB(NOW(), INTERVAL %d WEEK)",
                array(0, 2, 1)
            )
        );

        $current_period = $this->db->get_var(
            $this->db->prepare(
                "SELECT COUNT(*) FROM {$this->interactions_table}
                WHERE feedback > %d
                AND created_at >= DATE_SUB(NOW(), INTERVAL %d WEEK)",
                array(0, 1)
            )
        );

        return $previous_period > 0 ?
            round((($current_period - $previous_period) / $previous_period) * 100) : 0;
    }

    /**
     * Get feedback data grouped by assistant
     *
     * @param string $date_condition SQL date condition
     * @param string $assistant_condition SQL assistant condition
     * @return array Assistant feedback data
     */
    /**
     * Get feedback trend data for chart
     *
     * @param string $date_condition SQL date condition
     * @param string $assistant_condition SQL assistant condition
     * @return array Trend data for chart
     */
    private function get_feedback_trend_data($date_condition = '', $assistant_condition = '')
    {
        // Get data for the last 7 days
        $days = 7;
        $labels = [];
        $positive_data = [];
        $negative_data = [];

        // Generate date labels and get data for each day
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = date('M j', strtotime("-$i days"));
            $labels[] = $date;

            // Get positive feedback count for this day
            $positive = $this->db->get_var(
                "SELECT COUNT(*)
                FROM {$this->interactions_table}
                WHERE feedback > 0
                AND DATE(created_at) = DATE(DATE_SUB(NOW(), INTERVAL $i DAY))
                $assistant_condition"
            );

            // Get negative feedback count for this day
            $negative = $this->db->get_var(
                "SELECT COUNT(*)
                FROM {$this->interactions_table}
                WHERE feedback < 0
                AND DATE(created_at) = DATE(DATE_SUB(NOW(), INTERVAL $i DAY))
                $assistant_condition"
            );

            $positive_data[] = intval($positive);
            $negative_data[] = intval($negative);
        }

        return [
            'labels' => $labels,
            'positive' => $positive_data,
            'negative' => $negative_data
        ];
    }

    /**
     * Get feedback data grouped by assistant
     *
     * @param string $date_condition SQL date condition
     * @param string $assistant_condition SQL assistant condition
     * @return array Assistant feedback data
     */
    private function get_assistant_feedback_data($date_condition = '', $assistant_condition = '')
    {
        // Get assistants with feedback data
        $assistants = $this->db->get_results(
            "SELECT DISTINCT t.term_id, t.name
            FROM {$this->interactions_table} f
            JOIN {$this->db->terms} t ON f.assistant_id = t.term_id
            WHERE 1=1 $date_condition
            ORDER BY t.name ASC
            LIMIT 10"
        );

        if (empty($assistants)) {
            // Return dummy data if no assistants found
            return [
                'labels' => ['No Data'],
                'satisfaction_rates' => [0],
                'total_interactions' => [0],
                'positive_feedback' => [0]
            ];
        }

        $labels = [];
        $satisfaction_rates = [];
        $total_interactions = [];
        $positive_feedback = [];

        foreach ($assistants as $assistant) {
            // Get total interactions for this assistant
            $total = $this->db->get_var(
                $this->db->prepare(
                    "SELECT COUNT(*)
                    FROM {$this->interactions_table}
                    WHERE assistant_id = %d $date_condition",
                    $assistant->term_id
                )
            );

            // Get positive feedback count
            $positive = $this->db->get_var(
                $this->db->prepare(
                    "SELECT COUNT(*)
                    FROM {$this->interactions_table}
                    WHERE assistant_id = %d
                    AND feedback > 0 $date_condition",
                    $assistant->term_id
                )
            );

            // Calculate satisfaction rate
            $satisfaction = $total > 0 ? round(($positive / $total) * 100) : 0;

            // Add to arrays
            $labels[] = $assistant->name;
            $satisfaction_rates[] = $satisfaction;
            $total_interactions[] = intval($total);
            $positive_feedback[] = intval($positive);
        }

        return [
            'labels' => $labels,
            'satisfaction_rates' => $satisfaction_rates,
            'total_interactions' => $total_interactions,
            'positive_feedback' => $positive_feedback
        ];
    }

    private function convert_markdown_to_html($markdown)
    {
        // Basic markdown conversion
        $html = $markdown;

        // Code blocks with language
        $html = preg_replace_callback('/```(\w+)?\n(.*?)\n```/s', function ($matches) {
            $language = !empty($matches[1]) ? $matches[1] : '';
            $code = esc_html($matches[2]);
            return sprintf(
                '<pre><code class="language-%s">%s</code></pre>',
                esc_attr($language),
                $code
            );
        }, $html);

        // Inline code
        $html = preg_replace('/`(.*?)`/', '<code>$1</code>', $html);

        // Headers
        $html = preg_replace('/^### (.*?)$/m', '<h3>$1</h3>', $html);
        $html = preg_replace('/^## (.*?)$/m', '<h2>$1</h2>', $html);
        $html = preg_replace('/^# (.*?)$/m', '<h1>$1</h1>', $html);

        // Bold
        $html = preg_replace('/\*\*(.*?)\*\*/', '<strong>$1</strong>', $html);

        // Italic
        $html = preg_replace('/\*(.*?)\*/', '<em>$1</em>', $html);

        // Lists
        $html = preg_replace('/^\* (.*?)$/m', '<li>$1</li>', $html);
        $html = preg_replace('/(<li>.*?<\/li>)\s+(?=<li>)/s', '$1', $html);
        $html = preg_replace('/(<li>.*?<\/li>)+/', '<ul>$0</ul>', $html);

        // Numbered lists
        $html = preg_replace('/^\d+\. (.*?)$/m', '<li>$1</li>', $html);
        $html = preg_replace('/(<li>.*?<\/li>)\s+(?=<li>)/s', '$1', $html);
        $html = preg_replace('/(<li>.*?<\/li>)+/', '<ol>$0</ol>', $html);

        // Links
        $html = preg_replace('/\[(.*?)\]\((.*?)\)/', '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>', $html);

        // Line breaks
        $html = nl2br($html);

        return $html;
    }

    /**
     * Format response for display
     *
     * @param string $response Response text in markdown format
     * @return string Formatted HTML
     */
    private function format_response($response)
    {
        // Convert markdown to HTML
        return $this->convert_markdown_to_html($response);
    }

    /**
     * AJAX handler for getting feedback analytics data
     */
    public function ajax_get_feedback_analytics()
    {
        check_ajax_referer('qkb_feedback_analytics_nonce', 'nonce');

        // Get parameters
        $date_range = isset($_POST['date_range']) ? sanitize_text_field($_POST['date_range']) : '30';
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;

        // Date condition
        $date_condition = '';
        if ($date_range !== 'all' && intval($date_range) > 0) {
            $date_condition = $this->db->prepare(
                "AND created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)",
                intval($date_range)
            );
        }

        // Assistant condition
        $assistant_condition = '';
        if ($assistant_id > 0) {
            $assistant_condition = $this->db->prepare(
                "AND assistant_id = %d",
                $assistant_id
            );
        }

        // Get total interactions
        $total_interactions = $this->db->get_var(
            "SELECT COUNT(*) FROM {$this->interactions_table}
            WHERE 1=1 $date_condition $assistant_condition"
        );

        // Get positive feedback count
        $positive_feedback = $this->db->get_var(
            "SELECT COUNT(*) FROM {$this->interactions_table}
            WHERE feedback > 0 $date_condition $assistant_condition"
        );

        // Get feedback count (both positive and negative)
        $total_feedback = $this->db->get_var(
            "SELECT COUNT(*) FROM {$this->interactions_table}
            WHERE feedback != 0 $date_condition $assistant_condition"
        );

        // Calculate satisfaction rate
        $satisfaction_rate = $total_feedback > 0 ?
            round(($positive_feedback / $total_feedback) * 100) : 0;

        // Calculate feedback rate
        $feedback_rate = $total_interactions > 0 ?
            round(($total_feedback / $total_interactions) * 100) : 0;

        // Calculate average response length
        $avg_response_length = $this->db->get_var(
            "SELECT AVG(LENGTH(response)) FROM {$this->interactions_table}
            WHERE 1=1 $date_condition $assistant_condition"
        );

        // Calculate satisfaction trend
        $satisfaction_trend = $this->calculate_trend();

        // Get trend data for chart
        $trend_data = $this->get_feedback_trend_data($date_condition, $assistant_condition);

        // Get assistant data for chart
        $assistant_data = $this->get_assistant_feedback_data($date_condition, $assistant_condition);

        // Prepare response
        $response = [
            'summary' => [
                'total_interactions' => intval($total_interactions),
                'satisfaction_rate' => floatval($satisfaction_rate),
                'feedback_rate' => floatval($feedback_rate),
                'avg_response_length' => round(floatval($avg_response_length)),
                'satisfaction_trend' => intval($satisfaction_trend)
            ],
            'trend_data' => $trend_data,
            'assistant_data' => $assistant_data
        ];

        wp_send_json_success($response);
    }

    /**
     * AJAX handler for getting feedback table
     */
    public function ajax_get_feedback_table()
    {
        check_ajax_referer('qkb_feedback_analytics_nonce', 'nonce');

        // Get parameters
        $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
        $date_range = isset($_POST['date_range']) ? sanitize_text_field($_POST['date_range']) : '30';
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;
        $search = isset($_POST['search']) ? sanitize_text_field($_POST['search']) : '';

        // Items per page
        $per_page = 10;

        // Calculate offset
        $offset = ($page - 1) * $per_page;

        // Date condition
        $date_condition = '';
        if ($date_range !== 'all' && intval($date_range) > 0) {
            $date_condition = $this->db->prepare(
                "AND f.created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)",
                intval($date_range)
            );
        }

        // Assistant condition
        $assistant_condition = '';
        if ($assistant_id > 0) {
            $assistant_condition = $this->db->prepare(
                "AND f.assistant_id = %d",
                $assistant_id
            );
        }

        // Assistant condition
        $assistant_condition = '';
        if ($assistant_id > 0) {
            $assistant_condition = $this->db->prepare(
                "AND f.assistant_id = %d",
                $assistant_id
            );
        }

        // Search condition
        $search_condition = '';
        if (!empty($search)) {
            $search_condition = $this->db->prepare(
                "AND (f.query LIKE %s OR f.response LIKE %s)",
                array('%' . $this->db->esc_like($search) . '%', '%' . $this->db->esc_like($search) . '%')
            );
        }

        // Get total items for pagination
        $total_items = $this->db->get_var(
            "SELECT COUNT(*) FROM {$this->interactions_table} f
            WHERE 1=1 $date_condition $assistant_condition $search_condition"
        );

        // Get paginated results
        $results = $this->db->get_results(
            $this->db->prepare(
                "SELECT f.*, t.name as assistant_name
                FROM {$this->interactions_table} f
                LEFT JOIN {$this->db->terms} t ON f.assistant_id = t.term_id
                WHERE 1=1 $date_condition $assistant_condition $search_condition
                ORDER BY f.created_at DESC
                LIMIT %d OFFSET %d",
                array($per_page, $offset)
            )
        );

        // Start output buffer
        ob_start();

        if (empty($results)) {
            echo '<div class="qkb-no-data">
                <span class="dashicons dashicons-info-outline"></span>
                <p>No feedback data available for the selected filters.</p>
            </div>';
        } else {
            // Table
            echo '<table class="wp-list-table widefat fixed striped">';
            echo '<thead><tr>
                <th>Date</th>
                <th>Assistant</th>
                <th>Query</th>
                <th>Response</th>
                <th>Feedback</th>
                <th>Actions</th>
            </tr></thead>';

            echo '<tbody>';
            foreach ($results as $row) {
                $feedback_class = $row->feedback > 0 ? 'positive' : ($row->feedback < 0 ? 'negative' : '');
                $feedback_icon = $row->feedback > 0 ? '👍' : ($row->feedback < 0 ? '👎' : '—');
                $feedback_text = $row->feedback > 0 ? 'Positive' : ($row->feedback < 0 ? 'Negative' : 'None');

                echo "<tr>
                    <td data-label=\"Date\">{$row->created_at}</td>
                    <td data-label=\"Assistant\">{$row->assistant_name}</td>
                    <td data-label=\"Query\">{$row->query}</td>
                    <td class='qkb-response-cell' data-label=\"Response\">{$this->format_response($row->response)}</td>
                    <td data-label=\"Feedback\"><span class='qkb-feedback-indicator {$feedback_class}'>{$feedback_icon} {$feedback_text}</span></td>
                    <td data-label=\"Actions\">
                        <a href=\"#\" class=\"qkb-action-btn view qkb-view-details\" data-id=\"{$row->id}\"><span class=\"dashicons dashicons-visibility\"></span> View</a>
                    </td>
                </tr>";
            }
            echo '</tbody>';

            echo '</table>';

            // Pagination
            $total_pages = ceil($total_items / $per_page);

            if ($total_pages > 1) {
                echo '<div class="qkb-pagination">';

                // Previous page
                if ($page > 1) {
                    echo '<a href="#" data-page="' . ($page - 1) . '" aria-label="Previous page"><span class="dashicons dashicons-arrow-left-alt2"></span></a>';
                } else {
                    echo '<span class="qkb-pagination-disabled"><span class="dashicons dashicons-arrow-left-alt2"></span></span>';
                }

                // Page numbers
                $start_page = max(1, $page - 2);
                $end_page = min($total_pages, $page + 2);

                // First page
                if ($start_page > 1) {
                    echo '<a href="#" data-page="1">1</a>';
                    if ($start_page > 2) {
                        echo '<span class="qkb-pagination-ellipsis">...</span>';
                    }
                }

                // Page numbers
                for ($i = $start_page; $i <= $end_page; $i++) {
                    $class = $i === $page ? 'current' : '';
                    echo '<a href="#" data-page="' . $i . '" class="' . $class . '">' . $i . '</a>';
                }

                // Last page
                if ($end_page < $total_pages) {
                    if ($end_page < $total_pages - 1) {
                        echo '<span class="qkb-pagination-ellipsis">...</span>';
                    }
                    echo '<a href="#" data-page="' . $total_pages . '">' . $total_pages . '</a>';
                }

                // Next page
                if ($page < $total_pages) {
                    echo '<a href="#" data-page="' . ($page + 1) . '" aria-label="Next page"><span class="dashicons dashicons-arrow-right-alt2"></span></a>';
                } else {
                    echo '<span class="qkb-pagination-disabled"><span class="dashicons dashicons-arrow-right-alt2"></span></span>';
                }

                echo '</div>';
            }
        }

        // Get output buffer content
        $html = ob_get_clean();

        // Prepare response
        $response = [
            'html' => $html,
            'total_items' => $total_items,
            'total_pages' => ceil($total_items / $per_page),
            'current_page' => $page
        ];

        wp_send_json_success($response);
    }

    /**
     * AJAX handler for getting feedback details
     */
    public function ajax_get_feedback_details()
    {
        check_ajax_referer('qkb_feedback_analytics_nonce', 'nonce');

        // Get parameters
        $feedback_id = isset($_POST['feedback_id']) ? intval($_POST['feedback_id']) : 0;

        if ($feedback_id <= 0) {
            wp_send_json_error(['message' => 'Invalid feedback ID']);
            return;
        }

        // Get feedback details
        $feedback = $this->db->get_row(
            $this->db->prepare(
                "SELECT f.*, t.name as assistant_name
                FROM {$this->interactions_table} f
                LEFT JOIN {$this->db->terms} t ON f.assistant_id = t.term_id
                WHERE f.id = %d",
                $feedback_id
            )
        );

        if (!$feedback) {
            wp_send_json_error(['message' => 'Feedback not found']);
            return;
        }

        // Start output buffer
        ob_start();

        // Feedback details
        echo '<div class="qkb-feedback-details">';

        // Metadata
        echo '<div class="qkb-feedback-meta">';
        echo '<p><strong><span class="dashicons dashicons-calendar-alt"></span> Date:</strong> ' . $feedback->created_at . '</p>';
        echo '<p><strong><span class="dashicons dashicons-admin-users"></span> Assistant:</strong> ' . $feedback->assistant_name . '</p>';
        echo '<p><strong><span class="dashicons dashicons-id"></span> Message ID:</strong> ' . $feedback->message_id . '</p>';

        // Feedback indicator
        $feedback_class = $feedback->feedback > 0 ? 'positive' : ($feedback->feedback < 0 ? 'negative' : '');
        $feedback_icon = $feedback->feedback > 0 ? '👍' : ($feedback->feedback < 0 ? '👎' : '—');
        $feedback_text = $feedback->feedback > 0 ? 'Positive' : ($feedback->feedback < 0 ? 'Negative' : 'None');
        echo '<p><strong><span class="dashicons dashicons-feedback"></span> Feedback:</strong> <span class="qkb-feedback-indicator ' . $feedback_class . '">' . $feedback_icon . ' ' . $feedback_text . '</span></p>';
        echo '</div>';

        // Query and response
        echo '<div class="qkb-feedback-content">';
        echo '<h3><span class="dashicons dashicons-editor-help"></span> Query</h3>';
        echo '<div class="qkb-feedback-query">' . $feedback->query . '</div>';

        echo '<h3><span class="dashicons dashicons-editor-textcolor"></span> Response</h3>';
        echo '<div class="qkb-feedback-response">' . $this->format_response($feedback->response) . '</div>';
        echo '</div>';

        echo '</div>';

        // Get output buffer content
        $html = ob_get_clean();

        // Prepare response
        $response = [
            'html' => $html
        ];

        wp_send_json_success($response);
    }

    /**
     * AJAX handler for exporting feedback data
     */
    public function ajax_export_feedback()
    {
        check_ajax_referer('qkb_feedback_analytics_nonce', 'nonce');

        // Get parameters
        $date_range = isset($_POST['date_range']) ? sanitize_text_field($_POST['date_range']) : '30';
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;
        $format = isset($_POST['format']) ? sanitize_text_field($_POST['format']) : 'csv';

        // Date condition
        $date_condition = '';
        if ($date_range !== 'all' && intval($date_range) > 0) {
            $date_condition = $this->db->prepare(
                "AND f.created_at >= DATE_SUB(NOW(), INTERVAL %d DAY)",
                intval($date_range)
            );
        }

        // Assistant condition
        $assistant_condition = '';
        if ($assistant_id > 0) {
            $assistant_condition = $this->db->prepare(
                "AND f.assistant_id = %d",
                $assistant_id
            );
        }

        // Get feedback data
        $results = $this->db->get_results(
            "SELECT f.*, t.name as assistant_name
            FROM {$this->interactions_table} f
            LEFT JOIN {$this->db->terms} t ON f.assistant_id = t.term_id
            WHERE 1=1 $date_condition $assistant_condition
            ORDER BY f.created_at DESC"
        );

        if (empty($results)) {
            wp_die('No feedback data available for export.');
            return;
        }

        // Export based on format
        if ($format === 'csv') {
            $this->export_feedback_csv($results);
        } else {
            $this->export_feedback_json($results);
        }

        exit;
    }

    /**
     * Export feedback data as CSV
     *
     * @param array $results Feedback data
     */
    private function export_feedback_csv($results)
    {
        // Set headers
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="feedback-export-' . date('Y-m-d') . '.csv"');

        // Create file pointer
        $output = fopen('php://output', 'w');

        // Add BOM for UTF-8
        fputs($output, "\xEF\xBB\xBF");

        // Add CSV headers
        fputcsv($output, ['ID', 'Date', 'Assistant', 'Message ID', 'Query', 'Response', 'Feedback']);

        // Add data rows
        foreach ($results as $row) {
            $feedback_text = $row->feedback > 0 ? 'Positive' : ($row->feedback < 0 ? 'Negative' : 'None');

            fputcsv($output, [
                $row->id,
                $row->created_at,
                $row->assistant_name,
                $row->message_id,
                $row->query,
                $row->response,
                $feedback_text
            ]);
        }

        // Close file pointer
        fclose($output);
    }

    /**
     * Export feedback data as JSON
     *
     * @param array $results Feedback data
     */
    private function export_feedback_json($results)
    {
        // Set headers
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="feedback-export-' . date('Y-m-d') . '.json"');

        // Prepare data
        $data = [];

        foreach ($results as $row) {
            $feedback_text = $row->feedback > 0 ? 'Positive' : ($row->feedback < 0 ? 'Negative' : 'None');

            $data[] = [
                'id' => $row->id,
                'date' => $row->created_at,
                'assistant' => $row->assistant_name,
                'message_id' => $row->message_id,
                'query' => $row->query,
                'response' => $row->response,
                'feedback' => $feedback_text
            ];
        }

        // Output JSON
        echo json_encode($data, JSON_PRETTY_PRINT);
    }

    public function maybe_upgrade_database()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'qkb_ml_patterns';
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            pattern text NOT NULL,
            response text NOT NULL,
            success_rate float DEFAULT 0,
            usage_count int DEFAULT 0,
            assistant_id bigint(20) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            KEY assistant_id (assistant_id),
            KEY success_rate (success_rate)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Retrieve a pattern-based response for a given query.
     *
     * @param string $query The user query.
     * @return string|false The stored response if a matching pattern is found; false otherwise.
     */
    public function get_pattern_response($query)
    {
        $threshold = 0.8; // Only consider patterns with a high success rate.
        $sql = $this->db->prepare("SELECT pattern, response FROM {$this->patterns_table} WHERE success_rate >= %f", $threshold);
        $results = $this->db->get_results($sql, ARRAY_A);
        if (!empty($results)) {
            foreach ($results as $row) {
                // A simple check: if the query loosely matches the stored pattern.
                if (stripos($query, $row['pattern']) !== false) {
                    return $row['response'];
                }
            }
        }
        return false;
    }

    /**
     * Record user feedback for a given interaction to update learning.
     *
     * @param int   $interaction_id The ID of the interaction.
     * @param float $feedback_score A numeric feedback score (e.g., from 0 to 1).
     */
    public function record_interaction_feedback($interaction_id, $feedback_score)
    {
        if (!$this->verify_table_structure()) {
            return false;
        }

        $table = $this->interactions_table;
        // Fetch existing interaction data
        $interaction = $this->db->get_row($this->db->prepare(
            "SELECT query, response, assistant_id, success_rate, usage_count
            FROM $table WHERE id = %d",
            $interaction_id
        ));

        if (!$interaction) {
            return false;
        }

        // Calculate new metrics
        $new_usage = $interaction->usage_count + 1;
        $new_success_rate = (($interaction->success_rate * $interaction->usage_count) + $feedback_score) / $new_usage;

        // Enhanced analytics data
        $analytics_data = [
            'query_length' => strlen($interaction->query),
            'response_length' => strlen($interaction->response),
            'query_complexity' => $this->analyze_query_complexity($interaction->query),
            'response_sentiment' => $this->nlp_handler->analyze_sentiment($interaction->response),
            'interaction_duration' => isset($_POST['duration']) ? intval($_POST['duration']) : 0,
            'user_corrections' => isset($_POST['corrections']) ? intval($_POST['corrections']) : 0
        ];

        // Update interaction record with new metrics
        $this->db->update(
            $table,
            [
                'success_rate' => $new_success_rate,
                'usage_count' => $new_usage,
                'analytics_data' => json_encode($analytics_data),
                'updated_at' => current_time('mysql')
            ],
            ['id' => $interaction_id],
            ['%f', '%d', '%s', '%s'],
            ['%d']
        );

        // Trigger pattern learning for high-quality interactions
        if ($feedback_score > 0.8) {
            $this->learn_from_successful_interaction($interaction->query, $interaction->response, $analytics_data);
        }

        return true;
    }

    private function analyze_query_complexity($query)
    {
        $metrics = [
            'word_count' => str_word_count($query),
            'sentence_count' => preg_match_all('/[.!?]+/', $query, $matches) + 1,
            'technical_terms' => $this->count_technical_terms($query),
            'question_type' => $this->classify_question_type($query)
        ];

        // Calculate complexity score (0-1)
        $complexity = min(1, (
            ($metrics['word_count'] / 20) +
            ($metrics['technical_terms'] / 5) +
            ($metrics['sentence_count'] / 3)
        ) / 3);

        return [
            'score' => $complexity,
            'metrics' => $metrics
        ];
    }

    private function learn_from_successful_interaction($query, $response, $analytics_data)
    {
        // Extract patterns from successful interactions
        $pattern_data = [
            'pattern' => $this->extract_pattern($query),
            'response_template' => $this->extract_response_template($response),
            'context' => [
                'complexity' => $analytics_data['query_complexity']['score'],
                'sentiment' => $analytics_data['response_sentiment'],
                'interaction_metrics' => [
                    'duration' => $analytics_data['interaction_duration'],
                    'corrections' => $analytics_data['user_corrections']
                ]
            ]
        ];

        // Store pattern for future use
        $this->store_pattern($pattern_data);
    }

    /**
     * Force recreation of tables
     *
     * This method will drop and recreate the feedback tables
     *
     * @return bool True if tables were successfully recreated, false otherwise
     */
    public function recreate_tables()
    {
        try {
            // Drop existing tables
            $this->db->query("DROP TABLE IF EXISTS {$this->interactions_table}");

            if ($this->db->last_error) {
                error_log('QKB Table Recreation Error: Failed to drop table: ' . $this->db->last_error);
                return false;
            }

            // Create new tables
            $result = $this->create_tables();

            if ($result) {
                error_log('QKB Table Recreation: Successfully recreated tables');
            } else {
                error_log('QKB Table Recreation: Failed to recreate tables');
            }

            return $result;
        } catch (Exception $e) {
            error_log('QKB Table Recreation Exception: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * AJAX handler for clearing all feedback data
     */
    public function ajax_clear_feedback()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        // Verify nonce
        check_ajax_referer('qkb_feedback_analytics_nonce', 'nonce');

        try {
            // Get count before deletion for confirmation
            $count_before = $this->db->get_var("SELECT COUNT(*) FROM {$this->interactions_table}");

            // Clear all feedback data
            $result = $this->db->query("DELETE FROM {$this->interactions_table}");

            if ($result !== false) {
                // Log the action
                error_log("QKB Feedback Clear: User " . get_current_user_id() . " cleared $count_before feedback records");

                wp_send_json_success([
                    'message' => sprintf(__('Successfully cleared %d feedback records.', 'q-knowledge-base'), $count_before),
                    'cleared_count' => $count_before
                ]);
            } else {
                error_log("QKB Feedback Clear: Failed to clear feedback data - " . $this->db->last_error);
                wp_send_json_error(__('Failed to clear feedback data. Please try again.', 'q-knowledge-base'));
            }
        } catch (Exception $e) {
            error_log("QKB Feedback Clear Exception: " . $e->getMessage());
            wp_send_json_error(__('An error occurred while clearing feedback data.', 'q-knowledge-base'));
        }
    }
}