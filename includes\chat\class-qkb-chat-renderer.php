<?php
/**
 * QKB Chat Renderer Base Class
 *
 * Base class for all chat rendering components
 */

if (!defined('ABSPATH')) {
    exit;
}

abstract class QKB_Chat_Renderer
{
    /**
     * Common settings for all renderers
     */
    protected $settings = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->init_settings();
    }

    /**
     * Initialize settings
     *
     * Base method that can be overridden by child classes
     */
    protected function init_settings()
    {
        $this->settings = [];
    }

    /**
     * Get default assistant
     *
     * @return object|false WP_Term object or false
     */
    protected function get_default_assistant()
    {
        return get_term_by('slug', 'knowledge-base', 'kb_assistant');
    }

    /**
     * Get default assistant ID
     *
     * @return int Default assistant ID
     */
    protected function get_default_assistant_id()
    {
        $default_assistant = $this->get_default_assistant();
        return $default_assistant ? $default_assistant->term_id : 0;
    }

    /**
     * Render message templates
     *
     * Common templates used by both chatbot and chat interface
     */
    protected function render_message_templates()
    {
        ?>
        <template id="qkb-user-message-template">
            <div class="qkb-message qkb-user-message">
                <div class="qkb-message-content"></div>
                <div class="qkb-message-timestamp" aria-hidden="true"></div>
            </div>
        </template>

        <template id="qkb-bot-message-template">
            <div class="qkb-message qkb-bot-message">
                <div class="qkb-message-content"></div>
                <div class="qkb-message-timestamp" aria-hidden="true"></div>
                <div class="qkb-message-feedback">
                    <div class="qkb-feedback-prompt">Was this helpful?</div>
                    <div class="qkb-feedback-buttons">
                        <button class="qkb-feedback-btn" data-value="helpful" aria-label="Mark as helpful">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path
                                    d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3">
                                </path>
                            </svg>
                        </button>
                        <button class="qkb-feedback-btn" data-value="not_helpful" aria-label="Mark as not helpful">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path
                                    d="M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h3a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2h-3">
                                </path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </template>

        <template id="qkb-error-message-template">
            <div class="qkb-message qkb-error-message">
                <div class="qkb-message-content">
                    <?php echo esc_html__('Message exceeds 500 character limit', 'q-knowledge-base'); ?>
                </div>
            </div>
        </template>
        <?php
    }

    /**
     * Convert hex color to RGB
     *
     * @param string $hex Hex color code
     * @return array RGB values
     */
    protected function hex2rgb($hex)
    {
        $hex = str_replace('#', '', $hex);

        if (strlen($hex) == 3) {
            $r = hexdec(substr($hex, 0, 1) . substr($hex, 0, 1));
            $g = hexdec(substr($hex, 1, 1) . substr($hex, 1, 1));
            $b = hexdec(substr($hex, 2, 1) . substr($hex, 2, 1));
        } else {
            $r = hexdec(substr($hex, 0, 2));
            $g = hexdec(substr($hex, 2, 2));
            $b = hexdec(substr($hex, 4, 2));
        }

        return ['r' => $r, 'g' => $g, 'b' => $b];
    }

    /**
     * Adjust brightness of a hex color
     *
     * @param string $hex Hex color code
     * @param int $steps Steps to adjust brightness
     * @return string Adjusted hex color
     */
    protected function adjustBrightness($hex, $steps)
    {
        $hex = str_replace('#', '', $hex);

        $r = max(0, min(255, hexdec(substr($hex, 0, 2)) + $steps));
        $g = max(0, min(255, hexdec(substr($hex, 2, 2)) + $steps));
        $b = max(0, min(255, hexdec(substr($hex, 4, 2)) + $steps));

        return sprintf("#%02x%02x%02x", $r, $g, $b);
    }

    /**
     * Abstract method that must be implemented by child classes
     */
    abstract public function render();
}
