/**
 * Performance testing suite for Q-Knowledge Base chat interface
 * Tests various performance metrics and optimizations
 */
class QKBPerformanceTester {
    constructor() {
        this.metrics = {
            responseTime: [],
            renderTime: [],
            memoryUsage: [],
            domOperations: [],
            cacheHitRate: 0,
            totalRequests: 0,
            cachedRequests: 0
        };
        
        this.testResults = {};
        this.isRunning = false;
    }
    
    /**
     * Initialize performance testing
     */
    init() {
        console.log('🚀 QKB Performance Tester initialized');
        this.setupPerformanceObserver();
        this.setupMemoryMonitoring();
        this.setupNetworkMonitoring();
    }
    
    /**
     * Setup Performance Observer for monitoring
     */
    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            // Monitor navigation timing
            const navObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'navigation') {
                        this.recordMetric('pageLoad', {
                            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
                            loadComplete: entry.loadEventEnd - entry.loadEventStart,
                            totalTime: entry.loadEventEnd - entry.fetchStart
                        });
                    }
                }
            });
            navObserver.observe({ entryTypes: ['navigation'] });
            
            // Monitor resource timing
            const resourceObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.name.includes('qkb') || entry.name.includes('chat')) {
                        this.recordMetric('resourceLoad', {
                            name: entry.name,
                            duration: entry.duration,
                            transferSize: entry.transferSize || 0
                        });
                    }
                }
            });
            resourceObserver.observe({ entryTypes: ['resource'] });
        }
    }
    
    /**
     * Setup memory monitoring
     */
    setupMemoryMonitoring() {
        if ('memory' in performance) {
            setInterval(() => {
                this.recordMetric('memory', {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit
                });
            }, 5000); // Every 5 seconds
        }
    }
    
    /**
     * Setup network monitoring
     */
    setupNetworkMonitoring() {
        // Override fetch to monitor AJAX requests
        const originalFetch = window.fetch;
        window.fetch = (...args) => {
            const startTime = performance.now();
            this.metrics.totalRequests++;
            
            return originalFetch.apply(this, args).then(response => {
                const endTime = performance.now();
                this.recordMetric('networkRequest', {
                    url: args[0],
                    duration: endTime - startTime,
                    status: response.status,
                    cached: response.headers.get('x-cache') === 'HIT'
                });
                
                if (response.headers.get('x-cache') === 'HIT') {
                    this.metrics.cachedRequests++;
                }
                
                return response;
            });
        };
    }
    
    /**
     * Record a performance metric
     */
    recordMetric(type, data) {
        if (!this.metrics[type]) {
            this.metrics[type] = [];
        }
        
        this.metrics[type].push({
            ...data,
            timestamp: Date.now()
        });
        
        // Keep only last 100 entries to prevent memory issues
        if (this.metrics[type].length > 100) {
            this.metrics[type] = this.metrics[type].slice(-100);
        }
    }
    
    /**
     * Test chat response performance
     */
    async testChatResponse() {
        console.log('🧪 Testing chat response performance...');
        
        const testMessages = [
            'Hello, how are you?',
            'What is artificial intelligence?',
            'Can you help me with a complex technical question about database optimization?',
            'Explain quantum computing in simple terms',
            'What are the best practices for web development?'
        ];
        
        const results = [];
        
        for (const message of testMessages) {
            const startTime = performance.now();
            
            try {
                // Simulate chat request
                const response = await this.simulateChatRequest(message);
                const endTime = performance.now();
                
                results.push({
                    message: message.substring(0, 50) + '...',
                    responseTime: endTime - startTime,
                    success: true,
                    responseLength: response.length
                });
                
            } catch (error) {
                const endTime = performance.now();
                results.push({
                    message: message.substring(0, 50) + '...',
                    responseTime: endTime - startTime,
                    success: false,
                    error: error.message
                });
            }
            
            // Wait between requests to avoid rate limiting
            await this.sleep(1000);
        }
        
        this.testResults.chatResponse = results;
        return results;
    }
    
    /**
     * Test DOM rendering performance
     */
    async testDOMRendering() {
        console.log('🧪 Testing DOM rendering performance...');
        
        const results = [];
        const testContainer = document.createElement('div');
        testContainer.style.position = 'absolute';
        testContainer.style.top = '-9999px';
        document.body.appendChild(testContainer);
        
        // Test message rendering
        for (let i = 0; i < 50; i++) {
            const startTime = performance.now();
            
            const messageElement = document.createElement('div');
            messageElement.className = 'qi-message qi-assistant-message';
            messageElement.innerHTML = `
                <div class="qi-message-content">
                    <p>This is test message ${i} with some <strong>formatting</strong> and <em>styling</em>.</p>
                    <ul>
                        <li>List item 1</li>
                        <li>List item 2</li>
                    </ul>
                </div>
            `;
            
            testContainer.appendChild(messageElement);
            
            const endTime = performance.now();
            results.push({
                messageIndex: i,
                renderTime: endTime - startTime
            });
        }
        
        // Cleanup
        document.body.removeChild(testContainer);
        
        this.testResults.domRendering = results;
        return results;
    }
    
    /**
     * Test cache performance
     */
    async testCachePerformance() {
        console.log('🧪 Testing cache performance...');
        
        const results = {
            hitRate: 0,
            averageHitTime: 0,
            averageMissTime: 0
        };
        
        if (this.metrics.totalRequests > 0) {
            results.hitRate = (this.metrics.cachedRequests / this.metrics.totalRequests) * 100;
        }
        
        // Test local cache if available
        if (window.qiChat && window.qiChat.requestCache) {
            const cache = window.qiChat.requestCache;
            const cacheSize = cache.size;
            
            results.cacheSize = cacheSize;
            results.cacheUtilization = (cacheSize / 50) * 100; // Assuming max 50 entries
        }
        
        this.testResults.cachePerformance = results;
        return results;
    }
    
    /**
     * Run comprehensive performance test suite
     */
    async runFullTestSuite() {
        if (this.isRunning) {
            console.warn('Performance test already running');
            return;
        }
        
        this.isRunning = true;
        console.log('🚀 Starting comprehensive performance test suite...');
        
        try {
            // Test chat response performance
            await this.testChatResponse();
            
            // Test DOM rendering performance
            await this.testDOMRendering();
            
            // Test cache performance
            await this.testCachePerformance();
            
            // Generate performance report
            const report = this.generatePerformanceReport();
            
            console.log('✅ Performance test suite completed');
            console.table(report.summary);
            
            return report;
            
        } catch (error) {
            console.error('❌ Performance test suite failed:', error);
            throw error;
        } finally {
            this.isRunning = false;
        }
    }
    
    /**
     * Generate performance report
     */
    generatePerformanceReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {},
            details: this.testResults,
            recommendations: []
        };
        
        // Chat response performance
        if (this.testResults.chatResponse) {
            const responseTimes = this.testResults.chatResponse
                .filter(r => r.success)
                .map(r => r.responseTime);
            
            if (responseTimes.length > 0) {
                const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
                const maxResponseTime = Math.max(...responseTimes);
                
                report.summary['Avg Response Time'] = `${avgResponseTime.toFixed(2)}ms`;
                report.summary['Max Response Time'] = `${maxResponseTime.toFixed(2)}ms`;
                
                if (avgResponseTime > 3000) {
                    report.recommendations.push('Consider optimizing backend processing - average response time is high');
                }
            }
        }
        
        // DOM rendering performance
        if (this.testResults.domRendering) {
            const renderTimes = this.testResults.domRendering.map(r => r.renderTime);
            const avgRenderTime = renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length;
            
            report.summary['Avg Render Time'] = `${avgRenderTime.toFixed(2)}ms`;
            
            if (avgRenderTime > 10) {
                report.recommendations.push('Consider optimizing DOM operations - rendering is slow');
            }
        }
        
        // Cache performance
        if (this.testResults.cachePerformance) {
            const cache = this.testResults.cachePerformance;
            report.summary['Cache Hit Rate'] = `${cache.hitRate.toFixed(1)}%`;
            
            if (cache.hitRate < 50) {
                report.recommendations.push('Consider improving cache strategy - hit rate is low');
            }
        }
        
        // Memory usage
        if (this.metrics.memory && this.metrics.memory.length > 0) {
            const latestMemory = this.metrics.memory[this.metrics.memory.length - 1];
            const memoryUsageMB = (latestMemory.used / 1024 / 1024).toFixed(2);
            
            report.summary['Memory Usage'] = `${memoryUsageMB}MB`;
            
            if (latestMemory.used > 100 * 1024 * 1024) { // 100MB
                report.recommendations.push('High memory usage detected - consider memory optimization');
            }
        }
        
        return report;
    }
    
    /**
     * Simulate a chat request for testing
     */
    async simulateChatRequest(message) {
        // This would normally make an actual AJAX request
        // For testing, we'll simulate the delay and response
        await this.sleep(Math.random() * 2000 + 500); // 500-2500ms delay
        
        return `This is a simulated response to: ${message}`;
    }
    
    /**
     * Utility sleep function
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * Export test results
     */
    exportResults() {
        const data = {
            metrics: this.metrics,
            testResults: this.testResults,
            timestamp: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `qkb-performance-test-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
    }
}

// Initialize performance tester when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.qkbPerformanceTester = new QKBPerformanceTester();
    window.qkbPerformanceTester.init();
    
    // Add global functions for easy testing
    window.runPerformanceTest = () => window.qkbPerformanceTester.runFullTestSuite();
    window.exportPerformanceResults = () => window.qkbPerformanceTester.exportResults();
    
    console.log('🔧 QKB Performance Tester ready. Run window.runPerformanceTest() to start testing.');
});
