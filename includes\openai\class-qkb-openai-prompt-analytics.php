<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for handling prompt analytics
 */
class QKB_OpenAI_Prompt_Analytics {
    private $db;
    private $analytics_table;
    private $usage_table;

    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->db = $wpdb;
        $this->analytics_table = $wpdb->prefix . 'qkb_prompt_analytics';
        $this->usage_table = $wpdb->prefix . 'qkb_prompt_usage';

        // Create tables if they don't exist
        $this->create_tables();

        // Add hooks
        add_action('admin_menu', [$this, 'add_analytics_page']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_scripts']);
        add_action('wp_ajax_qkb_get_prompt_analytics', [$this, 'ajax_get_analytics']);
        add_action('qkb_after_prompt_used', [$this, 'track_prompt_usage'], 10, 4);
    }

    /**
     * Create database tables
     */
    public function create_tables() {
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        $charset_collate = $this->db->get_charset_collate();

        // Analytics table - aggregated metrics
        $sql = "CREATE TABLE IF NOT EXISTS {$this->analytics_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            prompt_id varchar(255) NOT NULL,
            prompt_type varchar(50) NOT NULL,
            total_uses int(11) DEFAULT 0,
            avg_rating float DEFAULT 0,
            success_rate float DEFAULT 0,
            avg_response_time int(11) DEFAULT 0,
            last_updated datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY prompt_id (prompt_id),
            KEY prompt_type (prompt_type),
            KEY success_rate (success_rate)
        ) $charset_collate;";

        dbDelta($sql);

        // Usage table - individual usage records
        $sql = "CREATE TABLE IF NOT EXISTS {$this->usage_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            prompt_id varchar(255) NOT NULL,
            prompt_type varchar(50) NOT NULL,
            prompt_version int(11) DEFAULT 1,
            query text NOT NULL,
            response text NOT NULL,
            rating int(11) DEFAULT 0,
            response_time int(11) DEFAULT 0,
            tokens_used int(11) DEFAULT 0,
            user_id bigint(20) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY prompt_id (prompt_id),
            KEY prompt_type (prompt_type),
            KEY prompt_version (prompt_version),
            KEY created_at (created_at)
        ) $charset_collate;";

        dbDelta($sql);
    }

    /**
     * Add analytics page to admin menu
     */
    public function add_analytics_page() {
        add_submenu_page(
            'edit.php?post_type=kb_knowledge_base',
            __('Prompt Analytics', 'q-knowledge-base'),
            __('Prompt Analytics', 'q-knowledge-base'),
            'manage_options',
            'qkb-prompt-analytics',
            [$this, 'render_analytics_page']
        );
    }

    /**
     * Enqueue scripts for analytics page
     */
    public function enqueue_scripts($hook) {
        if ($hook !== 'kb_knowledge_base_page_qkb-prompt-analytics') {
            return;
        }

        wp_enqueue_style('qkb-admin-css', plugin_dir_url(QKB_PLUGIN_FILE) . 'assets/css/admin.css', [], QKB_VERSION);
        wp_enqueue_script('chart-js', 'https://cdn.jsdelivr.net/npm/chart.js', [], '3.9.1', true);
        wp_enqueue_script('qkb-prompt-analytics', plugin_dir_url(QKB_PLUGIN_FILE) . 'assets/js/prompt-analytics.js', ['jquery', 'chart-js'], QKB_VERSION, true);

        wp_localize_script('qkb-prompt-analytics', 'qkbPromptAnalytics', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('qkb_prompt_analytics_nonce'),
            'errorMessage' => __('An error occurred while fetching analytics data.', 'q-knowledge-base')
        ]);
    }

    /**
     * Render analytics page
     */
    public function render_analytics_page() {
        ?>
        <div class="wrap qkb-analytics-page">
            <h1><?php _e('Prompt Analytics', 'q-knowledge-base'); ?></h1>

            <div class="qkb-analytics-filters">
                <select id="qkb-prompt-type-filter">
                    <option value="all"><?php _e('All Prompt Types', 'q-knowledge-base'); ?></option>
                    <option value="system"><?php _e('System Prompts', 'q-knowledge-base'); ?></option>
                    <option value="assistant"><?php _e('Assistant Prompts', 'q-knowledge-base'); ?></option>
                    <option value="template"><?php _e('Template Prompts', 'q-knowledge-base'); ?></option>
                </select>

                <select id="qkb-date-range-filter">
                    <option value="7"><?php _e('Last 7 Days', 'q-knowledge-base'); ?></option>
                    <option value="30"><?php _e('Last 30 Days', 'q-knowledge-base'); ?></option>
                    <option value="90"><?php _e('Last 90 Days', 'q-knowledge-base'); ?></option>
                    <option value="all"><?php _e('All Time', 'q-knowledge-base'); ?></option>
                </select>

                <button id="qkb-refresh-analytics" class="button button-primary">
                    <i class="dashicons dashicons-update"></i> <?php _e('Refresh', 'q-knowledge-base'); ?>
                </button>
            </div>

            <div class="qkb-analytics-dashboard">
                <div class="qkb-analytics-card qkb-analytics-summary">
                    <h2><?php _e('Summary', 'q-knowledge-base'); ?></h2>
                    <div class="qkb-analytics-summary-grid">
                        <div class="qkb-analytics-metric">
                            <span class="qkb-metric-value" id="qkb-total-uses">0</span>
                            <span class="qkb-metric-label"><?php _e('Total Uses', 'q-knowledge-base'); ?></span>
                        </div>
                        <div class="qkb-analytics-metric">
                            <span class="qkb-metric-value" id="qkb-avg-rating">0.0</span>
                            <span class="qkb-metric-label"><?php _e('Avg. Rating', 'q-knowledge-base'); ?></span>
                        </div>
                        <div class="qkb-analytics-metric">
                            <span class="qkb-metric-value" id="qkb-success-rate">0%</span>
                            <span class="qkb-metric-label"><?php _e('Success Rate', 'q-knowledge-base'); ?></span>
                        </div>
                        <div class="qkb-analytics-metric">
                            <span class="qkb-metric-value" id="qkb-avg-response-time">0ms</span>
                            <span class="qkb-metric-label"><?php _e('Avg. Response Time', 'q-knowledge-base'); ?></span>
                        </div>
                    </div>
                </div>

                <div class="qkb-analytics-row">
                    <div class="qkb-analytics-card qkb-analytics-chart">
                        <h2><?php _e('Usage Over Time', 'q-knowledge-base'); ?></h2>
                        <canvas id="qkb-usage-chart"></canvas>
                    </div>

                    <div class="qkb-analytics-card qkb-analytics-chart">
                        <h2><?php _e('Performance Metrics', 'q-knowledge-base'); ?></h2>
                        <canvas id="qkb-performance-chart"></canvas>
                    </div>
                </div>

                <div class="qkb-analytics-card qkb-analytics-table">
                    <h2><?php _e('Top Performing Prompts', 'q-knowledge-base'); ?></h2>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Prompt', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Type', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Uses', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Success Rate', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Avg. Rating', 'q-knowledge-base'); ?></th>
                            </tr>
                        </thead>
                        <tbody id="qkb-top-prompts-table">
                            <tr>
                                <td colspan="5"><?php _e('Loading data...', 'q-knowledge-base'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * AJAX handler for getting analytics data
     */
    public function ajax_get_analytics() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_prompt_analytics_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'q-knowledge-base')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have sufficient permissions', 'q-knowledge-base')]);
        }

        // Get filter parameters
        $prompt_type = isset($_POST['prompt_type']) ? sanitize_text_field($_POST['prompt_type']) : 'all';
        $date_range = isset($_POST['date_range']) ? intval($_POST['date_range']) : 30;

        // Get analytics data
        $summary = $this->get_analytics_summary($prompt_type, $date_range);
        $usage_data = $this->get_usage_over_time($prompt_type, $date_range);
        $performance_data = $this->get_performance_metrics($prompt_type, $date_range);
        $top_prompts = $this->get_top_prompts($prompt_type, $date_range);

        wp_send_json_success([
            'summary' => $summary,
            'usage_data' => $usage_data,
            'performance_data' => $performance_data,
            'top_prompts' => $top_prompts
        ]);
    }

    /**
     * Track prompt usage
     *
     * @param string $prompt_id Prompt ID (can be assistant ID, template ID, etc.)
     * @param string $prompt_type Type of prompt (system, assistant, template)
     * @param array $data Usage data (query, response, rating, etc.)
     * @param int $version Prompt version
     */
    public function track_prompt_usage($prompt_id, $prompt_type, $data, $version = 1) {
        // Insert usage record
        $this->db->insert(
            $this->usage_table,
            [
                'prompt_id' => $prompt_id,
                'prompt_type' => $prompt_type,
                'prompt_version' => $version,
                'query' => $data['query'] ?? '',
                'response' => $data['response'] ?? '',
                'rating' => $data['rating'] ?? 0,
                'response_time' => $data['response_time'] ?? 0,
                'tokens_used' => $data['tokens_used'] ?? 0,
                'user_id' => get_current_user_id(),
                'created_at' => current_time('mysql')
            ],
            [
                '%s', '%s', '%d', '%s', '%s', '%d', '%d', '%d', '%d', '%s'
            ]
        );

        // Update analytics summary
        $this->update_analytics_summary($prompt_id, $prompt_type, $data);
    }

    /**
     * Update analytics summary
     *
     * @param string $prompt_id Prompt ID
     * @param string $prompt_type Prompt type
     * @param array $data Usage data
     */
    private function update_analytics_summary($prompt_id, $prompt_type, $data) {
        // Check if summary exists
        $summary = $this->db->get_row(
            $this->db->prepare(
                "SELECT * FROM {$this->analytics_table} WHERE prompt_id = %s AND prompt_type = %s",
                $prompt_id, $prompt_type
            )
        );

        $rating = isset($data['rating']) ? intval($data['rating']) : 0;
        $response_time = isset($data['response_time']) ? intval($data['response_time']) : 0;
        $success = isset($data['success']) ? (bool)$data['success'] : ($rating > 3);

        if ($summary) {
            // Update existing summary
            $new_total = $summary->total_uses + 1;
            $new_avg_rating = (($summary->avg_rating * $summary->total_uses) + $rating) / $new_total;
            $new_success_rate = (($summary->success_rate * $summary->total_uses) + ($success ? 1 : 0)) / $new_total;
            $new_avg_response_time = (($summary->avg_response_time * $summary->total_uses) + $response_time) / $new_total;

            $this->db->update(
                $this->analytics_table,
                [
                    'total_uses' => $new_total,
                    'avg_rating' => $new_avg_rating,
                    'success_rate' => $new_success_rate,
                    'avg_response_time' => $new_avg_response_time,
                    'last_updated' => current_time('mysql')
                ],
                ['id' => $summary->id],
                ['%d', '%f', '%f', '%d', '%s'],
                ['%d']
            );
        } else {
            // Create new summary
            $this->db->insert(
                $this->analytics_table,
                [
                    'prompt_id' => $prompt_id,
                    'prompt_type' => $prompt_type,
                    'total_uses' => 1,
                    'avg_rating' => $rating,
                    'success_rate' => $success ? 1 : 0,
                    'avg_response_time' => $response_time,
                    'last_updated' => current_time('mysql')
                ],
                ['%s', '%s', '%d', '%f', '%f', '%d', '%s']
            );
        }
    }

    /**
     * Get analytics summary
     *
     * @param string $prompt_type Prompt type filter
     * @param int $date_range Date range in days
     * @return array Summary data
     */
    private function get_analytics_summary($prompt_type = 'all', $date_range = 30) {
        $where = [];
        $where_sql = '';

        if ($prompt_type !== 'all') {
            $where[] = $this->db->prepare("prompt_type = %s", $prompt_type);
        }

        if ($date_range !== 'all' && $date_range > 0) {
            $date_limit = date('Y-m-d H:i:s', strtotime("-{$date_range} days"));
            $where[] = $this->db->prepare("created_at >= %s", $date_limit);
        }

        if (!empty($where)) {
            $where_sql = "WHERE " . implode(" AND ", $where);
        }

        $query = "SELECT
                    COUNT(*) as total_uses,
                    AVG(rating) as avg_rating,
                    SUM(CASE WHEN rating > 3 THEN 1 ELSE 0 END) / COUNT(*) as success_rate,
                    AVG(response_time) as avg_response_time
                  FROM {$this->usage_table}
                  {$where_sql}";

        $result = $this->db->get_row($query);

        return [
            'total_uses' => intval($result->total_uses),
            'avg_rating' => round(floatval($result->avg_rating), 1),
            'success_rate' => round(floatval($result->success_rate) * 100, 1),
            'avg_response_time' => intval($result->avg_response_time)
        ];
    }

    /**
     * Get usage data over time
     *
     * @param string $prompt_type Prompt type filter
     * @param int $date_range Date range in days
     * @return array Usage data
     */
    private function get_usage_over_time($prompt_type = 'all', $date_range = 30) {
        $where = [];
        $where_sql = '';

        if ($prompt_type !== 'all') {
            $where[] = $this->db->prepare("prompt_type = %s", $prompt_type);
        }

        if ($date_range !== 'all' && $date_range > 0) {
            $date_limit = date('Y-m-d H:i:s', strtotime("-{$date_range} days"));
            $where[] = $this->db->prepare("created_at >= %s", $date_limit);
        }

        if (!empty($where)) {
            $where_sql = "WHERE " . implode(" AND ", $where);
        }

        $group_by = "DATE(created_at)";
        if ($date_range > 60) {
            $group_by = "WEEK(created_at)";
        } elseif ($date_range > 7) {
            $group_by = "DATE(created_at)";
        } else {
            $group_by = "DATE(created_at), HOUR(created_at)";
        }

        $query = "SELECT
                    {$group_by} as date_group,
                    COUNT(*) as count
                  FROM {$this->usage_table}
                  {$where_sql}
                  GROUP BY {$group_by}
                  ORDER BY date_group ASC";

        $results = $this->db->get_results($query);

        $labels = [];
        $data = [];

        foreach ($results as $row) {
            $labels[] = $row->date_group;
            $data[] = intval($row->count);
        }

        return [
            'labels' => $labels,
            'data' => $data
        ];
    }

    /**
     * Get performance metrics
     *
     * @param string $prompt_type Prompt type filter
     * @param int $date_range Date range in days
     * @return array Performance data
     */
    private function get_performance_metrics($prompt_type = 'all', $date_range = 30) {
        $where = [];
        $where_sql = '';

        if ($prompt_type !== 'all') {
            $where[] = $this->db->prepare("prompt_type = %s", $prompt_type);
        }

        if ($date_range !== 'all' && $date_range > 0) {
            $date_limit = date('Y-m-d H:i:s', strtotime("-{$date_range} days"));
            $where[] = $this->db->prepare("created_at >= %s", $date_limit);
        }

        if (!empty($where)) {
            $where_sql = "WHERE " . implode(" AND ", $where);
        }

        $group_by = "DATE(created_at)";
        if ($date_range > 60) {
            $group_by = "WEEK(created_at)";
        } elseif ($date_range > 7) {
            $group_by = "DATE(created_at)";
        } else {
            $group_by = "DATE(created_at), HOUR(created_at)";
        }

        $query = "SELECT
                    {$group_by} as date_group,
                    AVG(rating) as avg_rating,
                    SUM(CASE WHEN rating > 3 THEN 1 ELSE 0 END) / COUNT(*) as success_rate,
                    AVG(response_time) as avg_response_time
                  FROM {$this->usage_table}
                  {$where_sql}
                  GROUP BY {$group_by}
                  ORDER BY date_group ASC";

        $results = $this->db->get_results($query);

        $labels = [];
        $rating_data = [];
        $success_data = [];
        $response_time_data = [];

        foreach ($results as $row) {
            $labels[] = $row->date_group;
            $rating_data[] = round(floatval($row->avg_rating), 1);
            $success_data[] = round(floatval($row->success_rate) * 100, 1);
            $response_time_data[] = intval($row->avg_response_time);
        }

        return [
            'labels' => $labels,
            'rating_data' => $rating_data,
            'success_data' => $success_data,
            'response_time_data' => $response_time_data
        ];
    }

    /**
     * Get top performing prompts
     *
     * @param string $prompt_type Prompt type filter
     * @param int $date_range Date range in days
     * @return array Top prompts data
     */
    private function get_top_prompts($prompt_type = 'all', $date_range = 30) {
        $where = [];
        $where_sql = '';

        if ($prompt_type !== 'all') {
            $where[] = $this->db->prepare("prompt_type = %s", $prompt_type);
        }

        if ($date_range !== 'all' && $date_range > 0) {
            $date_limit = date('Y-m-d H:i:s', strtotime("-{$date_range} days"));
            $where[] = $this->db->prepare("last_updated >= %s", $date_limit);
        }

        if (!empty($where)) {
            $where_sql = "WHERE " . implode(" AND ", $where);
        }

        $query = "SELECT
                    prompt_id,
                    prompt_type,
                    total_uses,
                    avg_rating,
                    success_rate
                  FROM {$this->analytics_table}
                  {$where_sql}
                  ORDER BY success_rate DESC, total_uses DESC
                  LIMIT 10";

        $results = $this->db->get_results($query);

        $prompts = [];

        foreach ($results as $row) {
            $prompt_name = $this->get_prompt_name($row->prompt_id, $row->prompt_type);

            $prompts[] = [
                'id' => $row->prompt_id,
                'name' => $prompt_name,
                'type' => $row->prompt_type,
                'uses' => intval($row->total_uses),
                'success_rate' => round(floatval($row->success_rate) * 100, 1),
                'avg_rating' => round(floatval($row->avg_rating), 1)
            ];
        }

        return $prompts;
    }

    /**
     * Get prompt name from ID and type
     *
     * @param string $prompt_id Prompt ID
     * @param string $prompt_type Prompt type
     * @return string Prompt name
     */
    private function get_prompt_name($prompt_id, $prompt_type) {
        switch ($prompt_type) {
            case 'assistant':
                $assistant = get_term($prompt_id, 'kb_assistant');
                if ($assistant && !is_wp_error($assistant)) {
                    // Handle both object and array formats for assistant
                    return is_object($assistant) ? $assistant->name : (isset($assistant['name']) ? $assistant['name'] : __('Unknown Assistant', 'q-knowledge-base'));
                }
                return __('Unknown Assistant', 'q-knowledge-base');

            case 'template':
                $templates_handler = new QKB_OpenAI_Prompt_Templates();
                $template = $templates_handler->get_template($prompt_id, true); // Force refresh to get latest templates
                return $template ? $template['name'] : __('Unknown Template', 'q-knowledge-base');

            case 'system':
                return __('System Prompt', 'q-knowledge-base');

            default:
                return __('Unknown Prompt', 'q-knowledge-base');
        }
    }
}

// Initialize the class
add_action('plugins_loaded', function() {
    new QKB_OpenAI_Prompt_Analytics();
});
