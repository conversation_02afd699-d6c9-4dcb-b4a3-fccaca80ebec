<?php
if (!defined('ABSPATH')) {
    exit;
}

require_once(plugin_dir_path(__FILE__) . 'openai/class-qkb-openai-base.php');
require_once(plugin_dir_path(__FILE__) . 'openai/class-qkb-openai-prompts.php');
require_once(plugin_dir_path(__FILE__) . 'openai/class-qkb-openai-completion.php');
require_once(plugin_dir_path(__FILE__) . 'openai/class-qkb-openai-embedding.php');
require_once(plugin_dir_path(__FILE__) . 'openai/class-qkb-openai-context.php');
require_once(plugin_dir_path(__FILE__) . 'openai/class-qkb-openai-prompt-templates.php');
require_once(plugin_dir_path(__FILE__) . 'openai/class-qkb-openai-prompt-tester.php');
require_once(plugin_dir_path(__FILE__) . 'openai/class-qkb-openai-assistants.php');

class QKB_OpenAI_Handler {
    private $completion_handler;
    private $embedding_handler;
    private $context_handler;
    private $assistants_handler;

    public function __construct() {
        $this->completion_handler = new QKB_OpenAI_Completion();
        $this->embedding_handler = new QKB_OpenAI_Embedding();
        $this->context_handler = new QKB_OpenAI_Context();
        $this->assistants_handler = new QKB_OpenAI_Assistants();
    }

    public function get_completion($message, $context = '', $assistant_id = null) {
        return $this->completion_handler->get_completion($message, $context, $assistant_id);
    }

    public function get_embedding($text) {
        return $this->embedding_handler->get_embedding($text);
    }

    /**
     * Get an assistant from OpenAI
     *
     * @param string $assistant_id OpenAI Assistant ID
     * @return array|WP_Error Assistant data or error
     */
    public function get_assistant($assistant_id) {
        return $this->assistants_handler->get_assistant($assistant_id);
    }

    /**
     * Create a thread for an assistant
     *
     * @return string|WP_Error Thread ID or error
     */
    public function create_thread() {
        return $this->assistants_handler->create_thread();
    }

    /**
     * Add a message to a thread
     *
     * @param string $thread_id Thread ID
     * @param string $content Message content
     * @param string $role Message role (user or assistant)
     * @return string|WP_Error Message ID or error
     */
    public function add_message($thread_id, $content, $role = 'user') {
        return $this->assistants_handler->add_message($thread_id, $content, $role);
    }

    /**
     * Run an assistant on a thread
     *
     * @param string $thread_id Thread ID
     * @param string $assistant_id Assistant ID
     * @return string|WP_Error Run ID or error
     */
    public function run_assistant($thread_id, $assistant_id) {
        return $this->assistants_handler->run_assistant($thread_id, $assistant_id);
    }

    /**
     * Get the status of a run
     *
     * @param string $thread_id Thread ID
     * @param string $run_id Run ID
     * @return array|WP_Error Run status or error
     */
    public function get_run_status($thread_id, $run_id) {
        return $this->assistants_handler->get_run_status($thread_id, $run_id);
    }

    /**
     * Get messages from a thread
     *
     * @param string $thread_id Thread ID
     * @param array $params Optional parameters
     * @return array|WP_Error Messages or error
     */
    public function get_messages($thread_id, $params = []) {
        return $this->assistants_handler->get_messages($thread_id, $params);
    }



    public function search_knowledge_base($query, $assistant_id = null) {
        try {
            error_log('Searching knowledge base with query: ' . $query . ' and assistant ID: ' . $assistant_id);

            // Get the term ID directly to ensure we're using the correct value
            if (!empty($assistant_id)) {
                $assistant_term = get_term($assistant_id, 'kb_assistant');
                if (!is_wp_error($assistant_term) && $assistant_term) {
                    error_log('Found assistant term for search: ' . $assistant_term->name . ' (ID: ' . $assistant_term->term_id . ')');
                    $assistant_id = $assistant_term->term_id;
                } else {
                    error_log('Assistant term not found for search with ID: ' . $assistant_id);
                }
            }

            $result = $this->context_handler->get_kb_context($query, [
                'assistant_id' => $assistant_id,
                'include_sources' => true,
                'format_results' => true,
                'fallback_to_all' => false,  // Never fallback to all content
                'min_relevance' => 0.1,
                'timeout' => 5,
                'retry_on_error' => true
            ]);

            if (empty($result['context'])) {
                error_log('No relevant content found for query: ' . $query . ' with assistant ID: ' . $assistant_id);
                return [
                    'context' => 'NO_RELEVANT_CONTENT',
                    'response_type' => 'no_content',
                    'confidence_score' => 0,
                    'sources' => []
                ];
            }

            error_log('Found relevant content for query: ' . $query . ' with assistant ID: ' . $assistant_id);
            return $result;
        } catch (Exception $e) {
            error_log('Knowledge base search error: ' . $e->getMessage());
            return [
                'context' => 'ERROR',
                'response_type' => 'error',
                'confidence_score' => 0,
                'sources' => []
            ];
        }
    }
}
