<?php
/**
 * Document Settings Class
 *
 * Handles settings for document extraction features
 *
 * @package Q-Knowledge-Base
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Document Settings Class
 */
class QKB_Document_Settings {

    /**
     * Instance of this class
     *
     * @var QKB_Document_Settings
     */
    private static $instance = null;

    /**
     * Constructor
     */
    private function __construct() {
        // Add settings page
        add_action('admin_menu', [$this, 'add_settings_page']);
        
        // Register settings
        add_action('admin_init', [$this, 'register_settings']);
    }

    /**
     * Get instance of this class
     *
     * @return QKB_Document_Settings
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Add settings page
     */
    public function add_settings_page() {
        add_submenu_page(
            'edit.php?post_type=kb_knowledge_base',
            __('Document Settings', 'q-knowledge-base'),
            __('Document Settings', 'q-knowledge-base'),
            'manage_options',
            'qkb-document-settings',
            [$this, 'render_settings_page']
        );
    }

    /**
     * Register settings
     */
    public function register_settings() {
        // Register settings section
        add_settings_section(
            'qkb_document_settings_section',
            __('Document Extraction Settings', 'q-knowledge-base'),
            [$this, 'render_settings_section'],
            'qkb-document-settings'
        );

        // Register OCR settings
        register_setting('qkb_document_settings', 'qkb_use_ocr', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean',
        ]);

        add_settings_field(
            'qkb_use_ocr',
            __('Enable OCR', 'q-knowledge-base'),
            [$this, 'render_checkbox_field'],
            'qkb-document-settings',
            'qkb_document_settings_section',
            [
                'id' => 'qkb_use_ocr',
                'label' => __('Enable Optical Character Recognition (OCR) for scanned documents', 'q-knowledge-base'),
                'description' => __('Requires Tesseract OCR to be installed on the server', 'q-knowledge-base'),
            ]
        );

        // Register OCR language setting
        register_setting('qkb_document_settings', 'qkb_ocr_language', [
            'type' => 'string',
            'default' => 'eng',
            'sanitize_callback' => 'sanitize_text_field',
        ]);

        add_settings_field(
            'qkb_ocr_language',
            __('OCR Language', 'q-knowledge-base'),
            [$this, 'render_text_field'],
            'qkb-document-settings',
            'qkb_document_settings_section',
            [
                'id' => 'qkb_ocr_language',
                'label' => __('Language code for OCR (e.g., eng, fra, deu)', 'q-knowledge-base'),
                'description' => __('Default: eng (English)', 'q-knowledge-base'),
            ]
        );

        // Register structure preservation setting
        register_setting('qkb_document_settings', 'qkb_preserve_structure', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean',
        ]);

        add_settings_field(
            'qkb_preserve_structure',
            __('Preserve Structure', 'q-knowledge-base'),
            [$this, 'render_checkbox_field'],
            'qkb-document-settings',
            'qkb_document_settings_section',
            [
                'id' => 'qkb_preserve_structure',
                'label' => __('Attempt to preserve document structure during extraction', 'q-knowledge-base'),
                'description' => __('May improve formatting but can increase processing time', 'q-knowledge-base'),
            ]
        );

        // Register metadata extraction setting
        register_setting('qkb_document_settings', 'qkb_extract_metadata', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean',
        ]);

        add_settings_field(
            'qkb_extract_metadata',
            __('Extract Metadata', 'q-knowledge-base'),
            [$this, 'render_checkbox_field'],
            'qkb-document-settings',
            'qkb_document_settings_section',
            [
                'id' => 'qkb_extract_metadata',
                'label' => __('Extract document metadata (title, author, etc.)', 'q-knowledge-base'),
                'description' => __('Useful for organizing and searching documents', 'q-knowledge-base'),
            ]
        );

        // Register caching setting
        register_setting('qkb_document_settings', 'qkb_cache_extraction', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean',
        ]);

        add_settings_field(
            'qkb_cache_extraction',
            __('Cache Extraction Results', 'q-knowledge-base'),
            [$this, 'render_checkbox_field'],
            'qkb-document-settings',
            'qkb_document_settings_section',
            [
                'id' => 'qkb_cache_extraction',
                'label' => __('Cache document extraction results for better performance', 'q-knowledge-base'),
                'description' => __('Recommended for large documents', 'q-knowledge-base'),
            ]
        );

        // Register cache expiration setting
        register_setting('qkb_document_settings', 'qkb_cache_expiration', [
            'type' => 'integer',
            'default' => 7 * DAY_IN_SECONDS,
            'sanitize_callback' => 'absint',
        ]);

        add_settings_field(
            'qkb_cache_expiration',
            __('Cache Expiration', 'q-knowledge-base'),
            [$this, 'render_number_field'],
            'qkb-document-settings',
            'qkb_document_settings_section',
            [
                'id' => 'qkb_cache_expiration',
                'label' => __('Number of days to keep cached extraction results', 'q-knowledge-base'),
                'description' => __('Default: 7 days', 'q-knowledge-base'),
                'min' => 1,
                'max' => 30,
                'step' => 1,
            ]
        );
    }

    /**
     * Render settings section
     */
    public function render_settings_section() {
        echo '<p>' . __('Configure settings for document extraction from PDF, DOC, and DOCX files.', 'q-knowledge-base') . '</p>';
        
        // Check if OCR is available
        require_once QKB_PLUGIN_DIR . 'includes/class-qkb-document-extractor.php';
        $extractor = new QKB_Document_Extractor();
        
        if (!$extractor->is_ocr_available()) {
            echo '<div class="notice notice-warning inline"><p>';
            echo __('OCR functionality is not available. Please install Tesseract OCR on your server and the PHP Tesseract OCR library.', 'q-knowledge-base');
            echo '</p></div>';
        }
    }

    /**
     * Render settings page
     */
    public function render_settings_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            <form method="post" action="options.php">
                <?php
                settings_fields('qkb_document_settings');
                do_settings_sections('qkb-document-settings');
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }

    /**
     * Render checkbox field
     *
     * @param array $args Field arguments
     */
    public function render_checkbox_field($args) {
        $id = $args['id'];
        $value = get_option($id);
        ?>
        <label for="<?php echo esc_attr($id); ?>">
            <input type="checkbox" id="<?php echo esc_attr($id); ?>" name="<?php echo esc_attr($id); ?>" value="1" <?php checked($value, true); ?>>
            <?php echo esc_html($args['label']); ?>
        </label>
        <?php if (!empty($args['description'])) : ?>
            <p class="description"><?php echo esc_html($args['description']); ?></p>
        <?php endif; ?>
        <?php
    }

    /**
     * Render text field
     *
     * @param array $args Field arguments
     */
    public function render_text_field($args) {
        $id = $args['id'];
        $value = get_option($id);
        ?>
        <input type="text" id="<?php echo esc_attr($id); ?>" name="<?php echo esc_attr($id); ?>" value="<?php echo esc_attr($value); ?>" class="regular-text">
        <?php if (!empty($args['description'])) : ?>
            <p class="description"><?php echo esc_html($args['description']); ?></p>
        <?php endif; ?>
        <?php
    }

    /**
     * Render number field
     *
     * @param array $args Field arguments
     */
    public function render_number_field($args) {
        $id = $args['id'];
        $value = get_option($id);
        
        // Convert from seconds to days for display
        if ($id === 'qkb_cache_expiration') {
            $value = $value / DAY_IN_SECONDS;
        }
        
        $min = isset($args['min']) ? $args['min'] : 0;
        $max = isset($args['max']) ? $args['max'] : 999;
        $step = isset($args['step']) ? $args['step'] : 1;
        ?>
        <input type="number" id="<?php echo esc_attr($id); ?>" name="<?php echo esc_attr($id); ?>" value="<?php echo esc_attr($value); ?>" min="<?php echo esc_attr($min); ?>" max="<?php echo esc_attr($max); ?>" step="<?php echo esc_attr($step); ?>" class="small-text">
        <?php if (!empty($args['label'])) : ?>
            <label for="<?php echo esc_attr($id); ?>"><?php echo esc_html($args['label']); ?></label>
        <?php endif; ?>
        <?php if (!empty($args['description'])) : ?>
            <p class="description"><?php echo esc_html($args['description']); ?></p>
        <?php endif; ?>
        <?php
    }
}
