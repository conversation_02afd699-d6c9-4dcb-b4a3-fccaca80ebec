<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Assistant Builder Admin Page
 *
 * Provides a visual interface for building and managing assistants.
 */
class QKB_Assistant_Builder
{
    /**
     * Instance of this class
     */
    private static $instance = null;

    /**
     * Get the instance of this class
     */
    public static function get_instance()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct()
    {
        // Add admin menu
        add_action('admin_menu', [$this, 'add_admin_menu']);

        // Add admin scripts and styles
        add_action('admin_enqueue_scripts', [$this, 'enqueue_scripts']);

        // Add AJAX handlers
        add_action('wp_ajax_qkb_get_assistant_data', [$this, 'ajax_get_assistant_data']);
        add_action('wp_ajax_qkb_save_assistant', [$this, 'ajax_save_assistant']);
        add_action('wp_ajax_qkb_get_system_templates', [$this, 'ajax_get_system_templates']);
        add_action('wp_ajax_qkb_get_suggested_prompts', [$this, 'ajax_get_suggested_prompts']);
        add_action('wp_ajax_qkb_save_suggested_prompts', [$this, 'ajax_save_suggested_prompts']);
        add_action('wp_ajax_qkb_get_assistant_actions', [$this, 'ajax_get_assistant_actions']);
        add_action('wp_ajax_qkb_save_assistant_actions', [$this, 'ajax_save_assistant_actions']);
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu()
    {
        add_submenu_page(
            'edit.php?post_type=kb_knowledge_base',
            __('Assistant Builder', 'q-knowledge-base'),
            __('Assistant Builder', 'q-knowledge-base'),
            'manage_options',
            'qkb-assistant-builder',
            [$this, 'render_page']
        );
    }

    /**
     * Enqueue scripts and styles
     *
     * @param string $hook Current admin page
     */
    public function enqueue_scripts($hook)
    {
        if ($hook !== 'kb_knowledge_base_page_qkb-assistant-builder') {
            return;
        }

        // Enqueue styles
        wp_enqueue_style('qkb-assistant-builder', QKB_PLUGIN_URL . 'assets/css/assistant-builder.css', [], QKB_VERSION);
        wp_enqueue_style('qkb-modern-assistant-builder', QKB_PLUGIN_URL . 'assets/css/modern-assistant-builder.css', ['qkb-assistant-builder'], QKB_VERSION);

        // Enqueue scripts
        wp_enqueue_script('qkb-assistant-builder', QKB_PLUGIN_URL . 'assets/js/assistant-builder.js', ['jquery', 'wp-util'], QKB_VERSION, true);

        // Localize script
        wp_localize_script('qkb-assistant-builder', 'qkbAssistantBuilder', [
            'nonce' => wp_create_nonce('qkb_admin_nonce'),
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'strings' => [
                'saveSuccess' => __('Assistant saved successfully', 'q-knowledge-base'),
                'saveError' => __('Error saving assistant', 'q-knowledge-base'),
                'confirmDelete' => __('Are you sure you want to delete this assistant?', 'q-knowledge-base'),
                'deleteSuccess' => __('Assistant deleted successfully', 'q-knowledge-base'),
                'deleteError' => __('Error deleting assistant', 'q-knowledge-base'),
                'loadError' => __('Error loading assistant data', 'q-knowledge-base'),
                'noAssistants' => __('No assistants found', 'q-knowledge-base'),
                'noTemplates' => __('No system templates found', 'q-knowledge-base'),
            ]
        ]);
    }

    /**
     * Render the assistant builder page
     */
    public function render_page()
    {
        ?>
        <div class="wrap qkb-assistant-builder-wrap">
            <h1><?php _e('Assistant Builder', 'q-knowledge-base'); ?></h1>

            <div class="qkb-assistant-builder-container">
                <div class="qkb-assistant-sidebar">
                    <div class="qkb-assistant-list-header">
                        <h2><?php _e('Assistants', 'q-knowledge-base'); ?></h2>
                        <button type="button" class="button button-primary qkb-add-assistant">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php _e('Add New', 'q-knowledge-base'); ?>
                        </button>
                    </div>

                    <div class="qkb-assistant-list">
                        <ul class="qkb-assistants"></ul>
                    </div>
                </div>

                <div class="qkb-assistant-editor">
                    <div class="qkb-assistant-editor-header">
                        <h2 class="qkb-assistant-editor-title"><?php _e('New Assistant', 'q-knowledge-base'); ?></h2>
                        <div class="qkb-assistant-actions">
                            <button type="button" class="button qkb-delete-assistant">
                                <span class="dashicons dashicons-trash"></span>
                                <?php _e('Delete', 'q-knowledge-base'); ?>
                            </button>
                            <button type="button" class="button button-primary qkb-save-assistant">
                                <span class="dashicons dashicons-saved"></span>
                                <?php _e('Save', 'q-knowledge-base'); ?>
                            </button>
                        </div>
                    </div>

                    <div class="qkb-assistant-editor-content">
                        <form id="qkb-assistant-form">
                            <input type="hidden" id="qkb-assistant-id" name="assistant_id" value="">
                            <input type="hidden" id="qkb-openai-assistant-id" name="openai_assistant_id" value="">

                            <div class="qkb-assistant-tabs">
                                <div class="qkb-tab-nav">
                                    <button type="button" class="qkb-tab-button active" data-tab="general">
                                        <span class="dashicons dashicons-admin-generic"></span>
                                        <?php _e('General', 'q-knowledge-base'); ?>
                                    </button>
                                    <button type="button" class="qkb-tab-button" data-tab="instructions">
                                        <span class="dashicons dashicons-editor-code"></span>
                                        <?php _e('Instructions', 'q-knowledge-base'); ?>
                                    </button>
                                    <button type="button" class="qkb-tab-button" data-tab="knowledge">
                                        <span class="dashicons dashicons-book-alt"></span>
                                        <?php _e('Knowledge', 'q-knowledge-base'); ?>
                                    </button>
                                    <button type="button" class="qkb-tab-button" data-tab="suggested-prompts">
                                        <span class="dashicons dashicons-format-chat"></span>
                                        <?php _e('Suggested Prompts', 'q-knowledge-base'); ?>
                                    </button>
                                    <button type="button" class="qkb-tab-button" data-tab="assistant-actions">
                                        <span class="dashicons dashicons-admin-tools"></span>
                                        <?php _e('Assistant Actions', 'q-knowledge-base'); ?>
                                    </button>
                                </div>

                                <div class="qkb-tab-content">
                                    <!-- General Tab -->
                                    <div class="qkb-tab-pane active" data-tab="general">
                                        <div class="qkb-form-row">
                                            <label for="qkb-assistant-name"><?php _e('Name', 'q-knowledge-base'); ?></label>
                                            <input type="text" id="qkb-assistant-name" name="name" required>
                                        </div>

                                        <div class="qkb-form-row">
                                            <label
                                                for="qkb-assistant-description"><?php _e('Description', 'q-knowledge-base'); ?></label>
                                            <textarea id="qkb-assistant-description" name="description" rows="3"></textarea>
                                        </div>

                                        <div class="qkb-form-row">
                                            <label for="qkb-assistant-icon"><?php _e('Icon', 'q-knowledge-base'); ?></label>
                                            <input type="text" id="qkb-assistant-icon" name="icon" placeholder="fas fa-robot">
                                            <p class="description">
                                                <?php _e('Enter a Font Awesome icon class (e.g., fas fa-robot)', 'q-knowledge-base'); ?>
                                            </p>
                                        </div>

                                        <div class="qkb-form-row">
                                            <label for="qkb-assistant-model"><?php _e('Model', 'q-knowledge-base'); ?></label>
                                            <select id="qkb-assistant-model" name="model">
                                                <option value="gpt-4o"><?php _e('GPT-4o', 'q-knowledge-base'); ?></option>
                                                <option value="gpt-4-turbo"><?php _e('GPT-4 Turbo', 'q-knowledge-base'); ?>
                                                </option>
                                                <option value="gpt-4"><?php _e('GPT-4', 'q-knowledge-base'); ?></option>
                                                <option value="gpt-3.5-turbo"><?php _e('GPT-3.5 Turbo', 'q-knowledge-base'); ?>
                                                </option>
                                            </select>
                                        </div>

                                        <div class="qkb-form-row">
                                            <label
                                                for="qkb-assistant-expertise"><?php _e('Expertise', 'q-knowledge-base'); ?></label>
                                            <input type="text" id="qkb-assistant-expertise" name="expertise"
                                                placeholder="Knowledge Management,Information Retrieval,User Support">
                                            <p class="description">
                                                <?php _e('Comma-separated list of expertise areas', 'q-knowledge-base'); ?>
                                            </p>
                                        </div>

                                        <div class="qkb-form-row">
                                            <label
                                                for="qkb-assistant-categories"><?php _e('Categories', 'q-knowledge-base'); ?></label>
                                            <input type="text" id="qkb-assistant-categories" name="categories"
                                                placeholder="General,Technical,Support">
                                            <p class="description">
                                                <?php _e('Comma-separated list of categories', 'q-knowledge-base'); ?>
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Instructions Tab -->
                                    <div class="qkb-tab-pane" data-tab="instructions">
                                        <div class="qkb-form-row">
                                            <label
                                                for="qkb-assistant-template"><?php _e('Select Template', 'q-knowledge-base'); ?></label>
                                            <select id="qkb-assistant-template" name="template_id">
                                                <option value=""><?php _e('Select a template', 'q-knowledge-base'); ?></option>
                                            </select>
                                        </div>

                                        <div class="qkb-form-row">
                                            <label
                                                for="qkb-assistant-instructions"><?php _e('Instructions', 'q-knowledge-base'); ?></label>
                                            <textarea id="qkb-assistant-instructions" name="instructions" rows="15"></textarea>
                                            <p class="description">
                                                <?php _e('Instructions for the assistant. This will be used as the system prompt.', 'q-knowledge-base'); ?>
                                            </p>
                                        </div>
                                    </div>




                                    <!-- Knowledge Tab -->
                                    <div class="qkb-tab-pane" data-tab="knowledge">
                                        <div class="qkb-form-row">
                                            <label><?php _e('Knowledge Base Content', 'q-knowledge-base'); ?></label>
                                            <div class="qkb-knowledge-selector">
                                                <div class="qkb-knowledge-search">
                                                    <input type="text" id="qkb-knowledge-search"
                                                        placeholder="<?php _e('Search content...', 'q-knowledge-base'); ?>">
                                                </div>
                                                <div class="qkb-knowledge-list">
                                                    <!-- Knowledge base content will be added here dynamically -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Suggested Prompts Tab -->
                                    <div class="qkb-tab-pane" data-tab="suggested-prompts">
                                        <div class="qkb-form-row">
                                            <label><?php _e('Suggested Prompts', 'q-knowledge-base'); ?></label>
                                            <p class="description">
                                                <?php _e('Add suggested prompts for users to quickly ask this assistant', 'q-knowledge-base'); ?>
                                            </p>

                                            <div class="qkb-suggested-prompts-list">
                                                <div class="qkb-suggested-prompts-container">
                                                    <!-- Suggested prompts will be added here dynamically -->
                                                </div>

                                                <button type="button"
                                                    class="button qkb-add-suggested-prompt"><?php _e('Add Prompt', 'q-knowledge-base'); ?></button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Assistant Actions Tab -->
                                    <div class="qkb-tab-pane" data-tab="assistant-actions">
                                        <div class="qkb-form-row">
                                            <label><?php _e('Assistant Actions', 'q-knowledge-base'); ?></label>
                                            <p class="description">
                                                <?php _e('Add actions that can be triggered by this assistant in prompt templates using {assistant_action:NAME}', 'q-knowledge-base'); ?>
                                            </p>

                                            <div class="qkb-assistant-actions-list">
                                                <div class="qkb-assistant-actions-container">
                                                    <!-- Assistant actions will be added here dynamically -->
                                                </div>

                                                <button type="button"
                                                    class="button qkb-add-assistant-action"><?php _e('Add Action', 'q-knowledge-base'); ?></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Suggested Prompt Template -->
        <script type="text/template" id="tmpl-suggested-prompt">
                                                                                                                                    <div class="qkb-suggested-prompt-item">
                                                                                                                                        <div class="qkb-suggested-prompt-input">
                                                                                                                                            <input type="text" name="suggested_prompts[]" value="{{ data.prompt }}" placeholder="<?php _e('Enter a suggested prompt', 'q-knowledge-base'); ?>">
                                                                                                                                            <button type="button" class="button qkb-remove-suggested-prompt" title="<?php _e('Remove', 'q-knowledge-base'); ?>">
                                                                                                                                                <span class="dashicons dashicons-no-alt"></span>
                                                                                                                                            </button>
                                                                                                                                        </div>
                                                                                                                                    </div>
                                                                                                                                </script>

        <!-- Assistant Action Template -->
        <script type="text/template" id="tmpl-assistant-action">
                                                                                                                                    <div class="qkb-assistant-action-item">
                                                                                                                                        <div class="qkb-assistant-action-row">
                                                                                                                                            <div class="qkb-assistant-action-name">
                                                                                                                                                <label><?php _e('Action Name', 'q-knowledge-base'); ?></label>
                                                                                                                                                <input type="text" name="assistant_actions[{{ data.index }}][name]" value="{{ data.name }}" class="regular-text" placeholder="<?php _e('Enter action name', 'q-knowledge-base'); ?>">
                                                                                                                                            </div>
                                                                                                                                        </div>
                                                                                                                                        <div class="qkb-assistant-action-content">
                                                                                                                                            <label><?php _e('Content', 'q-knowledge-base'); ?></label>
                                                                                                                                            <textarea name="assistant_actions[{{ data.index }}][content]" class="widefat" rows="5">{{ data.content }}</textarea>
                                                                                                                                            <p class="description"><?php _e('Content can include HTML and shortcodes. Use {assistant_action:NAME} in prompt templates to trigger this action.', 'q-knowledge-base'); ?></p>
                                                                                                                                        </div>
                                                                                                                                        <div class="qkb-assistant-action-options">
                                                                                                                                            <label>
                                                                                                                                                <input type="checkbox" name="assistant_actions[{{ data.index }}][show_in_modal]" class="qkb-modal-toggle" value="1" <# if (data.show_in_modal) { #>checked<# } #>>
                                                                                                                                                <?php _e('Open in Modal', 'q-knowledge-base'); ?>
                                                                                                                                            </label>
                                                                                                                                        </div>
                                                                                                                                        <div class="qkb-assistant-action-modal-title" <# if (!data.show_in_modal) { #>style="display:none;"<# } #>>
                                                                                                                                            <label><?php _e('Modal Title', 'q-knowledge-base'); ?></label>
                                                                                                                                            <input type="text" name="assistant_actions[{{ data.index }}][modal_title]" value="{{ data.modal_title }}" class="regular-text" placeholder="<?php _e('Enter modal title', 'q-knowledge-base'); ?>">
                                                                                                                                        </div>
                                                                                                                                        <button type="button" class="button qkb-remove-assistant-action"><?php _e('Remove', 'q-knowledge-base'); ?></button>
                                                                                                                                    </div>
                                                                                                                                </script>
        <?php
    }

    /**
     * AJAX handler for getting assistant data
     */
    public function ajax_get_assistant_data()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Get assistant ID
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;
        if (empty($assistant_id)) {
            wp_send_json_error('Assistant ID is required');
        }

        // Get assistant data
        $assistant = get_term($assistant_id, 'kb_assistant');
        if (is_wp_error($assistant) || !$assistant) {
            wp_send_json_error('Assistant not found');
        }

        // Get assistant meta
        $openai_assistant_id = get_term_meta($assistant_id, 'openai_assistant_id', true);
        $icon = get_term_meta($assistant_id, 'assistant_icon', true);
        $expertise = get_term_meta($assistant_id, 'assistant_expertise', true);
        $categories = get_term_meta($assistant_id, 'assistant_categories', true);
        $model = get_term_meta($assistant_id, 'assistant_model', true);
        $instructions = get_term_meta($assistant_id, 'assistant_prompt', true);
        $template_id = get_term_meta($assistant_id, 'assistant_template_id', true);
        $suggested_prompts = get_term_meta($assistant_id, 'assistant_suggested_prompts', true);
        $assistant_actions = get_term_meta($assistant_id, 'assistant_actions', true);

        // Get knowledge base content
        $kb_content = $this->get_assistant_kb_content($assistant_id);

        // Prepare response
        $response = [
            'id' => $assistant->term_id,
            'name' => $assistant->name,
            'description' => $assistant->description,
            'openai_assistant_id' => $openai_assistant_id,
            'icon' => $icon,
            'expertise' => $expertise,
            'categories' => $categories,
            'model' => $model ?: 'gpt-4o',
            'instructions' => $instructions,
            'template_id' => $template_id,
            'kb_content' => $kb_content,
            'suggested_prompts' => $suggested_prompts ?: [],
            'assistant_actions' => $assistant_actions ?: []
        ];

        wp_send_json_success($response);
    }

    /**
     * AJAX handler for saving an assistant
     */
    public function ajax_save_assistant()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Get assistant data
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;
        $name = isset($_POST['name']) ? sanitize_text_field($_POST['name']) : '';
        $description = isset($_POST['description']) ? sanitize_textarea_field($_POST['description']) : '';
        $openai_assistant_id = isset($_POST['openai_assistant_id']) ? sanitize_text_field($_POST['openai_assistant_id']) : '';
        $icon = isset($_POST['icon']) ? sanitize_text_field($_POST['icon']) : '';
        $expertise = isset($_POST['expertise']) ? sanitize_text_field($_POST['expertise']) : '';
        $categories = isset($_POST['categories']) ? sanitize_text_field($_POST['categories']) : '';
        $model = isset($_POST['model']) ? sanitize_text_field($_POST['model']) : 'gpt-4o';
        $instructions = isset($_POST['instructions']) ? wp_kses_post($_POST['instructions']) : '';
        $template_id = isset($_POST['template_id']) ? sanitize_text_field($_POST['template_id']) : '';
        $kb_content = isset($_POST['kb_content']) ? json_decode(stripslashes($_POST['kb_content']), true) : [];
        $suggested_prompts = isset($_POST['suggested_prompts']) ? json_decode(stripslashes($_POST['suggested_prompts']), true) : [];
        $assistant_actions = isset($_POST['assistant_actions']) ? json_decode(stripslashes($_POST['assistant_actions']), true) : [];

        if (empty($name)) {
            wp_send_json_error('Assistant name is required');
        }

        // Create or update assistant
        if (empty($assistant_id)) {
            // Create new assistant
            $result = wp_insert_term($name, 'kb_assistant', [
                'description' => $description,
                'slug' => sanitize_title($name)
            ]);

            if (is_wp_error($result)) {
                wp_send_json_error($result->get_error_message());
            }

            $assistant_id = $result['term_id'];
        } else {
            // Update existing assistant
            $result = wp_update_term($assistant_id, 'kb_assistant', [
                'name' => $name,
                'description' => $description
            ]);

            if (is_wp_error($result)) {
                wp_send_json_error($result->get_error_message());
            }
        }

        // Update assistant meta
        update_term_meta($assistant_id, 'openai_assistant_id', $openai_assistant_id);
        update_term_meta($assistant_id, 'assistant_icon', $icon);
        update_term_meta($assistant_id, 'assistant_expertise', $expertise);
        update_term_meta($assistant_id, 'assistant_categories', $categories);
        update_term_meta($assistant_id, 'assistant_model', $model);
        update_term_meta($assistant_id, 'assistant_prompt', $instructions);
        update_term_meta($assistant_id, 'assistant_template_id', $template_id);
        update_term_meta($assistant_id, 'assistant_suggested_prompts', $suggested_prompts);
        update_term_meta($assistant_id, 'assistant_actions', $assistant_actions);

        // Update knowledge base content
        $this->update_assistant_kb_content($assistant_id, $kb_content);

        // Trigger action for assistant save
        do_action('qkb_after_assistant_save', $assistant_id, [
            'name' => $name,
            'description' => $description
        ]);

        wp_send_json_success([
            'assistant_id' => $assistant_id,
            'message' => __('Assistant saved successfully', 'q-knowledge-base')
        ]);
    }

    /**
     * AJAX handler for getting system templates
     */
    public function ajax_get_system_templates()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }        // Get templates
        $templates_handler = new QKB_OpenAI_Prompt_Templates();
        $templates = $templates_handler->get_templates();

        wp_send_json_success(['templates' => $templates]);
    }

    /**
     * AJAX handler for getting suggested prompts
     */
    public function ajax_get_suggested_prompts()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Get assistant ID
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;
        if (empty($assistant_id)) {
            wp_send_json_error('Assistant ID is required');
        }

        // Get suggested prompts
        $suggested_prompts = get_term_meta($assistant_id, 'assistant_suggested_prompts', true);

        wp_send_json_success(['prompts' => $suggested_prompts ?: []]);
    }

    /**
     * AJAX handler for saving suggested prompts
     */
    public function ajax_save_suggested_prompts()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Get assistant ID and prompts
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;
        $prompts = isset($_POST['prompts']) ? json_decode(stripslashes($_POST['prompts']), true) : [];

        if (empty($assistant_id)) {
            wp_send_json_error('Assistant ID is required');
        }

        // Update suggested prompts
        update_term_meta($assistant_id, 'assistant_suggested_prompts', $prompts);

        wp_send_json_success(['message' => __('Suggested prompts saved successfully', 'q-knowledge-base')]);
    }

    /**
     * AJAX handler for getting assistant actions
     */
    public function ajax_get_assistant_actions()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Get assistant ID
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;
        if (empty($assistant_id)) {
            wp_send_json_error('Assistant ID is required');
        }

        // Get assistant actions
        $assistant_actions = get_term_meta($assistant_id, 'assistant_actions', true);

        wp_send_json_success(['actions' => $assistant_actions ?: []]);
    }

    /**
     * AJAX handler for saving assistant actions
     */
    public function ajax_save_assistant_actions()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Get assistant ID and actions
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;
        $actions = isset($_POST['actions']) ? json_decode(stripslashes($_POST['actions']), true) : [];

        if (empty($assistant_id)) {
            wp_send_json_error('Assistant ID is required');
        }

        // Update assistant actions
        update_term_meta($assistant_id, 'assistant_actions', $actions);

        wp_send_json_success(['message' => __('Assistant actions saved successfully', 'q-knowledge-base')]);
    }



    /**
     * Get knowledge base content assigned to an assistant
     *
     * @param int $assistant_id Assistant ID
     * @return array Knowledge base content
     */
    private function get_assistant_kb_content($assistant_id)
    {
        // Get all knowledge base content
        $args = [
            'post_type' => ['kb_knowledge_base', 'kb_external_content'],
            'posts_per_page' => -1,
            'tax_query' => [
                [
                    'taxonomy' => 'kb_assistant',
                    'field' => 'term_id',
                    'terms' => $assistant_id
                ]
            ]
        ];

        $query = new WP_Query($args);
        $content = [];

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $content[] = get_the_ID();
            }
        }

        wp_reset_postdata();

        return $content;
    }

    /**
     * Update knowledge base content assigned to an assistant
     *
     * @param int $assistant_id Assistant ID
     * @param array $content_ids Content IDs
     */
    private function update_assistant_kb_content($assistant_id, $content_ids)
    {
        // Get current content
        $current_content = $this->get_assistant_kb_content($assistant_id);

        // Remove assistant from content that's no longer assigned
        $to_remove = array_diff($current_content, $content_ids);
        foreach ($to_remove as $post_id) {
            wp_remove_object_terms($post_id, $assistant_id, 'kb_assistant');
        }

        // Add assistant to newly assigned content
        $to_add = array_diff($content_ids, $current_content);
        foreach ($to_add as $post_id) {
            wp_set_object_terms($post_id, $assistant_id, 'kb_assistant', true);
        }
    }
}
