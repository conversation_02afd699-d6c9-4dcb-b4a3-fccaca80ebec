<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for managing OpenAI prompt templates
 */
class QKB_OpenAI_Prompt_Templates
{
    /**
     * Constructor
     */
    public function __construct()
    {
        add_action('admin_menu', [$this, 'add_templates_page']);
        add_action('admin_post_qkb_save_prompt_template', [$this, 'save_prompt_template']);
        add_action('admin_post_qkb_delete_prompt_template', [$this, 'delete_prompt_template']);
        add_action('admin_post_qkb_save_template_category', [$this, 'save_template_category']);
        add_action('admin_post_qkb_delete_template_category', [$this, 'delete_template_category']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_scripts']);
        add_action('wp_ajax_qkb_generate_template_content', [$this, 'ajax_generate_template_content']);
    }

    /**
     * Enqueue scripts and styles for the prompt templates page
     *
     * @param string $hook Current admin page hook
     */
    public function enqueue_scripts($hook)
    {
        if ($hook !== 'kb_knowledge_base_page_qkb-prompt-templates') {
            return;
        }

        // Enqueue the modern style for prompt templates
        wp_enqueue_style('qkb-modern-prompt-templates', QKB_PLUGIN_URL . 'assets/css/modern-prompt-templates.css', [], QKB_VERSION);

        // Enqueue root style variables if they exist
        if (file_exists(QKB_PLUGIN_DIR . 'assets/css/root-style.css')) {
            wp_enqueue_style('qkb-root-style', QKB_PLUGIN_URL . 'assets/css/root-style.css', [], QKB_VERSION);
        }

        // Enqueue the prompt template generator script
        wp_enqueue_script('qkb-prompt-template-generator', QKB_PLUGIN_URL . 'assets/js/prompt-template-generator.js', ['jquery'], QKB_VERSION, true);

        // Pass data to the script
        wp_localize_script('qkb-prompt-template-generator', 'qkbPromptTemplateGenerator', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('qkb_prompt_template_generator_nonce'),
            'generating' => __('Generating...', 'q-knowledge-base'),
            'generate' => __('Generate', 'q-knowledge-base'),
            'errorMessage' => __('An error occurred. Please try again.', 'q-knowledge-base')
        ]);
    }

    /**
     * Add prompt templates admin page
     */
    public function add_templates_page()
    {
        add_submenu_page(
            'edit.php?post_type=kb_knowledge_base',
            __('Prompt Templates', 'q-knowledge-base'),
            __('Prompt Templates', 'q-knowledge-base'),
            'manage_options',
            'qkb-prompt-templates',
            [$this, 'render_templates_page']
        );
    }

    /**
     * Render the prompt templates admin page
     */
    public function render_templates_page()
    {
        // Check if we're editing a template
        $edit_id = isset($_GET['edit']) ? intval($_GET['edit']) : 0;
        $template_to_edit = null;

        if ($edit_id) {
            $templates = $this->get_templates();
            foreach ($templates as $template) {
                if ($template['id'] == $edit_id) {
                    $template_to_edit = $template;
                    break;
                }
            }
        }

        ?>
        <div class="wrap qkb-prompt-templates-wrap">
            <h1><?php echo $edit_id ? __('Edit Prompt Template', 'q-knowledge-base') : __('Prompt Templates', 'q-knowledge-base'); ?>
            </h1>

            <?php if (isset($_GET['saved'])): ?>
                <div class="notice notice-success is-dismissible">
                    <p><?php _e('Template saved successfully.', 'q-knowledge-base'); ?></p>
                </div>
            <?php endif; ?>

            <?php if (isset($_GET['deleted'])): ?>
                <div class="notice notice-success is-dismissible">
                    <p><?php _e('Template deleted successfully.', 'q-knowledge-base'); ?></p>
                </div>
            <?php endif; ?>

            <?php if ($template_to_edit): ?>
                <!-- Edit Template Form -->
                <form method="post" action="<?php echo admin_url('admin-post.php'); ?>" class="qkb-template-form">
                    <input type="hidden" name="action" value="qkb_save_prompt_template">
                    <input type="hidden" name="template_id" value="<?php echo esc_attr($edit_id); ?>">
                    <?php wp_nonce_field('qkb_save_prompt_template', 'qkb_prompt_template_nonce'); ?>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><label for="template_name"><?php _e('Template Name', 'q-knowledge-base'); ?></label>
                            </th>
                            <td>
                                <input type="text" name="template_name" id="template_name" class="regular-text"
                                    value="<?php echo esc_attr($template_to_edit['name']); ?>" required>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label
                                    for="template_description"><?php _e('Description', 'q-knowledge-base'); ?></label></th>
                            <td>
                                <textarea name="template_description" id="template_description" class="large-text"
                                    rows="3"><?php echo esc_textarea($template_to_edit['description']); ?></textarea>
                            </td>
                        </tr>
                        <!-- Q-AI Setup Fields -->
                        <tr>
                            <th scope="row"><label for="tone"><?php _e('Tone', 'q-knowledge-base'); ?></label></th>
                            <td>
                                <input type="text" name="tone" id="tone" class="regular-text"
                                    value="<?php echo isset($template_to_edit['tone']) ? esc_attr($template_to_edit['tone']) : ''; ?>">
                                <p class="description">
                                    <?php _e('The tone of the response (e.g., professional, friendly, technical)', 'q-knowledge-base'); ?>
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="style"><?php _e('Style', 'q-knowledge-base'); ?></label></th>
                            <td>
                                <input type="text" name="style" id="style" class="regular-text"
                                    value="<?php echo isset($template_to_edit['style']) ? esc_attr($template_to_edit['style']) : ''; ?>">
                                <p class="description">
                                    <?php _e('The writing style (e.g., concise, detailed, conversational)', 'q-knowledge-base'); ?>
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="format"><?php _e('Format', 'q-knowledge-base'); ?></label></th>
                            <td>
                                <input type="text" name="format" id="format" class="regular-text"
                                    value="<?php echo isset($template_to_edit['format']) ? esc_attr($template_to_edit['format']) : ''; ?>">
                                <p class="description">
                                    <?php _e('The format of the response (e.g., bullet points, paragraphs, step-by-step)', 'q-knowledge-base'); ?>
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="audience"><?php _e('Audience', 'q-knowledge-base'); ?></label></th>
                            <td>
                                <input type="text" name="audience" id="audience" class="regular-text"
                                    value="<?php echo isset($template_to_edit['audience']) ? esc_attr($template_to_edit['audience']) : ''; ?>">
                                <p class="description">
                                    <?php _e('The target audience (e.g., beginners, experts, customers)', 'q-knowledge-base'); ?>
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="purpose"><?php _e('Purpose', 'q-knowledge-base'); ?></label></th>
                            <td>
                                <input type="text" name="purpose" id="purpose" class="regular-text"
                                    value="<?php echo isset($template_to_edit['purpose']) ? esc_attr($template_to_edit['purpose']) : ''; ?>">
                                <p class="description">
                                    <?php _e('The purpose of the template (e.g., answer questions, provide instructions)', 'q-knowledge-base'); ?>
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="constraints"><?php _e('Constraints', 'q-knowledge-base'); ?></label></th>
                            <td>
                                <textarea name="constraints" id="constraints" class="large-text"
                                    rows="3"><?php echo isset($template_to_edit['constraints']) ? esc_textarea($template_to_edit['constraints']) : ''; ?></textarea>
                                <p class="description">
                                    <?php _e('Any constraints or limitations (e.g., word count, specific requirements)', 'q-knowledge-base'); ?>
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"></th>
                            <td>
                                <button type="button" id="generate-template-content"
                                    class="button"><?php _e('Generate Template Content', 'q-knowledge-base'); ?></button>
                                <span class="spinner" style="float: none; margin-top: 0;"></span>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label
                                    for="template_content"><?php _e('Template Content', 'q-knowledge-base'); ?></label></th>
                            <td>
                                <textarea name="template_content" id="template_content" class="large-text code" rows="15"
                                    required><?php echo esc_textarea($template_to_edit['content']); ?></textarea>
                                <p class="description">
                                    <?php _e('You can use the following variables:', 'q-knowledge-base'); ?><br>
                                    <code>{kb_content}</code> - <?php _e('Knowledge base content', 'q-knowledge-base'); ?><br>
                                    <code>{user_query}</code> - <?php _e('User question', 'q-knowledge-base'); ?><br>
                                    <code>{assistant_name}</code> - <?php _e('Assistant name', 'q-knowledge-base'); ?><br>
                                    <code>{site_name}</code> - <?php _e('Website name', 'q-knowledge-base'); ?><br>
                                    <code>{quick_action:NAME}</code> -
                                    <?php _e('Trigger a specific quick action by name', 'q-knowledge-base'); ?><br>
                                    <code>{assistant_action:NAME}</code> -
                                    <?php _e('Trigger a specific assistant action by name', 'q-knowledge-base'); ?>
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="template_type"><?php _e('Template Type', 'q-knowledge-base'); ?></label>
                            </th>
                            <td>
                                <select name="template_type" id="template_type">
                                    <option value="system" <?php selected($template_to_edit['type'], 'system'); ?>>
                                        <?php _e('System Prompt', 'q-knowledge-base'); ?>
                                    </option>
                                    <option value="user" <?php selected($template_to_edit['type'], 'user'); ?>>
                                        <?php _e('User Prompt', 'q-knowledge-base'); ?>
                                    </option>
                                    <option value="assistant" <?php selected($template_to_edit['type'], 'assistant'); ?>>
                                        <?php _e('Assistant Prompt', 'q-knowledge-base'); ?>
                                    </option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="template_category"><?php _e('Category', 'q-knowledge-base'); ?></label></th>
                            <td>
                                <select name="template_category" id="template_category">
                                    <option value="0"><?php _e('Uncategorized', 'q-knowledge-base'); ?></option>
                                    <?php
                                    $categories = $this->get_template_categories();
                                    foreach ($categories as $category):
                                        $selected = isset($template_to_edit['category_id']) && $template_to_edit['category_id'] == $category['id'] ? 'selected' : '';
                                        ?>
                                        <option value="<?php echo esc_attr($category['id']); ?>" <?php echo $selected; ?>>
                                            <?php echo esc_html($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </td>
                        </tr>
                    </table>

                    <p class="submit">
                        <button type="submit"
                            class="button button-primary"><?php _e('Save Template', 'q-knowledge-base'); ?></button>
                        <a href="<?php echo admin_url('edit.php?post_type=kb_knowledge_base&page=qkb-prompt-templates'); ?>"
                            class="button"><?php _e('Cancel', 'q-knowledge-base'); ?></a>
                    </p>
                </form>

            <?php else: ?>
                <!-- Templates List -->
                <div class="qkb-templates-intro">
                    <p><?php _e('Prompt templates allow you to create reusable prompts for different scenarios.', 'q-knowledge-base'); ?>
                    </p>

                    <a href="<?php echo admin_url('edit.php?post_type=kb_knowledge_base&page=qkb-prompt-templates&action=new'); ?>"
                        class="button button-primary"><?php _e('Add New Template', 'q-knowledge-base'); ?></a>
                </div>

                <div class="qkb-templates-list">
                    <table>
                        <thead>
                            <tr>
                                <th><?php _e('Name', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Description', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Type', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Actions', 'q-knowledge-base'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $templates = $this->get_templates();

                            if (empty($templates)) {
                                echo '<tr><td colspan="4">' . __('No templates found.', 'q-knowledge-base') . '</td></tr>';
                            } else {
                                foreach ($templates as $template) {
                                    $edit_url = add_query_arg(['page' => 'qkb-prompt-templates', 'edit' => $template['id']], admin_url('edit.php?post_type=kb_knowledge_base'));
                                    $delete_url = wp_nonce_url(
                                        add_query_arg(
                                            [
                                                'action' => 'qkb_delete_prompt_template',
                                                'template_id' => $template['id']
                                            ],
                                            admin_url('admin-post.php')
                                        ),
                                        'qkb_delete_prompt_template_' . $template['id'],
                                        'qkb_prompt_template_nonce'
                                    );

                                    $type_labels = [
                                        'system' => __('System', 'q-knowledge-base'),
                                        'user' => __('User', 'q-knowledge-base'),
                                        'assistant' => __('Assistant', 'q-knowledge-base')
                                    ];

                                    // Add highlight class if this template was just saved
                                    $highlight_class = '';
                                    if (isset($_GET['saved']) && isset($_GET['template_id']) && $_GET['template_id'] == $template['id']) {
                                        $highlight_class = ' class="qkb-highlight"';
                                    }

                                    echo '<tr' . $highlight_class . '>';
                                    echo '<td><strong>' . esc_html($template['name']) . '</strong></td>';
                                    echo '<td>' . esc_html($template['description']) . '</td>';
                                    echo '<td>' . esc_html($type_labels[$template['type']]) . '</td>';
                                    echo '<td class="qkb-template-actions">';
                                    echo '<a href="' . esc_url($edit_url) . '" class="button button-small">' . __('Edit', 'q-knowledge-base') . '</a> ';
                                    echo '<a href="' . esc_url($delete_url) . '" class="button button-small" onclick="return confirm(\'' . __('Are you sure you want to delete this template?', 'q-knowledge-base') . '\')">' . __('Delete', 'q-knowledge-base') . '</a>';
                                    echo '</td>';
                                    echo '</tr>';
                                }
                            }
                            ?>
                        </tbody>
                    </table>
                </div>

                <?php if (isset($_GET['action']) && $_GET['action'] === 'new'): ?>
                    <!-- New Template Form -->
                    <h2><?php _e('Add New Template', 'q-knowledge-base'); ?></h2>

                    <form method="post" action="<?php echo admin_url('admin-post.php'); ?>" class="qkb-template-form">
                        <input type="hidden" name="action" value="qkb_save_prompt_template">
                        <?php wp_nonce_field('qkb_save_prompt_template', 'qkb_prompt_template_nonce'); ?>

                        <table class="form-table">
                            <tr>
                                <th scope="row"><label for="template_name"><?php _e('Template Name', 'q-knowledge-base'); ?></label>
                                </th>
                                <td>
                                    <input type="text" name="template_name" id="template_name" class="regular-text" required>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><label
                                        for="template_description"><?php _e('Description', 'q-knowledge-base'); ?></label></th>
                                <td>
                                    <textarea name="template_description" id="template_description" class="large-text"
                                        rows="3"></textarea>
                                </td>
                            </tr>
                            <!-- Q-AI Setup Fields -->
                            <tr>
                                <th scope="row"><label for="tone"><?php _e('Tone', 'q-knowledge-base'); ?></label></th>
                                <td>
                                    <input type="text" name="tone" id="tone" class="regular-text">
                                    <p class="description">
                                        <?php _e('The tone of the response (e.g., professional, friendly, technical)', 'q-knowledge-base'); ?>
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="style"><?php _e('Style', 'q-knowledge-base'); ?></label></th>
                                <td>
                                    <input type="text" name="style" id="style" class="regular-text">
                                    <p class="description">
                                        <?php _e('The writing style (e.g., concise, detailed, conversational)', 'q-knowledge-base'); ?>
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="format"><?php _e('Format', 'q-knowledge-base'); ?></label></th>
                                <td>
                                    <input type="text" name="format" id="format" class="regular-text">
                                    <p class="description">
                                        <?php _e('The format of the response (e.g., bullet points, paragraphs, step-by-step)', 'q-knowledge-base'); ?>
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="audience"><?php _e('Audience', 'q-knowledge-base'); ?></label></th>
                                <td>
                                    <input type="text" name="audience" id="audience" class="regular-text">
                                    <p class="description">
                                        <?php _e('The target audience (e.g., beginners, experts, customers)', 'q-knowledge-base'); ?>
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="purpose"><?php _e('Purpose', 'q-knowledge-base'); ?></label></th>
                                <td>
                                    <input type="text" name="purpose" id="purpose" class="regular-text">
                                    <p class="description">
                                        <?php _e('The purpose of the template (e.g., answer questions, provide instructions)', 'q-knowledge-base'); ?>
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="constraints"><?php _e('Constraints', 'q-knowledge-base'); ?></label></th>
                                <td>
                                    <textarea name="constraints" id="constraints" class="large-text" rows="3"></textarea>
                                    <p class="description">
                                        <?php _e('Any constraints or limitations (e.g., word count, specific requirements)', 'q-knowledge-base'); ?>
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"></th>
                                <td>
                                    <button type="button" id="generate-template-content"
                                        class="button"><?php _e('Generate Template Content', 'q-knowledge-base'); ?></button>
                                    <span class="spinner" style="float: none; margin-top: 0;"></span>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><label
                                        for="template_content"><?php _e('Template Content', 'q-knowledge-base'); ?></label></th>
                                <td>
                                    <textarea name="template_content" id="template_content" class="large-text code" rows="15"
                                        required></textarea>
                                    <p class="description">
                                        <?php _e('You can use the following variables:', 'q-knowledge-base'); ?><br>
                                        <code>{kb_content}</code> - <?php _e('Knowledge base content', 'q-knowledge-base'); ?><br>
                                        <code>{user_query}</code> - <?php _e('User question', 'q-knowledge-base'); ?><br>
                                        <code>{assistant_name}</code> - <?php _e('Assistant name', 'q-knowledge-base'); ?><br>
                                        <code>{site_name}</code> - <?php _e('Website name', 'q-knowledge-base'); ?><br>
                                        <code>{quick_action:NAME}</code> -
                                        <?php _e('Trigger a specific quick action by name', 'q-knowledge-base'); ?>
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="template_type"><?php _e('Template Type', 'q-knowledge-base'); ?></label>
                                </th>
                                <td>
                                    <select name="template_type" id="template_type">
                                        <option value="system"><?php _e('System Prompt', 'q-knowledge-base'); ?></option>
                                        <option value="user"><?php _e('User Prompt', 'q-knowledge-base'); ?></option>
                                        <option value="assistant"><?php _e('Assistant Prompt', 'q-knowledge-base'); ?></option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><label for="template_category"><?php _e('Category', 'q-knowledge-base'); ?></label></th>
                                <td>
                                    <select name="template_category" id="template_category">
                                        <option value="0"><?php _e('Uncategorized', 'q-knowledge-base'); ?></option>
                                        <?php
                                        $categories = $this->get_template_categories();
                                        foreach ($categories as $category):
                                            ?>
                                            <option value="<?php echo esc_attr($category['id']); ?>">
                                                <?php echo esc_html($category['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </td>
                            </tr>
                        </table>

                        <p class="submit">
                            <button type="submit"
                                class="button button-primary"><?php _e('Save Template', 'q-knowledge-base'); ?></button>
                            <a href="<?php echo admin_url('edit.php?post_type=kb_knowledge_base&page=qkb-prompt-templates'); ?>"
                                class="button"><?php _e('Cancel', 'q-knowledge-base'); ?></a>
                        </p>
                    </form>
                <?php endif; ?>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * Save a prompt template
     */
    public function save_prompt_template()
    {
        // Check nonce
        if (!isset($_POST['qkb_prompt_template_nonce']) || !wp_verify_nonce($_POST['qkb_prompt_template_nonce'], 'qkb_save_prompt_template')) {
            wp_die(__('Security check failed', 'q-knowledge-base'));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'q-knowledge-base'));
        }

        // Get form data
        $template_id = isset($_POST['template_id']) ? intval($_POST['template_id']) : 0;
        $template_name = sanitize_text_field($_POST['template_name']);
        $template_description = sanitize_textarea_field($_POST['template_description']);
        $template_content = wp_kses_post($_POST['template_content']);
        $template_type = sanitize_text_field($_POST['template_type']);
        $category_id = isset($_POST['template_category']) ? intval($_POST['template_category']) : 0;

        // Get Q-AI Setup fields
        $tone = isset($_POST['tone']) ? sanitize_text_field($_POST['tone']) : '';
        $style = isset($_POST['style']) ? sanitize_text_field($_POST['style']) : '';
        $format = isset($_POST['format']) ? sanitize_text_field($_POST['format']) : '';
        $audience = isset($_POST['audience']) ? sanitize_text_field($_POST['audience']) : '';
        $purpose = isset($_POST['purpose']) ? sanitize_text_field($_POST['purpose']) : '';
        $constraints = isset($_POST['constraints']) ? sanitize_textarea_field($_POST['constraints']) : '';

        // Validate template type
        if (!in_array($template_type, ['system', 'user', 'assistant'])) {
            $template_type = 'system';
        }

        // Get existing templates
        $templates = $this->get_templates();

        if ($template_id) {
            // Update existing template
            foreach ($templates as $key => $template) {
                if ($template['id'] == $template_id) {
                    $templates[$key] = [
                        'id' => $template_id,
                        'name' => $template_name,
                        'description' => $template_description,
                        'content' => $template_content,
                        'type' => $template_type,
                        'category_id' => $category_id,
                        'tone' => $tone,
                        'style' => $style,
                        'format' => $format,
                        'audience' => $audience,
                        'purpose' => $purpose,
                        'constraints' => $constraints
                    ];
                    break;
                }
            }
        } else {
            // Add new template
            $new_id = 1;
            if (!empty($templates)) {
                $ids = array_column($templates, 'id');
                $new_id = max($ids) + 1;
            }

            $templates[] = [
                'id' => $new_id,
                'name' => $template_name,
                'description' => $template_description,
                'content' => $template_content,
                'type' => $template_type,
                'category_id' => $category_id,
                'tone' => $tone,
                'style' => $style,
                'format' => $format,
                'audience' => $audience,
                'purpose' => $purpose,
                'constraints' => $constraints
            ];
        }

        // Save templates
        update_option('qkb_prompt_templates', $templates);

        // Clear any cached templates
        $this->get_templates(true);

        // Redirect back to templates page
        $redirect_url = add_query_arg(
            [
                'page' => 'qkb-prompt-templates',
                'saved' => '1',
                'template_id' => $template_id ? $template_id : $new_id
            ],
            admin_url('edit.php?post_type=kb_knowledge_base')
        );
        wp_redirect($redirect_url);
        exit;
    }

    /**
     * Delete a prompt template
     */
    public function delete_prompt_template()
    {
        $template_id = isset($_GET['template_id']) ? intval($_GET['template_id']) : 0;

        // Check nonce
        if (!isset($_GET['qkb_prompt_template_nonce']) || !wp_verify_nonce($_GET['qkb_prompt_template_nonce'], 'qkb_delete_prompt_template_' . $template_id)) {
            wp_die(__('Security check failed', 'q-knowledge-base'));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'q-knowledge-base'));
        }

        // Get existing templates
        $templates = $this->get_templates();

        // Remove the template
        $templates = array_filter($templates, function ($template) use ($template_id) {
            return $template['id'] != $template_id;
        });

        // Re-index array
        $templates = array_values($templates);

        // Save templates
        update_option('qkb_prompt_templates', $templates);

        // Clear any cached templates
        $this->get_templates(true);

        // Redirect back to templates page
        wp_redirect(admin_url('edit.php?post_type=kb_knowledge_base&page=qkb-prompt-templates&deleted=1'));
        exit;
    }

    /**
     * Save a template category
     */
    public function save_template_category()
    {
        // Check nonce
        if (!isset($_POST['qkb_category_nonce']) || !wp_verify_nonce($_POST['qkb_category_nonce'], 'qkb_save_template_category')) {
            wp_die(__('Security check failed', 'q-knowledge-base'));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'q-knowledge-base'));
        }

        // Get form data
        $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;
        $category_name = sanitize_text_field($_POST['category_name']);
        $category_description = sanitize_textarea_field($_POST['category_description']);
        $category_color = sanitize_hex_color($_POST['category_color'] ?? '#0073aa');

        // Get existing categories
        $categories = $this->get_template_categories();

        if ($category_id) {
            // Update existing category
            foreach ($categories as $key => $category) {
                if ($category['id'] == $category_id) {
                    $categories[$key] = [
                        'id' => $category_id,
                        'name' => $category_name,
                        'description' => $category_description,
                        'color' => $category_color
                    ];
                    break;
                }
            }
        } else {
            // Add new category
            $new_id = 1;
            if (!empty($categories)) {
                $ids = array_column($categories, 'id');
                $new_id = max($ids) + 1;
            }

            $categories[] = [
                'id' => $new_id,
                'name' => $category_name,
                'description' => $category_description,
                'color' => $category_color
            ];
        }

        // Save categories
        update_option('qkb_template_categories', $categories);

        // Redirect back to templates page with categories tab active
        wp_redirect(admin_url('edit.php?post_type=kb_knowledge_base&page=qkb-prompt-templates&tab=categories&saved=1'));
        exit;
    }

    /**
     * Delete a template category
     */
    public function delete_template_category()
    {
        $category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : 0;

        // Check nonce
        if (!isset($_GET['qkb_category_nonce']) || !wp_verify_nonce($_GET['qkb_category_nonce'], 'qkb_delete_template_category_' . $category_id)) {
            wp_die(__('Security check failed', 'q-knowledge-base'));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'q-knowledge-base'));
        }

        // Get existing categories
        $categories = $this->get_template_categories();

        // Remove the category
        $categories = array_filter($categories, function ($category) use ($category_id) {
            return $category['id'] != $category_id;
        });

        // Re-index array
        $categories = array_values($categories);

        // Save categories
        update_option('qkb_template_categories', $categories);

        // Update templates to remove this category
        $templates = $this->get_templates();
        foreach ($templates as $key => $template) {
            if (isset($template['category_id']) && $template['category_id'] == $category_id) {
                $templates[$key]['category_id'] = 0; // Set to uncategorized
            }
        }
        update_option('qkb_prompt_templates', $templates);

        // Redirect back to templates page with categories tab active
        wp_redirect(admin_url('edit.php?post_type=kb_knowledge_base&page=qkb-prompt-templates&tab=categories&deleted=1'));
        exit;
    }

    /**
     * Get all template categories
     *
     * @return array Array of categories
     */
    public function get_template_categories()
    {
        $categories = get_option('qkb_template_categories', []);

        if (empty($categories)) {
            // Add default categories
            $categories = $this->get_default_categories();
            update_option('qkb_template_categories', $categories);
        }

        return $categories;
    }

    /**
     * Get default template categories
     *
     * @return array Array of default categories
     */
    private function get_default_categories()
    {
        return [
            [
                'id' => 1,
                'name' => 'General',
                'description' => 'General purpose templates',
                'color' => '#0073aa'
            ],
            [
                'id' => 2,
                'name' => 'Technical',
                'description' => 'Templates for technical documentation',
                'color' => '#d54e21'
            ],
            [
                'id' => 3,
                'name' => 'Marketing',
                'description' => 'Templates for marketing content',
                'color' => '#46b450'
            ]
        ];
    }

    /**
     * Get all prompt templates
     *
     * @param bool $force_refresh Whether to force a refresh from the database
     * @return array Array of templates
     */
    public function get_templates($force_refresh = false)
    {
        static $cached_templates = null;

        // Return cached templates if available and not forcing refresh
        if (!$force_refresh && $cached_templates !== null) {
            return $cached_templates;
        }

        $templates = get_option('qkb_prompt_templates', []);

        if (empty($templates)) {
            // Add default templates
            $templates = $this->get_default_templates();
            update_option('qkb_prompt_templates', $templates);
        }

        // Cache the templates
        $cached_templates = $templates;

        return $templates;
    }

    /**
     * Get a specific template by ID
     *
     * @param int $template_id Template ID
     * @param bool $force_refresh Whether to force a refresh from the database
     * @return array|null Template data or null if not found
     */
    public function get_template($template_id, $force_refresh = false)
    {
        $templates = $this->get_templates($force_refresh);

        foreach ($templates as $template) {
            if ($template['id'] == $template_id) {
                return $template;
            }
        }

        return null;
    }

    /**
     * Get default templates
     *
     * @return array Array of default templates
     */
    private function get_default_templates()
    {
        return [
            [
                'id' => 1,
                'name' => 'Standard Knowledge Base',
                'description' => 'Default template for knowledge base queries',
                'content' => "You are a helpful AI assistant that answers questions based on the provided knowledge base content.\n\nKnowledge Base Content:\n{kb_content}\n\nPlease answer the user's question based only on the information provided above. If the information is not in the knowledge base, politely state that you don't have that information.",
                'type' => 'system',
                'category_id' => 1
            ],
            [
                'id' => 2,
                'name' => 'Technical Documentation',
                'description' => 'Template for technical documentation queries',
                'content' => "You are a technical documentation assistant for {site_name}. Your role is to provide clear, accurate technical information based on the documentation provided.\n\nDocumentation Content:\n{kb_content}\n\nWhen answering, use a technical but accessible tone. Include code examples where appropriate. If the information is not in the documentation, acknowledge this and suggest where the user might find the information.",
                'type' => 'system',
                'category_id' => 2
            ],
            [
                'id' => 3,
                'name' => 'FAQ Generator',
                'description' => 'Template for generating FAQs from content',
                'content' => "Based on the following content, generate a comprehensive FAQ section with at least 5 questions and answers. Format the output in markdown with clear headings and concise answers.\n\nContent to analyze:\n{kb_content}",
                'type' => 'user',
                'category_id' => 3
            ]
        ];
    }

    /**
     * Process a template by replacing variables with actual content
     *
     * @param string $template_content Template content with variables
     * @param array $variables Array of variables to replace
     * @return string Processed template
     */
    public function process_template($template_content, $variables)
    {
        $replacements = [
            '{kb_content}' => $variables['kb_content'] ?? '',
            '{user_query}' => $variables['user_query'] ?? '',
            '{assistant_name}' => $variables['assistant_name'] ?? 'AI Assistant',
            '{site_name}' => get_bloginfo('name')
        ];

        // Process the template with standard variables
        $processed_template = str_replace(array_keys($replacements), array_values($replacements), $template_content);

        // We'll keep the {quick_action:NAME} and {assistant_action:NAME} patterns in the template
        // The JavaScript will handle them directly in the chat interface

        return $processed_template;
    }

    /**
     * AJAX handler for generating template content
     */
    public function ajax_generate_template_content()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_prompt_template_generator_nonce')) {
            wp_send_json_error('Security check failed');
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('You do not have sufficient permissions');
        }

        // Get form data
        $template_name = isset($_POST['template_name']) ? sanitize_text_field($_POST['template_name']) : '';
        $template_description = isset($_POST['template_description']) ? sanitize_textarea_field($_POST['template_description']) : '';
        $template_type = isset($_POST['template_type']) ? sanitize_text_field($_POST['template_type']) : 'system';

        // Get Q-AI Setup fields
        $tone = isset($_POST['tone']) ? sanitize_text_field($_POST['tone']) : '';
        $style = isset($_POST['style']) ? sanitize_text_field($_POST['style']) : '';
        $format = isset($_POST['format']) ? sanitize_text_field($_POST['format']) : '';
        $audience = isset($_POST['audience']) ? sanitize_text_field($_POST['audience']) : '';
        $purpose = isset($_POST['purpose']) ? sanitize_text_field($_POST['purpose']) : '';
        $constraints = isset($_POST['constraints']) ? sanitize_textarea_field($_POST['constraints']) : '';

        // Validate required fields
        if (empty($template_name) || empty($purpose)) {
            wp_send_json_error('Template name and purpose are required');
        }

        // Build the prompt for OpenAI
        $prompt = "Generate a " . $template_type . " prompt template for an AI assistant with the following specifications:\n\n";
        $prompt .= "Template Name: " . $template_name . "\n";

        if (!empty($template_description)) {
            $prompt .= "Description: " . $template_description . "\n";
        }

        if (!empty($tone)) {
            $prompt .= "Tone: " . $tone . "\n";
        }

        if (!empty($style)) {
            $prompt .= "Style: " . $style . "\n";
        }

        if (!empty($format)) {
            $prompt .= "Format: " . $format . "\n";
        }

        if (!empty($audience)) {
            $prompt .= "Audience: " . $audience . "\n";
        }

        if (!empty($purpose)) {
            $prompt .= "Purpose: " . $purpose . "\n";
        }

        if (!empty($constraints)) {
            $prompt .= "Constraints: " . $constraints . "\n";
        }

        $prompt .= "\nThe template should include placeholders for dynamic content such as {kb_content}, {user_query}, {assistant_name}, and {site_name}. Make sure the template is well-structured and follows best practices for AI prompting.";

        // Specify the exact format structure
        $prompt .= "\n\nPlease format the template exactly as follows:

[System Instruction / Role]
You are a [ROLE/EXPERTISE], skilled in [DOMAIN]. Your task is to [OBJECTIVE]. Always [STYLE/TONE/PREFERENCES].

[Task Instruction]
Your job is to [EXACT TASK]. Focus on [GOALS/REQUIREMENTS], ensure [CONSTRAINTS/CRITERIA].

[User Context (Optional)]
The user is working on [PROJECT/DOMAIN]. This is part of [WIDER OBJECTIVE].

[Data Input (Dynamic)]
Data:
- Topic: {user_query}
- Keywords: [KEYWORDS]
- Tone: " . (!empty($tone) ? $tone : "[TONE]") . "
- Audience: " . (!empty($audience) ? $audience : "[AUDIENCE]") . "
- Limitations: " . (!empty($constraints) ? "See constraints below" : "[LIMITATIONS]") . "

[Output Requirements]
- Output format: " . (!empty($format) ? $format : "[FORMAT]") . "
- Style: " . (!empty($style) ? $style : "[STYLE]") . "
- Structure: [STRUCTURE]
- Language: [LANGUAGE]

[Example (Optional)]
Example output:
[Insert a short, model-style answer for few-shot prompting]

[Call to Action (Optional)]
Make the result [ACTIONABLE/INSIGHTFUL/USEFUL] for " . (!empty($audience) ? $audience : "[AUDIENCE]") . ".

Knowledge Base Content:
{kb_content}";

        // Add a note to replace placeholders with appropriate content
        $prompt .= "\n\nReplace all placeholders in square brackets with appropriate content based on the specifications provided. Keep the section headers exactly as shown, but fill in the content between them appropriately.";

        // Get API key
        $api_key = get_option('qkb_openai_api_key');
        if (!$api_key) {
            wp_send_json_error('API key not configured');
        }

        // Make API request to OpenAI
        $response = $this->generate_content_with_openai($prompt, $api_key);

        if (is_wp_error($response)) {
            wp_send_json_error($response->get_error_message());
        }

        wp_send_json_success(['content' => $response]);
    }

    /**
     * Generate content using OpenAI API
     *
     * @param string $prompt The prompt to send to OpenAI
     * @param string $api_key API key
     * @return string|WP_Error Generated content or error
     */
    private function generate_content_with_openai($prompt, $api_key)
    {
        $url = 'https://api.openai.com/v1/chat/completions';

        $headers = [
            'Authorization' => 'Bearer ' . $api_key,
            'Content-Type' => 'application/json'
        ];

        $body = [
            'model' => 'gpt-4-turbo',
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are a prompt engineering expert who creates high-quality templates for AI assistants. You follow instructions precisely and format your output exactly as requested.'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'temperature' => 0.7,
            'max_tokens' => 2000
        ];

        $response = wp_remote_post($url, [
            'headers' => $headers,
            'body' => json_encode($body),
            'timeout' => 30
        ]);

        if (is_wp_error($response)) {
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            $error_message = wp_remote_retrieve_response_message($response);
            return new WP_Error('openai_error', 'OpenAI API Error: ' . $error_message);
        }

        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        if (!isset($response_body['choices'][0]['message']['content'])) {
            return new WP_Error('openai_error', 'Invalid response from OpenAI API');
        }

        return $response_body['choices'][0]['message']['content'];
    }
}

// The class is initialized by the OpenAI Handler
