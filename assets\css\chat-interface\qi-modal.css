/* Modal Styling */
.qi-modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--qkb-primary-light);
    z-index: var(--qkb-z-index-overlay);
    -webkit-backdrop-filter: var(--qkb-blur);
    backdrop-filter: var(--qkb-blur);
}

.qi-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--qkb-bg);
    border-radius: var(--qkb-radius-sm);
    padding: var(--qkb-gap-xl);
    box-shadow: var(--qkb-shadow);
    max-width: 400px;
    width: 100%;
    z-index: var(--qkb-z-index-modal);
    text-align: center;
}

.qi-modal-title {
    font-size: var(--qkb-font-size-lg);
    font-weight: var(--qkb-font-weight-semibold);
    color: var(--qkb-text);
}

.qi-modal-content {
    margin-bottom: var(--qkb-gap-xl);
    color: var(--qkb-text-light);
    text-align: center;
    font-size: var(--qkb-font-size-sm);
}

.qi-modal-actions {
    display: flex;
    gap: var(--qkb-gap-md);
    justify-content: center;
}

.qi-modal-button {
    padding: var(--qkb-padding-sm);
    border-radius: var(--qkb-radius-xs);
    border: none;
    cursor: pointer;
    font-size: var(--qkb-font-size-sm);
    font-weight: var(--qkb-font-weight-medium);
    transition: var(--qkb-transition);
}

.qi-modal-button.qi-confirm {
    background: var(--qkb-primary);
    color: var(--qkb-bg);
}

.qi-modal-button.qi-cancel {
    background: var(--qkb-bg-light);
    color: var(--qkb-text-light);
}

.qi-modal-button:hover {
    transform: var(--qkb-transform);
}
