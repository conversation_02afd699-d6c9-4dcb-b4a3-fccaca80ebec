.qi-export-modal {
    position: fixed;
    inset: 0;
    background: var(--qkb-primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--qkb-z-index-modal);
    opacity: 0;
    visibility: hidden;
    transition: var(--qkb-transition);
    -webkit-backdrop-filter: var(--qkb-blur);
    backdrop-filter: var(--qkb-blur);
}

.qi-export-modal.show {
    opacity: 1;
    visibility: visible;
}

.qi-export-modal-content {
    background: var(--qkb-bg);
    border-radius: var(--qkb-radius-sm);
    width: 100%;
    max-width: 400px;
    box-shadow: var(--qkb-shadow);
    transform: var(--qkb-transform);
    transition: var(--qkb-transition);
    -webkit-border-radius: var(--qkb-radius-sm);
    -moz-border-radius: var(--qkb-radius-sm);
    -ms-border-radius: var(--qkb-radius-sm);
    -o-border-radius: var(--qkb-radius-sm);
}

.qi-export-modal-header {
    padding: var(--qkb-padding-md);
    border-bottom: 1px solid var(--qkb-primary-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.qi-export-modal-header h3 {
    font-size: var(--qkb-font-size-lg);
    font-weight: var(--qkb-font-weight-semibold);
    color: var(--qkb-text);
    margin: 0;
    padding: 0;
}

.qi-export-modal-close {
    background: none;
    border: none;
    font-size: calc(var(--qkb-font-size-lg) + 4px);
    color: var(--qkb-text-light);
    cursor: pointer;
    padding: var(--qkb-gap);
    line-height: 1;
    transition: color 0.2s ease;
}

.qi-export-modal-close:hover {
    background: none;
    color: var(--qkb-error);
    transform: scale(1.05);
}

.qi-export-modal-body {
    padding: var(--qkb-padding-md);
}


.qi-export-type-options {
    display: flex;
    gap:var(--qkb-gap-md);
    justify-content: space-between;
    margin-bottom: var(--qkb-gap-lg);
}

.qi-export-type-options label {
    font-size: var(--qkb-font-size-sm);
    border-radius: var(--qkb-radius-xs);
    -webkit-border-radius: var(--qkb-radius-xs);
    -moz-border-radius: var(--qkb-radius-xs);
    -ms-border-radius: var(--qkb-radius-xs);
    -o-border-radius: var(--qkb-radius-xs);
}

.qi-export-type-options input[type="radio"] {
  accent-color: var(--qkb-primary);
}




.qi-export-type-desc {
    display: block;
    font-size: var(--qkb-font-size-sm);
    color: var(--qkb-text-light);
    margin-top: var(--qkb-gap);
}

.qi-export-format-options {
    display:flex;
    flex-direction: column;
    font-size: var(--qkb-font-size-sm);
    font-weight: bold;
    color: var(--qkb-text);
   
}

.qi-export-format-options p {
 margin-bottom:5px;
    padding:0px;
}


.qi-export-options {
    display: flex;
    gap: var(--qkb-gap-md);
}

.qi-export-option {
    display: flex;
    align-items: center;
    padding: var(--qkb-padding-sm);
    background: var(--qkb-bg-light);
    border: 1px solid var(--qkb-border);
    border-radius: var(--qkb-radius-xs);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.qi-export-option:hover {
    background: var(--qkb-primary-light);
    border-color: var(--qkb-primary);
    transform: var(--qkb-transform);
    -webkit-transform: var(--qkb-transform);
    -moz-transform: var(--qkb-transform);
    -ms-transform: var(--qkb-transform);
    -o-transform: var(--qkb-transform);
}

.qi-export-option i {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--qkb-primary);
}

.qi-export-option-label {
    font-size: var(--qkb-font-size-xs);
    font-weight: 600;
    color: var(--qkb-text);
}

.qi-export-button {
    background: none;
    border: none;
    color: var(--qkb-text-light);
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.qi-export-button:hover {
    color: var(--qkb-primary);
}

.qi-export-format-info {
    display: flex;
    flex-direction: column;
    margin-left: var(--qkb-gap);
}