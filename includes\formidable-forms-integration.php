<?php
/**
 * Formidable Forms Integration
 *
 * This file contains functions to integrate with Formidable Forms
 * and ensure proper form submission from quick actions.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class QKB_Formidable_Forms_Integration
 */
class QKB_Formidable_Forms_Integration
{
    /**
     * Instance of this class
     *
     * @var QKB_Formidable_Forms_Integration
     */
    private static $instance = null;

    /**
     * Get the singleton instance of this class
     *
     * @return QKB_Formidable_Forms_Integration
     */
    public static function get_instance()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct()
    {
        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks()
    {
        // Hook into form processing
        add_action('frm_process_form', array($this, 'process_form_submission'), 10, 4);

        // Hook into form validation to ensure forms in modals are processed
        add_filter('frm_validate_entry', array($this, 'validate_form_submission'), 10, 3);

        // Hook into entry creation to capture successful submissions
        add_action('frm_after_create_entry', array($this, 'after_create_entry'), 30, 2);

        // Add AJAX handler for direct form submission
        add_action('wp_ajax_qkb_submit_formidable_form_direct', array($this, 'ajax_submit_formidable_form_direct'));
        add_action('wp_ajax_nopriv_qkb_submit_formidable_form_direct', array($this, 'ajax_submit_formidable_form_direct'));
    }

    /**
     * Process form submission from quick actions
     *
     * @param int $form_id The ID of the form
     * @param array $params Form parameters
     * @param array $fields Form fields
     * @param array $errors Form errors
     */
    public function process_form_submission($form_id, $params, $fields, $errors)
    {
        // Check if this is a submission from a quick action or assistant action
        if (isset($_POST['qkb_quick_action']) || isset($_POST['qkb_assistant_action']) || isset($_COOKIE['qkb_quick_action_active'])) {
            // Log for debugging
            error_log('Processing Formidable Form submission from quick/assistant action. Form ID: ' . $form_id);
            error_log('Form params: ' . print_r($params, true));

            // Make sure the form is processed even in the modal context
            add_filter('frm_continue_to_create', '__return_true');

            // Force Formidable to create the entry
            add_filter('frm_pre_create_entry', function ($entry) {
                error_log('Pre-create entry filter applied: ' . print_r($entry, true));
                return $entry;
            });

            // Disable validation for quick/assistant actions to ensure the form is processed
            add_filter('frm_validate_field_entry', function ($errors, $field, $value, $args) {
                error_log('Validating field: ' . $field->id . ' with value: ' . print_r($value, true));
                return array(); // Return empty array to bypass validation
            }, 10, 4);
        }
    }

    /**
     * Validate form submission from quick actions
     *
     * @param array $errors Form validation errors
     * @param array $values Form values
     * @param array $params Form parameters
     * @return array Modified errors
     */
    public function validate_form_submission($errors, $values, $params)
    {
        // Check if this is a submission from a quick action or assistant action
        if (isset($_POST['qkb_quick_action']) || isset($_POST['qkb_assistant_action']) || isset($_COOKIE['qkb_quick_action_active'])) {
            error_log('Validating Formidable Form submission from quick/assistant action');
            error_log('Form values: ' . print_r($values, true));
            error_log('Current errors: ' . print_r($errors, true));

            // Force form to be processed even if there are validation errors
            add_filter('frm_continue_to_create', '__return_true');

            // Clear all validation errors for quick/assistant actions
            return array();
        }

        return $errors;
    }

    /**
     * Handle successful entry creation
     *
     * @param int $entry_id The ID of the newly created entry
     * @param int $form_id The ID of the form
     */
    public function after_create_entry($entry_id, $form_id)
    {
        // Check if this is a submission from a quick action or assistant action
        if (isset($_POST['qkb_quick_action']) || isset($_POST['qkb_assistant_action']) || isset($_COOKIE['qkb_quick_action_active'])) {
            error_log('Entry created successfully from quick/assistant action. Entry ID: ' . $entry_id . ', Form ID: ' . $form_id);

            // Clear the cookie
            if (isset($_COOKIE['qkb_quick_action_active'])) {
                setcookie('qkb_quick_action_active', '', time() - 3600, '/');
            }

            // If this is an AJAX request, send a success response
            if (wp_doing_ajax()) {
                wp_send_json_success(array(
                    'entry_id' => $entry_id,
                    'message' => 'Form submitted successfully!'
                ));
                exit;
            }
        }
    }

    /**
     * AJAX handler for direct form submission
     */
    public function ajax_submit_formidable_form_direct()
    {
        // Log the entire request for debugging
        error_log('AJAX request received for qkb_submit_formidable_form_direct');
        error_log('POST data: ' . print_r($_POST, true));

        // Check nonce - make this optional to ensure it works in all contexts
        $nonce_verified = false;
        if (isset($_POST['nonce'])) {
            $nonce_value = $_POST['nonce'];
            $is_ajax_nonce_valid = wp_verify_nonce($nonce_value, 'qkb_ajax_nonce');
            $is_chat_nonce_valid = wp_verify_nonce($nonce_value, 'qi_chat_nonce');

            if ($is_ajax_nonce_valid || $is_chat_nonce_valid) {
                $nonce_verified = true;
            } else {
                error_log('Invalid nonce: ' . $nonce_value);
                // Continue anyway for assistant actions
            }
        }

        // Set a flag to indicate this is from an assistant action
        $_POST['qkb_assistant_action'] = '1';

        // Get form ID
        $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;
        if (!$form_id) {
            error_log('Form ID is required but not provided');
            wp_send_json_error('Form ID is required');
            return;
        }

        // Get form data
        $form_data = isset($_POST['form_data']) ? $_POST['form_data'] : array();

        // Get item_meta directly if provided
        $item_meta_direct = isset($_POST['item_meta']) ? $_POST['item_meta'] : array();

        // Log the submission
        error_log('Direct form submission for form ID: ' . $form_id);
        error_log('Form data: ' . print_r($form_data, true));
        error_log('Direct item_meta: ' . print_r($item_meta_direct, true));

        // Check if Formidable Forms is active
        if (!class_exists('FrmEntry')) {
            error_log('FrmEntry class not found - Formidable Forms may not be active');
            wp_send_json_error('Formidable Forms is not active');
            return;
        }

        try {
            // Prepare form data
            $entry = array();
            $entry['form_id'] = $form_id;
            $entry['item_meta'] = array();

            // First, use the direct item_meta if provided
            if (!empty($item_meta_direct)) {
                foreach ($item_meta_direct as $field_id => $value) {
                    if ($field_id !== '0') { // Skip the item_meta[0] field
                        if (is_array($value)) {
                            $entry['item_meta'][$field_id] = array_map('sanitize_text_field', $value);
                        } else {
                            $entry['item_meta'][$field_id] = sanitize_text_field($value);
                        }
                    }
                }

                error_log('Using direct item_meta data: ' . print_r($entry['item_meta'], true));
            }

            // If we still don't have item_meta or need to supplement it, process the form_data
            if (empty($entry['item_meta']) && !empty($form_data)) {
                // Process all form data to extract field values
                foreach ($form_data as $key => $value) {
                    // Check for standard Formidable field format (item_meta[field_id])
                    if (strpos($key, 'item_meta[') === 0) {
                        preg_match('/item_meta\[(\d+)\]/', $key, $matches);
                        if (isset($matches[1]) && $matches[1] !== '0') { // Skip item_meta[0]
                            $field_id = $matches[1];
                            if (is_array($value)) {
                                $entry['item_meta'][$field_id] = array_map('sanitize_text_field', $value);
                            } else {
                                $entry['item_meta'][$field_id] = sanitize_text_field($value);
                            }
                        }
                    }
                    // Check for our custom field_ format
                    else if (strpos($key, 'field_') === 0) {
                        $field_id = str_replace('field_', '', $key);
                        if (is_array($value)) {
                            $entry['item_meta'][$field_id] = array_map('sanitize_text_field', $value);
                        } else {
                            $entry['item_meta'][$field_id] = sanitize_text_field($value);
                        }
                    }
                    // Check for direct field IDs
                    else if (is_numeric($key)) {
                        $field_id = $key;
                        if (is_array($value)) {
                            $entry['item_meta'][$field_id] = array_map('sanitize_text_field', $value);
                        } else {
                            $entry['item_meta'][$field_id] = sanitize_text_field($value);
                        }
                    }
                }

                error_log('Extracted item_meta from form_data: ' . print_r($entry['item_meta'], true));
            }

            // If we still have no field data, try to extract it from the form fields
            if (empty($entry['item_meta'])) {
                error_log('No field data found in standard formats, trying to extract from form fields');

                // Get the form fields to know what to look for
                if (class_exists('FrmField')) {
                    $fields = FrmField::get_all_for_form($form_id);

                    foreach ($fields as $field) {
                        $field_id = $field->id;
                        $field_key = $field->field_key;

                        // Check if the field exists in the form data by ID or key
                        if (isset($form_data[$field_id])) {
                            $entry['item_meta'][$field_id] = sanitize_text_field($form_data[$field_id]);
                        } else if (isset($form_data[$field_key])) {
                            $entry['item_meta'][$field_id] = sanitize_text_field($form_data[$field_key]);
                        }
                    }

                    error_log('Extracted item_meta from form fields: ' . print_r($entry['item_meta'], true));
                }
            }

            // Log the prepared entry
            error_log('Prepared entry: ' . print_r($entry, true));

            // If we still have no field data, try to use the raw POST data directly
            if (empty($entry['item_meta'])) {
                error_log('No field data found in standard formats, trying to use raw POST data');

                // Look for item_meta fields directly in the POST data
                foreach ($_POST as $key => $value) {
                    if (strpos($key, 'item_meta') === 0) {
                        // Extract field ID using regex
                        preg_match('/item_meta\[(\d+)\]/', $key, $matches);
                        if (isset($matches[1]) && $matches[1] !== '0') {
                            $field_id = $matches[1];
                            $entry['item_meta'][$field_id] = sanitize_text_field($value);
                            error_log("Added field $field_id from raw POST data: $value");
                        }
                    }
                }

                // If we still have no field data, error out
                if (empty($entry['item_meta'])) {
                    error_log('No field data could be extracted from any source');
                    wp_send_json_error('No form field data could be extracted');
                    return;
                }
            }

            // Force the entry to be created even if there are validation errors
            add_filter('frm_continue_to_create', '__return_true', 99);

            // Create entry using Formidable Forms API
            $entry_id = FrmEntry::create($entry);

            if (is_wp_error($entry_id)) {
                error_log('Error creating entry: ' . $entry_id->get_error_message());
                wp_send_json_error($entry_id->get_error_message());
                return;
            }

            // Log success
            error_log('Form submitted successfully. Entry ID: ' . $entry_id);

            // Get form settings to check for success message
            $form = FrmForm::getOne($form_id);
            $success_message = '';

            if ($form && isset($form->options['success_msg'])) {
                $success_message = $form->options['success_msg'];
            }

            wp_send_json_success(array(
                'entry_id' => $entry_id,
                'message' => $success_message ?: 'Form submitted successfully!'
            ));

        } catch (Exception $e) {
            error_log('Exception in form submission: ' . $e->getMessage());
            wp_send_json_error('Error processing form: ' . $e->getMessage());
        }
    }
}

// Initialize the class
QKB_Formidable_Forms_Integration::get_instance();
