<?php
if (!defined('ABSPATH')) {
    exit;
}

class QKB_OpenAI_Prompts extends QKB_OpenAI_Base {
    private $assistant_prompts = [];
    private $system_instructions;
    // Remove the follow_up_templates property

    public function __construct() {
        parent::__construct();
        $this->load_assistant_prompts();
        $this->system_instructions = $this->get_system_instructions();
    }

    private function load_assistant_prompts() {
        $assistants = get_terms([
            'taxonomy' => 'kb_assistant',
            'hide_empty' => false
        ]);

        foreach ($assistants as $assistant) {
            // Handle both object and array formats
            $term_id = is_object($assistant) ? $assistant->term_id : (isset($assistant['term_id']) ? $assistant['term_id'] : 0);

            if (!$term_id) {
                continue; // Skip if we can't get a valid term_id
            }

            $prompt = get_term_meta($term_id, 'assistant_prompt', true);
            if (empty($prompt)) {
                $prompt = $this->get_default_prompt($assistant);
            }
            $this->assistant_prompts[$term_id] = $prompt;
        }
    }

    public function get_system_instructions($assistant_id = null) {
        // Get custom system prompt from settings or use default
        $system_prompt = get_option('qkb_global_system_prompt', $this->get_default_system_prompt());

        // Get response format and tone settings
        $response_format = get_option('qkb_response_format', 'markdown');
        $citation_style = get_option('qkb_citation_style', 'inline');
        $response_tone = get_option('qkb_response_tone', 'balanced');

        // Add format-specific instructions
        $format_instructions = "\n\nResponse Format Settings:\n";
        $format_instructions .= "- Format: " . ucfirst($response_format) . "\n";
        $format_instructions .= "- Citations: " . ucfirst($citation_style) . "\n";
        $format_instructions .= "- Tone: " . ucfirst($response_tone) . "\n";

        // Append format instructions to system prompt
        $system_prompt .= $format_instructions;

        if (!$assistant_id && isset($_REQUEST['assistant_id'])) {
            $assistant_id = intval($_REQUEST['assistant_id']);
        }

        if ($assistant_id) {
            $assistant = get_term($assistant_id, 'kb_assistant');
            if (!is_wp_error($assistant) && $assistant) {
                $assistant_prompt = $this->assistant_prompts[$assistant_id] ?? '';
                $assistant_categories = get_term_meta($assistant_id, 'assistant_categories', true);
                $assistant_expertise = get_term_meta($assistant_id, 'assistant_expertise', true);

                // Handle both object and array formats for assistant
                $assistant_name = is_object($assistant) ? $assistant->name : (isset($assistant['name']) ? $assistant['name'] : 'AI Assistant');

                return [
                    'role' => 'system',
                    'content' => $system_prompt . "\n\n" .
                                "Assistant Profile:\n" .
                                "Role: {$assistant_name}\n" .
                                "Categories: " . ($assistant_categories ? (is_array($assistant_categories) ? implode(', ', $assistant_categories) : $assistant_categories) : 'General') . "\n" .
                                "Expertise: " . ($assistant_expertise ?: 'General knowledge') . "\n" .
                                (!empty($assistant_prompt) ? "\nSpecific Instructions:\n" . $assistant_prompt : '')
                ];
            }
        }

        return [
            'role' => 'system',
            'content' => $system_prompt
        ];
    }

    // Removed get_follow_up_prompt and format_response methods

    private function get_default_prompt($assistant) {
        // Handle both object and array formats
        $term_id = is_object($assistant) ? $assistant->term_id : (isset($assistant['term_id']) ? $assistant['term_id'] : 0);
        $name = is_object($assistant) ? $assistant->name : (isset($assistant['name']) ? $assistant['name'] : 'AI Assistant');
        $description = is_object($assistant) ? $assistant->description : (isset($assistant['description']) ? $assistant['description'] : '');

        if (empty($description)) {
            $description = "You are a helpful AI assistant.";
        }

        $expertise = $term_id ? get_term_meta($term_id, 'assistant_expertise', true) : '';
        $expertise_str = empty($expertise) ? "" : "\n\nCore Expertise Areas:\n" . $this->format_expertise_areas($expertise);

        $categories = $term_id ? get_term_meta($term_id, 'assistant_categories', true) : '';
        $categories_str = empty($categories) ? "" : "\n\nSpecialized Categories:\n" . $this->format_categories($categories);

        return "Role: {$name}\n\n{$description}\n\n" .
               "Key Responsibilities:\n" .
               "1. Provide accurate information from assigned knowledge base content\n" .
               "2. Maintain expertise-specific context and terminology\n" .
               "3. Generate clear, structured responses\n" .
               "4. Guide users to relevant resources\n" .
               "5. Acknowledge knowledge boundaries professionally" .
               $expertise_str .
               $categories_str;
    }

    private function format_expertise_areas($expertise) {
        if (empty($expertise)) {
            return '';
        }
        $areas = is_array($expertise) ? $expertise : explode(',', $expertise);
        return implode("\n", array_map(function($area) {
            return "- " . trim($area);
        }, $areas));
    }

    private function format_categories($categories) {
        if (empty($categories)) {
            return '';
        }
        $categories_array = is_array($categories) ? $categories : explode(',', $categories);
        return implode("\n", array_map(function($category) {
            return "- " . trim($category);
        }, $categories_array));
    }

    public function get_default_system_prompt() {
        return "You are Q, an intelligent AI assistant developed by Q-Ai specializing in knowledge base content delivery. You must strictly adhere to the following principles and response format:\n\n" .
               "CRITICAL INSTRUCTION: You are ONLY allowed to provide information from the knowledge base content that has been specifically assigned to your assistant ID. DO NOT provide information from any other sources or any content not explicitly assigned to your assistant ID. If asked about content not assigned to you, state that you don't have access to that information.\n\n" .
               "IMPORTANT: Each assistant has access ONLY to its own assigned content. You MUST NOT answer questions about content assigned to other assistants. If a user asks about content that is not assigned to you, respond with: 'I don't have access to that information as it's not assigned to me.'\n\n" .
               "STRICT CONTENT BOUNDARY: The context provided to you has been filtered to only include content assigned to your assistant ID. You must NEVER attempt to answer questions beyond this context, even if you think you know the answer. If the information is not in your provided context, it means it's not assigned to you.\n\n" .
               "Core Principles:\n" .
               "1. Only provide information from your specifically assigned knowledge base content. If asked about content not assigned to you, state that you don't have access to that information.\n" .
               "2. Present information in clear, well-structured formats\n" .
               "3. Focus responses on the specific query context\n" .
               "4. Maintain a helpful, professional tone\n" .
               "5. Always acknowledge when information is outside your assigned knowledge scope\n\n" .
               "Response Format:\n" .
               "1. Begin with a concise, direct answer to the query\n" .
               "2. Provide relevant details and context from the knowledge base\n" .
               "4. Clearly state if the query cannot be fully answered with available knowledge";
    }
}
