<?php
/**
 * Admin page to fix feedback table structure
 * 
 * This file provides an admin interface to fix the feedback table structure
 * when the error "Feedback table structure is invalid" appears.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Create admin page
function qkb_add_fix_feedback_table_page() {
    add_submenu_page(
        'edit.php?post_type=kb_knowledge_base',
        'Fix Feedback Table',
        'Fix Feedback Table',
        'manage_options',
        'qkb-fix-feedback-table',
        'qkb_render_fix_feedback_table_page'
    );
}
add_action('admin_menu', 'qkb_add_fix_feedback_table_page');

// Render admin page
function qkb_render_fix_feedback_table_page() {
    // Check if user has permission
    if (!current_user_can('manage_options')) {
        wp_die('You do not have sufficient permissions to access this page.');
    }

    // Check if form was submitted
    $message = '';
    if (isset($_POST['qkb_fix_feedback_table']) && check_admin_referer('qkb_fix_feedback_table_nonce')) {
        // Get ML Handler instance
        $ml_handler = QKB_ML_Handler::get_instance();
        
        // Force recreation of tables
        if ($ml_handler->recreate_tables()) {
            $message = '<div class="notice notice-success"><p>Success! The feedback table has been fixed. You can now access the <a href="' . 
                admin_url('edit.php?post_type=kb_knowledge_base&page=qkb-feedback-analytics') . 
                '">Feedback Analytics</a> page.</p></div>';
        } else {
            $message = '<div class="notice notice-error"><p>Error: Failed to fix the feedback table. Please check the error logs for more information.</p></div>';
        }
    }

    // Render page
    ?>
    <div class="wrap">
        <h1>Fix Feedback Table</h1>
        
        <?php echo $message; ?>
        
        <div class="card">
            <h2>Feedback Table Repair Tool</h2>
            <p>If you're seeing the error <strong>"Feedback table structure is invalid"</strong> on the Feedback Analytics page, 
            use this tool to fix the database table structure.</p>
            
            <form method="post" action="">
                <?php wp_nonce_field('qkb_fix_feedback_table_nonce'); ?>
                <p>This will recreate the feedback table with the correct structure. Any existing feedback data will be lost.</p>
                <p><input type="submit" name="qkb_fix_feedback_table" class="button button-primary" value="Fix Feedback Table"></p>
            </form>
        </div>
    </div>
    <?php
}

// Add notice if feedback table is invalid
function qkb_check_feedback_table_notice() {
    // Only show on admin pages
    if (!is_admin()) {
        return;
    }
    
    // Get ML Handler instance
    $ml_handler = QKB_ML_Handler::get_instance();
    
    // Check if table structure is valid
    if (!$ml_handler->verify_table_structure()) {
        ?>
        <div class="notice notice-error">
            <p>Q Knowledge Base: The feedback table structure is invalid. Please <a href="<?php echo admin_url('edit.php?post_type=kb_knowledge_base&page=qkb-fix-feedback-table'); ?>">fix the feedback table</a> to use the Feedback Analytics feature.</p>
        </div>
        <?php
    }
}
add_action('admin_notices', 'qkb_check_feedback_table_notice');
