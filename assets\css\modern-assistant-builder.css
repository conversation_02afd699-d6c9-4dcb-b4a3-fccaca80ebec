/**
 * Modern Assistant Builder CSS
 *
 * Enhances the Q Knowledge Base assistant builder with a modern, clean design
 */

/* Import fonts if not already imported */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Main container styling */
.wrap.qkb-assistant-builder-wrap {
    max-width: 1200px;
    margin: 20px auto;
    font-family: var(--qkb-font, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
}

.wrap.qkb-assistant-builder-wrap h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 25px;
    color: var(--qkb-text-dark, #1f2328);
    padding-bottom: 15px;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
}

/* Container */
.qkb-assistant-builder-container {
    display: flex;
    margin-top: 20px;
    background: #fff;
    border-radius: var(--qkb-radius-md, 15px);
    box-shadow: var(--qkb-shadow-sm, 0 5px 10px rgba(0, 0, 0, 0.1));
    overflow: hidden;
}

/* Sidebar */
.qkb-assistant-sidebar {
    width: 250px;
    border-right: 1px solid var(--qkb-border, #e1e4e8);
    background: #f9f9f9;
}

.qkb-assistant-list-header {
    padding: 15px;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f1f5f9;
}

.qkb-assistant-list-header h2 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--qkb-text-dark, #1f2328);
}

.qkb-assistant-list {
    padding: 10px 0;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

.qkb-assistants {
    margin: 0;
    padding: 0;
    list-style: none;
}

.qkb-assistant-item {
    padding: 12px 15px;
    cursor: pointer;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
    margin-bottom: 2px;
    border-radius: 0 4px 4px 0;
}

.qkb-assistant-item:hover {
    background: #f1f1f1;
    color: var(--qkb-primary, #2271b1);
}

.qkb-assistant-item.active {
    background: rgba(34, 113, 177, 0.08);
    border-left-color: var(--qkb-primary, #2271b1);
    font-weight: 600;
    color: var(--qkb-primary, #2271b1);
}

.qkb-loading,
.qkb-no-items,
.qkb-error {
    padding: 10px 15px;
    color: #666;
    font-style: italic;
}

.qkb-error {
    color: #d63638;
}

/* Editor */
.qkb-assistant-editor {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
}

.qkb-assistant-editor-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8fafc;
}

.qkb-assistant-editor-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--qkb-text-dark, #1f2328);
}

.qkb-assistant-actions {
    display: flex;
    gap: 10px;
}

.qkb-assistant-editor-content {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

/* Tabs */
.qkb-assistant-tabs {
    margin-top: 20px;
}

.qkb-tab-nav {
    display: flex;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
    margin-bottom: 20px;
    background: #fff;
    position: sticky;
    top: 0;
    z-index: 10;
}

.qkb-tab-button {
    padding: 12px 18px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 600;
    color: var(--qkb-text-light, #646970);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.qkb-tab-button:hover {
    color: var(--qkb-primary, #2271b1);
    background-color: rgba(34, 113, 177, 0.04);
}

.qkb-tab-button.active {
    color: var(--qkb-primary, #2271b1);
    border-bottom-color: var(--qkb-primary, #2271b1);
    background-color: rgba(34, 113, 177, 0.04);
}

.qkb-tab-pane {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.qkb-tab-pane.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form */
.qkb-form-row {
    margin-bottom: 20px;
}

.qkb-form-row label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--qkb-text-dark, #1f2328);
}

.qkb-form-row input[type="text"],
.qkb-form-row textarea,
.qkb-form-row select {
    width: 100%;
    max-width: 100%;
    border: 1px solid var(--qkb-border, #e1e4e8);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.qkb-form-row input[type="text"]:focus,
.qkb-form-row textarea:focus,
.qkb-form-row select:focus {
    border-color: var(--qkb-primary, #2271b1);
    box-shadow: 0 0 0 1px var(--qkb-primary-light, #2271b1ad);
    outline: none;
}

.qkb-form-row textarea {
    min-height: 120px;
}

.qkb-form-row .description {
    color: var(--qkb-text-light, #646970);
    font-style: italic;
    margin-top: 5px;
    font-size: 13px;
}

/* Knowledge */
.qkb-knowledge-selector {
    border: 1px solid var(--qkb-border, #e1e4e8);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.qkb-knowledge-search {
    padding: 12px;
    background: #f9f9f9;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
}

.qkb-knowledge-search input {
    width: 100%;
    border: 1px solid var(--qkb-border, #e1e4e8);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
}

.qkb-knowledge-list {
    max-height: 300px;
    overflow-y: auto;
    padding: 12px;
}

.qkb-knowledge-item {
    padding: 10px;
    border-bottom: 1px solid #f0f0f0;
    transition: background 0.2s ease;
    border-radius: 4px;
}

.qkb-knowledge-item:hover {
    background: #f8f9fa;
}

.qkb-knowledge-item:last-child {
    border-bottom: none;
}

.qkb-knowledge-item label {
    display: flex;
    align-items: center;
    font-weight: normal;
    margin-bottom: 4px;
}

.qkb-knowledge-item input[type="checkbox"] {
    margin-right: 8px;
}

.qkb-knowledge-type {
    font-size: 12px;
    color: #666;
    margin: 0 0 0 24px;
}

/* Suggested prompts styling */
.qkb-suggested-prompts-list {
    margin-top: 15px;
}

.qkb-suggested-prompt-item {
    margin-bottom: 10px;
}

.qkb-suggested-prompt-input {
    display: flex;
    gap: 10px;
    align-items: center;
}

.qkb-suggested-prompt-input input {
    flex: 1;
    border: 1px solid var(--qkb-border, #e1e4e8);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
}

.qkb-suggested-prompt-input input:focus {
    border-color: var(--qkb-primary, #2271b1);
    box-shadow: 0 0 0 1px var(--qkb-primary-light, #2271b1ad);
    outline: none;
}

.qkb-remove-suggested-prompt {
    padding: 0 !important;
    width: 30px;
    height: 30px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    border-radius: 50% !important;
    color: #666;
    background: #f1f1f1 !important;
    border: 1px solid #ddd !important;
    transition: all 0.2s ease;
}

.qkb-remove-suggested-prompt:hover {
    background: #f8d7da !important;
    color: #842029;
    border-color: #f5c2c7 !important;
}

.qkb-remove-suggested-prompt .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.qkb-add-suggested-prompt {
    margin-top: 10px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    background: #f8f9fa !important;
    border-color: #ddd !important;
    color: #333 !important;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.qkb-add-suggested-prompt:hover {
    background: #f1f1f1 !important;
    border-color: #ccc !important;
}

.qkb-add-suggested-prompt:before {
    content: "+";
    font-size: 16px;
    font-weight: bold;
    margin-right: 5px;
}

/* Button styling */
.qkb-add-assistant,
.qkb-save-assistant,
.qkb-delete-assistant {
    border-radius: 4px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    gap: 5px !important;
}

.qkb-add-assistant:hover,
.qkb-save-assistant:hover {
    transform: translateY(-1px);
}

.qkb-add-assistant .dashicons,
.qkb-save-assistant .dashicons,
.qkb-delete-assistant .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qkb-delete-assistant {
    color: #666 !important;
    border-color: #ddd !important;
    background: #f8f9fa !important;
}

.qkb-delete-assistant:hover {
    color: #d63638 !important;
    border-color: #f5c2c7 !important;
    background: #f8d7da !important;
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
    .qkb-assistant-builder-container {
        flex-direction: column;
    }

    .qkb-assistant-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--qkb-border, #e1e4e8);
    }

    .qkb-tab-button {
        padding: 10px 15px;
        font-size: 13px;
    }
}
