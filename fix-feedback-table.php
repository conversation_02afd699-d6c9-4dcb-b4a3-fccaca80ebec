<?php
// Fix Feedback Table Script
// Place this file in the plugin root directory and run it once to fix the feedback table

// Load WordPress
require_once dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';

// Ensure user is an admin
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

// Get the ML Handler instance
require_once __DIR__ . '/includes/class-qkb-ml-handler.php';
$ml_handler = QKB_ML_Handler::get_instance();

// Force recreation of tables
echo '<h1>Q Knowledge Base - Fix Feedback Table</h1>';

// Check if table exists and its structure
echo '<p>Checking current table structure...</p>';
if (!$ml_handler->verify_table_structure()) {
    echo '<p>Table structure is invalid. Attempting to recreate tables...</p>';
    
    // Recreate tables
    if ($ml_handler->recreate_tables()) {
        echo '<p style="color: green; font-weight: bold;">Success! Tables have been recreated successfully.</p>';
        echo '<p>You can now go back to the <a href="' . admin_url('edit.php?post_type=kb_knowledge_base&page=qkb-feedback-analytics') . '">Feedback Analytics</a> page.</p>';
    } else {
        echo '<p style="color: red; font-weight: bold;">Error: Failed to recreate tables. Please check the error logs for more information.</p>';
    }
} else {
    echo '<p style="color: green; font-weight: bold;">Table structure is valid. No action needed.</p>';
    echo '<p>You can now go back to the <a href="' . admin_url('edit.php?post_type=kb_knowledge_base&page=qkb-feedback-analytics') . '">Feedback Analytics</a> page.</p>';
}
