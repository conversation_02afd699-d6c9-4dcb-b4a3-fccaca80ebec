/**
 * Prompt Tester CSS
 */

.qkb-prompt-tester-container {
    display: flex;
    margin-top: 20px;
    gap: 20px;
}

.qkb-prompt-tester-sidebar {
    flex: 0 0 350px;
    max-width: 350px;
}

.qkb-prompt-tester-main {
    flex: 1;
}

.qkb-form-group {
    margin-bottom: 15px;
}

.qkb-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.qkb-form-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

.qkb-token-estimation {
    margin-top: 30px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
}

.qkb-token-stats {
    margin-top: 10px;
}

.qkb-token-stat {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.qkb-token-total {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #ddd;
    font-weight: 600;
}

.qkb-token-warning {
    margin-top: 10px;
    padding: 8px;
    background: #fcf8e3;
    border: 1px solid #faebcc;
    border-radius: 3px;
    color: #8a6d3b;
}

.qkb-prompt-preview-container,
.qkb-response-container {
    margin-bottom: 30px;
}

.qkb-prompt-section {
    margin-bottom: 20px;
}

.qkb-prompt-section h3 {
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #ddd;
}

.qkb-prompt-content,
.qkb-response-output {
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
    min-height: 150px;
}

.qkb-response-placeholder {
    padding: 15px;
    color: #999;
    font-style: italic;
}

/* CodeMirror customizations */
.CodeMirror {
    height: auto;
    min-height: 150px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.CodeMirror-scroll {
    min-height: 150px;
}


