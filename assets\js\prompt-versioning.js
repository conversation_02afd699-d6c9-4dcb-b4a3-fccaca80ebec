/**
 * Prompt Versioning JavaScript
 */
(function($) {
    'use strict';

    // Current version data
    let currentVersionData = null;

    // Initialize
    $(document).ready(function() {
        // Load initial data
        loadVersionsData();

        // Set up event listeners
        $('#qkb-refresh-versions').on('click', loadVersionsData);
        $('#qkb-prompt-type-filter, #qkb-prompt-id-filter').on('change', loadVersionsData);
        
        // Version details actions
        $('#qkb-close-details').on('click', closeVersionDetails);
        $('#qkb-restore-version').on('click', restoreVersion);
        $('#qkb-compare-versions').on('click', compareVersions);
        $('#qkb-close-comparison').on('click', closeVersionComparison);
        
        // Delegate click for version view buttons
        $('#qkb-versions-table').on('click', '.qkb-view-version', function(e) {
            e.preventDefault();
            const versionId = $(this).data('version-id');
            viewVersion(versionId);
        });
    });

    /**
     * Load versions data via AJAX
     */
    function loadVersionsData() {
        const promptType = $('#qkb-prompt-type-filter').val();
        const promptId = $('#qkb-prompt-id-filter').val();
        
        // Show loading state
        $('#qkb-refresh-versions').addClass('loading');
        $('#qkb-refresh-versions i').addClass('spin');
        $('#qkb-versions-table').html('<tr><td colspan="6">Loading data...</td></tr>');
        
        // Make AJAX request
        $.ajax({
            url: qkbPromptVersions.ajaxUrl,
            type: 'POST',
            data: {
                action: 'qkb_get_prompt_versions',
                nonce: qkbPromptVersions.nonce,
                prompt_type: promptType,
                prompt_id: promptId
            },
            success: function(response) {
                if (response.success) {
                    updateVersionsTable(response.data.versions);
                } else {
                    alert(response.data.message || qkbPromptVersions.errorMessage);
                }
            },
            error: function() {
                alert(qkbPromptVersions.errorMessage);
            },
            complete: function() {
                // Reset loading state
                $('#qkb-refresh-versions').removeClass('loading');
                $('#qkb-refresh-versions i').removeClass('spin');
            }
        });
    }

    /**
     * Update versions table with data
     * 
     * @param {Array} versions Versions data
     */
    function updateVersionsTable(versions) {
        let tableHtml = '';
        
        if (versions.length === 0) {
            tableHtml = '<tr><td colspan="6">No versions found</td></tr>';
        } else {
            versions.forEach(function(version) {
                tableHtml += `
                    <tr>
                        <td>${version.version}</td>
                        <td>${version.prompt_name}</td>
                        <td>${formatPromptType(version.prompt_type)}</td>
                        <td>${version.created_by_name}</td>
                        <td>${formatDate(version.created_at)}</td>
                        <td>
                            <button class="button qkb-view-version" data-version-id="${version.id}">
                                <i class="dashicons dashicons-visibility"></i> View
                            </button>
                        </td>
                    </tr>
                `;
            });
        }
        
        $('#qkb-versions-table').html(tableHtml);
    }

    /**
     * View a specific version
     * 
     * @param {number} versionId Version ID
     */
    function viewVersion(versionId) {
        // Find version data
        const versions = $('#qkb-versions-table').data('versions');
        const version = versions.find(v => v.id == versionId);
        
        if (!version) {
            alert('Version not found');
            return;
        }
        
        // Store current version data
        currentVersionData = version;
        
        // Update version details
        $('.qkb-version-title').text(`${version.prompt_name} - Version ${version.version}`);
        $('.qkb-version-created-by').text(`Created by: ${version.created_by_name}`);
        $('.qkb-version-date').text(`Date: ${formatDate(version.created_at)}`);
        $('.qkb-version-comment').text(version.comment || 'No comment');
        $('#qkb-version-content-display').text(version.content);
        
        // Show version details
        $('.qkb-version-details').show();
        $('.qkb-version-comparison').hide();
    }

    /**
     * Close version details
     */
    function closeVersionDetails() {
        $('.qkb-version-details').hide();
        currentVersionData = null;
    }

    /**
     * Restore a version
     */
    function restoreVersion() {
        if (!currentVersionData) {
            return;
        }
        
        if (!confirm(qkbPromptVersions.confirmRestore)) {
            return;
        }
        
        // Show loading state
        $('#qkb-restore-version').prop('disabled', true).addClass('loading');
        
        // Make AJAX request
        $.ajax({
            url: qkbPromptVersions.ajaxUrl,
            type: 'POST',
            data: {
                action: 'qkb_restore_prompt_version',
                nonce: qkbPromptVersions.nonce,
                version_id: currentVersionData.id
            },
            success: function(response) {
                if (response.success) {
                    alert(qkbPromptVersions.restoreSuccess);
                    loadVersionsData();
                    closeVersionDetails();
                } else {
                    alert(response.data.message || qkbPromptVersions.restoreError);
                }
            },
            error: function() {
                alert(qkbPromptVersions.restoreError);
            },
            complete: function() {
                // Reset loading state
                $('#qkb-restore-version').prop('disabled', false).removeClass('loading');
            }
        });
    }

    /**
     * Compare versions
     */
    function compareVersions() {
        if (!currentVersionData) {
            return;
        }
        
        // Show loading state
        $('#qkb-compare-versions').prop('disabled', true).addClass('loading');
        
        // Make AJAX request
        $.ajax({
            url: qkbPromptVersions.ajaxUrl,
            type: 'POST',
            data: {
                action: 'qkb_compare_prompt_versions',
                nonce: qkbPromptVersions.nonce,
                version_id: currentVersionData.id
            },
            success: function(response) {
                if (response.success) {
                    showVersionComparison(response.data.old_content, response.data.new_content);
                } else {
                    alert(response.data.message || qkbPromptVersions.errorMessage);
                }
            },
            error: function() {
                alert(qkbPromptVersions.errorMessage);
            },
            complete: function() {
                // Reset loading state
                $('#qkb-compare-versions').prop('disabled', false).removeClass('loading');
            }
        });
    }

    /**
     * Show version comparison
     * 
     * @param {string} oldContent Old content
     * @param {string} newContent New content
     */
    function showVersionComparison(oldContent, newContent) {
        // Update comparison content
        $('#qkb-comparison-old-content').text(oldContent);
        $('#qkb-comparison-new-content').text(newContent);
        
        // Generate diff
        const dmp = new diff_match_patch();
        const diffs = dmp.diff_main(oldContent, newContent);
        dmp.diff_cleanupSemantic(diffs);
        
        // Format diff
        let diffHtml = '';
        diffs.forEach(function(diff) {
            const [type, text] = diff;
            const formattedText = text.replace(/\n/g, '<br>');
            
            if (type === -1) {
                // Deletion
                diffHtml += `<span class="qkb-diff-delete">${formattedText}</span>`;
            } else if (type === 1) {
                // Addition
                diffHtml += `<span class="qkb-diff-add">${formattedText}</span>`;
            } else {
                // Unchanged
                diffHtml += `<span class="qkb-diff-unchanged">${formattedText}</span>`;
            }
        });
        
        $('#qkb-comparison-diff-content').html(diffHtml);
        
        // Show comparison
        $('.qkb-version-details').hide();
        $('.qkb-version-comparison').show();
    }

    /**
     * Close version comparison
     */
    function closeVersionComparison() {
        $('.qkb-version-comparison').hide();
        $('.qkb-version-details').show();
    }

    /**
     * Format prompt type for display
     * 
     * @param {string} type Prompt type
     * @return {string} Formatted type
     */
    function formatPromptType(type) {
        switch (type) {
            case 'system':
                return 'System';
            case 'assistant':
                return 'Assistant';
            case 'template':
                return 'Template';
            default:
                return type.charAt(0).toUpperCase() + type.slice(1);
        }
    }

    /**
     * Format date for display
     * 
     * @param {string} dateString Date string
     * @return {string} Formatted date
     */
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString();
    }

})(jQuery);
