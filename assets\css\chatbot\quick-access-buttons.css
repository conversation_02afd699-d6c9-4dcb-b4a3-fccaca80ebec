/**
 * Quick Access Buttons CSS
 */

.qkb-quick-access-container {
  position: relative;
  width: 100%;
}

.qkb-quick-access-toggle {
  display: flex;
  justify-content: center;
}

.qkb-quick-access-toggle-button {
  display: flex;
  align-items: center;
  gap: 5px;
  background: var(--qkb-primary);
  border-radius: var(--qkb-radius-xs);
  padding: var(--qkb-padding-sm);
  font-size: var(--qkb-font-size-xs);
  font-weight: bold;
  color: var(--qkb-bg);
  cursor: pointer;
  transition: all 0.2s ease;
  -webkit-border-radius: var(--qkb-radius-xs);
  -moz-border-radius: var(--qkb-radius-xs);
  -ms-border-radius: var(--qkb-radius-xs);
  -o-border-radius: var(--qkb-radius-xs);
}

.qkb-quick-access-toggle-button:hover {
  background: transparent;
  color: var(--qkb-text);
}

.qkb-quick-access-toggle-button i {
  transition: var(--qkb-transform);
  -webkit-transition: var(--qkb-transform);
  -moz-transition: var(--qkb-transform);
  -ms-transition: var(--qkb-transform);
  -o-transition: var(--qkb-transform);
}

.qkb-quick-access-toggle-button[aria-expanded="true"] i {
  transform: rotate(180deg);
}

.qkb-quick-access-buttons-wrapper {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.qkb-quick-access-buttons-wrapper.visible {
  max-height: 150px;
  overflow-x: auto;
}

.qkb-quick-access-buttons {
  display: flex;
  gap: var(--qkb-gap);
  padding: 10px 10px;
  overflow-x: auto;
  scrollbar-width: thin;
  -ms-overflow-style: none;
}

.qkb-quick-access-buttons::-webkit-scrollbar {
  height: 4px;
}

.qkb-quick-access-buttons::-webkit-scrollbar-track {
  background: var(--qkb-bg-light);
}

.qkb-quick-access-buttons::-webkit-scrollbar-thumb {
  background: var(--qkb-primary);
  border-radius: 4px;
}

.qkb-quick-access-button {
  display: flex;
  align-items: center;
  gap: var(--qkb-gap);
  border: 1px solid var(--qkb-border);
  border-radius: var(--qkb-radius-xs);
  padding: var(--qkb-padding-sm);
  font-size: var(--qkb-font-size-xs);
  color: var(--qkb-text);
  white-space: nowrap;
  cursor: pointer;
  transition: var(--qkb-transition);
  flex-shrink: 0;
  -webkit-border-radius: var(--qkb-radius-xs);
  -moz-border-radius: var(--qkb-radius-xs);
  -ms-border-radius: var(--qkb-radius-xs);
  -o-border-radius: var(--qkb-radius-xs);
  -webkit-transition: var(--qkb-transition);
  -moz-transition: var(--qkb-transition);
  -ms-transition: var(--qkb-transition);
  -o-transition: var(--qkb-transition);
}

.qkb-quick-access-button:hover {
  background: var(--qkb-primary-light);
  border-color: var(--qkb-primary);
  color: var(--qkb-primary);
}

.qkb-quick-access-button i {
  font-size: var(--qkb-font-size-xs);
}

.qkb-hidden-content {
  display: none !important;
  position: absolute;
  visibility: hidden;
  width: 0;
  height: 0;
  overflow: hidden;
}

.qkb-quick-action-trigger-wrapper {
  display: inline-block;
  background: var(--qkb-border-light);
  margin: 0 auto;
}

.qkb-quick-action-trigger-button:hover {
  background: var(--qkb-primary-light);
  border-color: var(--qkb-primary);
  color: var(--qkb-primary);
}

.qkb-quick-action-trigger-button i {
  font-size: var(--qkb-font-size-xs);
}

/* Mobile styles */
@media screen and (max-width: 480px) {
  .qkb-quick-access-buttons-wrapper.visible {
    max-height: 120px;
  }

  .qkb-quick-access-button {
    padding: 4px 10px;
    font-size: 12px;
  }

  .qkb-quick-access-button i {
    font-size: 12px;
  }
}
