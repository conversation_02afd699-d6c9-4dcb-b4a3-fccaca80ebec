<?php
if (!defined('ABSPATH')) {
    exit;
}

class QKB_Error_Handler {
    private $log_file;
    
    public function __construct() {
        $upload_dir = wp_upload_dir();
        $this->log_file = $upload_dir['basedir'] . '/qkb-errors.log';
    }

    public function log_error($message, $exception = null) {
        $timestamp = current_time('mysql');
        $error_message = "[{$timestamp}] {$message}";
        
        if ($exception instanceof Exception) {
            $error_message .= "\nException: " . $exception->getMessage();
            $error_message .= "\nFile: " . $exception->getFile() . ":" . $exception->getLine();
            $error_message .= "\nTrace: " . $exception->getTraceAsString();
        }
        
        error_log($error_message);
        
        // Also log to custom file if writable
        if (is_writable(dirname($this->log_file))) {
            file_put_contents($this->log_file, $error_message . "\n", FILE_APPEND);
        }

        return false;
    }

    public function handle_wp_error($wp_error) {
        if (is_wp_error($wp_error)) {
            $this->log_error($wp_error->get_error_message());
            return false;
        }
        return true;
    }
}