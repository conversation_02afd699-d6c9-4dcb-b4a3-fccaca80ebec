<?php
if (!defined('ABSPATH')) {
    exit;
}

class QKB_OpenAI_Context extends QKB_OpenAI_Base {
    public function get_enriched_context($query, $assistant_id = null) {
        try {
            $context = [];

            // Convert assistant_id to proper options array
            $options = is_array($assistant_id) ? $assistant_id : ['assistant_id' => $assistant_id];

            // Get knowledge base context
            $kb_results = $this->get_kb_context($query, $options);
            if (!empty($kb_results)) {
                $context['kb_content'] = $kb_results;
            }

            // Get embedding-based similar content with assistant ID filter
            $similar_content = $this->get_similar_content($query, $options['assistant_id']);
            if (!empty($similar_content)) {
                $context['similar_content'] = $similar_content;
            }

            // Get assistant-specific context if available
            if ($assistant_id) {
                $assistant_context = $this->get_assistant_context($assistant_id);
                if (!empty($assistant_context)) {
                    $context['assistant_context'] = $assistant_context;
                }
            }

            // Add user context from session data
            $user_context = $this->get_user_context();
            if (!empty($user_context)) {
                $context['user_context'] = $user_context;
            }

            // Add conversation history context
            $conversation_context = $this->get_conversation_context();
            if (!empty($conversation_context)) {
                $context['conversation_history'] = $conversation_context;
            }

            return $this->format_context($context);

        } catch (Exception $e) {
            $this->error_handler->log_error('Context enrichment error', $e);
            return '';
        }
    }

    public function get_kb_context($query, $options = []) {
        $default_options = [
            'assistant_id' => null,
            'limit' => 5,
            'min_relevance' => 0.7,
            'include_sources' => false,
            'format_results' => false
        ];

        if (!is_array($options)) {
            $options = ['assistant_id' => $options];
        }

        $options = array_merge($default_options, $options);

        try {
            $search_params = [
                'max_results' => intval($options['limit']),
                'categories' => [],
                'content_types' => ['kb_knowledge_base', 'kb_external_content'],
                'assistant_id' => $options['assistant_id'],
                'min_relevance' => floatval($options['min_relevance']),
                'fallback_to_all' => false // Never fallback to all content
            ];

            error_log('Searching knowledge base with assistant ID: ' . $options['assistant_id']);

            // Perform the search
            $results = $this->kb_handler->search($query, $search_params);

            if (empty($results)) {
                error_log('No knowledge base results found for query: ' . $query);
                return $options['format_results'] ? [
                    'context' => 'No relevant information found.',
                    'sources' => []
                ] : [];
            }

            // Ensure results are properly formatted
            if ($options['format_results']) {
                $formatted_content = '';
                if (isset($results['content'])) {
                    $formatted_content = is_array($results['content'])
                        ? implode("\n", array_map('strval', $results['content']))
                        : strval($results['content']);
                }

                return [
                    'context' => $formatted_content,
                    'sources' => isset($results['sources']) ? array_map(function($source) {
                        return [
                            'title' => $source['title'] ?? '',
                            'url' => $source['url'] ?? '',
                            'relevance' => $source['relevance'] ?? 100,
                            'type' => $source['type'] ?? 'article'
                        ];
                    }, $results['sources']) : [],
                ];
            }

            return $results;
        } catch (Exception $e) {
            $this->error_handler->log_error('KB context error', $e);
            return $options['format_results'] ? [
                'context' => 'Error retrieving knowledge base content.',
                'sources' => []
            ] : [];
        }
    }

    private function get_similar_content($query, $assistant_id = null) {
        if (!method_exists($this->kb_handler, 'get_similar_articles_by_embedding')) {
            return [];
        }

        return $this->kb_handler->get_similar_articles_by_embedding($query, 5, 0.7, $assistant_id);
    }

    private function get_assistant_context($assistant_id) {
        $assistant = get_term($assistant_id, 'kb_assistant');
        if (is_wp_error($assistant) || !$assistant) {
            return [];
        }

        // Handle both object and array formats for assistant
        $name = is_object($assistant) ? $assistant->name : (isset($assistant['name']) ? $assistant['name'] : 'AI Assistant');
        $description = is_object($assistant) ? $assistant->description : (isset($assistant['description']) ? $assistant['description'] : '');

        return [
            'name' => $name,
            'description' => $description,
            'expertise' => get_term_meta($assistant_id, 'assistant_expertise', true),
            'categories' => get_term_meta($assistant_id, 'assistant_categories', true)
        ];
    }

    private function format_context($context) {
        $formatted = '';

        if (!empty($context['kb_content'])) {
            $formatted .= "Knowledge Base Content:\n" . $this->format_kb_content($context['kb_content']) . "\n\n";
        }

        if (!empty($context['similar_content'])) {
            $formatted .= "Related Content:\n" . $this->format_similar_content($context['similar_content']) . "\n\n";
        }

        if (!empty($context['assistant_context'])) {
            $formatted .= "Assistant Context:\n" . $this->format_assistant_context($context['assistant_context']) . "\n\n";
        }

        if (!empty($context['user_context'])) {
            $formatted .= "User Context:\n" . $this->format_user_context($context['user_context']) . "\n\n";
        }

        if (!empty($context['conversation_history'])) {
            $formatted .= "Recent Conversation:\n" . $this->format_conversation_history($context['conversation_history']);
        }

        return trim($formatted);
    }

    private function format_kb_content($content) {
        if (empty($content)) {
            return 'No relevant knowledge base content found.';
        }

        if (!is_array($content)) {
            return strval($content);
        }

        $formatted = [];
        foreach ($content as $item) {
            if (is_string($item)) {
                $formatted[] = "- " . $item;
            } elseif (is_array($item)) {
                if (isset($item['title'], $item['content'])) {
                    $formatted[] = "- " . strval($item['title']) . ": " . strval($item['content']);
                } else {
                    // Safely convert array values to strings
                    $filtered = array_map(function($value) {
                        return is_array($value) ? json_encode($value) : strval($value);
                    }, array_filter($item));
                    $formatted[] = "- " . implode(': ', $filtered);
                }
            } else {
                // Handle non-string, non-array items
                $formatted[] = "- " . strval($item);
            }
        }

        return empty($formatted) ? 'No relevant knowledge base content found.' : implode("\n\n", $formatted);
    }

    private function format_similar_content($content) {
        if (!is_array($content)) {
            return strval($content);
        }

        return implode("\n", array_map(function($item) {
            if (is_string($item)) {
                return "- " . $item;
            }
            if (is_array($item) && isset($item['title']) && isset($item['summary'])) {
                return "- " . $item['title'] . ": " . $item['summary'];
            }
            return "- " . print_r($item, true);
        }, $content));
    }

    private function format_assistant_context($context) {
        $formatted = "Role: {$context['name']}\n";
        if (!empty($context['description'])) {
            $formatted .= "Description: {$context['description']}\n";
        }
        if (!empty($context['expertise'])) {
            $formatted .= "Expertise: {$context['expertise']}\n";
        }
        if (!empty($context['categories'])) {
            $formatted .= "Categories: " . implode(', ', $context['categories']);
        }
        return $formatted;
    }

    private function format_user_context($user_context) {
        $formatted = "User: {$user_context['username']}\n";

        if (!empty($user_context['roles'])) {
            $formatted .= "Roles: " . implode(', ', $user_context['roles']) . "\n";
        }

        $formatted .= "Interaction Count: {$user_context['interaction_count']}\n";

        if (!empty($user_context['preferred_topics'])) {
            $formatted .= "Preferred Topics: " . implode(', ', $user_context['preferred_topics']);
        }

        return $formatted;
    }

    private function format_conversation_history($history) {
        if (empty($history)) {
            return "No recent conversation history.";
        }

        $formatted = [];
        foreach ($history as $exchange) {
            $formatted[] = "User: " . $exchange['user_query'] . "\nAssistant: " . $exchange['assistant_response'];
        }

        return implode("\n\n", $formatted);
    }

    private function get_user_context() {
        $user = wp_get_current_user();
        if (!$user->exists()) {
            return [];
        }

        return [
            'user_id' => $user->ID,
            'username' => $user->user_login,
            'roles' => $user->roles,
            'interaction_count' => get_user_meta($user->ID, 'qkb_interaction_count', true) ?: 0,
            'last_visit' => get_user_meta($user->ID, 'qkb_last_visit', true),
            'preferred_topics' => $this->get_user_preferred_topics($user->ID)
        ];
    }

    private function get_user_preferred_topics($user_id) {
        $topics = get_user_meta($user_id, 'qkb_preferred_topics', true);
        if (empty($topics)) {
            // Try to infer from past interactions
            global $wpdb;
            $table = $wpdb->prefix . 'qkb_interactions';

            if ($wpdb->get_var("SHOW TABLES LIKE '$table'") == $table) {
                $query = $wpdb->prepare(
                    "SELECT keywords FROM $table WHERE user_id = %d ORDER BY timestamp DESC LIMIT 10",
                    $user_id
                );
                $results = $wpdb->get_col($query);

                if (!empty($results)) {
                    $keywords = [];
                    foreach ($results as $result) {
                        $row_keywords = maybe_unserialize($result);
                        if (is_array($row_keywords)) {
                            $keywords = array_merge($keywords, $row_keywords);
                        }
                    }

                    // Count keyword frequency
                    $keyword_counts = array_count_values($keywords);
                    arsort($keyword_counts);

                    // Return top 5 keywords
                    return array_slice(array_keys($keyword_counts), 0, 5);
                }
            }
        }

        return $topics ?: [];
    }

    private function get_conversation_context() {
        if (!isset($_SESSION['chat_history']) || empty($_SESSION['chat_history'])) {
            return [];
        }

        // Get last 3 exchanges
        $history = array_slice($_SESSION['chat_history'], -3);

        // Format for context
        return array_map(function($exchange) {
            return [
                'user_query' => $exchange['user'],
                'assistant_response' => $exchange['bot'],
                'timestamp' => $exchange['timestamp']
            ];
        }, $history);
    }
}
