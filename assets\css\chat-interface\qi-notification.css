/* Notification Styles */
.qi-notification {
    position: fixed;
    top: 80px;
    right: 20px;
    padding: var(--qkb-padding-sm);
    border-radius: var(--qkb-radius-xs);
    background: var(--qkb-bg);
    box-shadow: var(--qkb-shadow);
    display: flex;
    align-items: center;
    gap: var(--qkb-gap);
    z-index: var(--qkb-z-index-top);
    animation: slideIn 0.3s ease;
}

.qi-notification i {
    font-size: var(--qkb-font-size-lg);
}

.qi-notification-success {
    border-left: 4px solid var(--qkb-success);
}

.qi-notification-success i {
    color: var(--qkb-success);
}

.qi-notification-error {
    border-left: 4px solid var(--qkb-error);
}

.qi-notification-error i {
    color: var(--qkb-error);
}

.qi-error {
    color: var(--qkb-error);
    padding: var(--qkb-gap-lg);
    border-bottom: 3px solid var(--qkb-error);
    text-align: center;
    background: transparent;
    border-radius: var(--qkb-radius-sm);
    z-index: calc(var(--qkb-z-index-top) + 1);
}