<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * OpenAI Assistants API Integration
 *
 * Handles integration with OpenAI's Assistants API for creating, managing,
 * and interacting with assistants.
 */
class QKB_OpenAI_Assistants extends QKB_OpenAI_Base {
    /**
     * OpenAI assistant objects cache
     */
    private $assistants_cache = [];

    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct();

        // Add hooks for assistant management
        add_action('qkb_after_assistant_save', [$this, 'sync_assistant_with_openai'], 10, 2);
        add_action('qkb_before_assistant_delete', [$this, 'delete_assistant_from_openai'], 10, 1);

        // Add AJAX handlers
        add_action('wp_ajax_qkb_create_openai_assistant', [$this, 'ajax_create_assistant']);
        add_action('wp_ajax_qkb_update_openai_assistant', [$this, 'ajax_update_assistant']);
        add_action('wp_ajax_qkb_delete_openai_assistant', [$this, 'ajax_delete_assistant']);
        add_action('wp_ajax_qkb_get_openai_assistants', [$this, 'ajax_get_assistants']);
    }

    /**
     * Create a new assistant in OpenAI
     *
     * @param array $assistant_data Assistant data
     * @return string|WP_Error Assistant ID or error
     */
    public function create_assistant($assistant_data) {
        try {
            if (empty($this->api_key)) {
                return new WP_Error('missing_api_key', __('API key is not configured', 'q-knowledge-base'));
            }

            $this->rate_limiter->throttle();

            $request_data = [
                'name' => $assistant_data['name'],
                'description' => $assistant_data['description'] ?? '',
                'instructions' => $assistant_data['instructions'] ?? '',
                'model' => $assistant_data['model'] ?? $this->model,
            ];


            $response = $this->make_request('assistants', $request_data);

            if (is_wp_error($response)) {
                return $response;
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);

            if (isset($body['error'])) {
                return new WP_Error('openai_error', $body['error']['message']);
            }

            // Cache the assistant
            $this->assistants_cache[$body['id']] = $body;

            return $body['id'];
        } catch (Exception $e) {
            $this->error_handler->log_error('Assistant creation error', $e);
            return new WP_Error('assistant_creation_error', $e->getMessage());
        }
    }

    /**
     * Update an existing assistant in OpenAI
     *
     * @param string $assistant_id OpenAI Assistant ID
     * @param array $assistant_data Assistant data
     * @return bool|WP_Error True on success or error
     */
    public function update_assistant($assistant_id, $assistant_data) {
        try {
            if (empty($this->api_key)) {
                return new WP_Error('missing_api_key', __('API key is not configured', 'q-knowledge-base'));
            }

            $this->rate_limiter->throttle();

            $request_data = [];

            // Only include fields that are provided
            if (isset($assistant_data['name'])) {
                $request_data['name'] = $assistant_data['name'];
            }

            if (isset($assistant_data['description'])) {
                $request_data['description'] = $assistant_data['description'];
            }

            if (isset($assistant_data['instructions'])) {
                $request_data['instructions'] = $assistant_data['instructions'];
            }

            if (isset($assistant_data['model'])) {
                $request_data['model'] = $assistant_data['model'];
            }


            $response = $this->make_request("assistants/$assistant_id", $request_data, 30, 'PATCH');

            if (is_wp_error($response)) {
                return $response;
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);

            if (isset($body['error'])) {
                return new WP_Error('openai_error', $body['error']['message']);
            }

            // Update cache
            $this->assistants_cache[$assistant_id] = $body;

            return true;
        } catch (Exception $e) {
            $this->error_handler->log_error('Assistant update error', $e);
            return new WP_Error('assistant_update_error', $e->getMessage());
        }
    }

    /**
     * Delete an assistant from OpenAI
     *
     * @param string $assistant_id OpenAI Assistant ID
     * @return bool|WP_Error True on success or error
     */
    public function delete_assistant($assistant_id) {
        try {
            if (empty($this->api_key)) {
                return new WP_Error('missing_api_key', __('API key is not configured', 'q-knowledge-base'));
            }

            $this->rate_limiter->throttle();

            $response = $this->make_request("assistants/$assistant_id", [], 30, 'DELETE');

            if (is_wp_error($response)) {
                return $response;
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);

            if (isset($body['error'])) {
                return new WP_Error('openai_error', $body['error']['message']);
            }

            // Remove from cache
            unset($this->assistants_cache[$assistant_id]);

            return true;
        } catch (Exception $e) {
            $this->error_handler->log_error('Assistant deletion error', $e);
            return new WP_Error('assistant_deletion_error', $e->getMessage());
        }
    }

    /**
     * Get an assistant from OpenAI
     *
     * @param string $assistant_id OpenAI Assistant ID
     * @return array|WP_Error Assistant data or error
     */
    public function get_assistant($assistant_id) {
        try {
            // Return from cache if available
            if (isset($this->assistants_cache[$assistant_id])) {
                return $this->assistants_cache[$assistant_id];
            }

            if (empty($this->api_key)) {
                return new WP_Error('missing_api_key', __('API key is not configured', 'q-knowledge-base'));
            }

            $this->rate_limiter->throttle();

            $response = $this->make_request("assistants/$assistant_id", [], 30, 'GET');

            if (is_wp_error($response)) {
                return $response;
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);

            if (isset($body['error'])) {
                return new WP_Error('openai_error', $body['error']['message']);
            }

            // Cache the assistant
            $this->assistants_cache[$assistant_id] = $body;

            return $body;
        } catch (Exception $e) {
            $this->error_handler->log_error('Assistant retrieval error', $e);
            return new WP_Error('assistant_retrieval_error', $e->getMessage());
        }
    }

    /**
     * List all assistants from OpenAI
     *
     * @param array $params Optional parameters
     * @return array|WP_Error List of assistants or error
     */
    public function list_assistants($params = []) {
        try {
            if (empty($this->api_key)) {
                return new WP_Error('missing_api_key', __('API key is not configured', 'q-knowledge-base'));
            }

            $this->rate_limiter->throttle();

            $query_params = [];
            if (!empty($params['limit'])) {
                $query_params['limit'] = intval($params['limit']);
            }
            if (!empty($params['order'])) {
                $query_params['order'] = $params['order'];
            }
            if (!empty($params['after'])) {
                $query_params['after'] = $params['after'];
            }
            if (!empty($params['before'])) {
                $query_params['before'] = $params['before'];
            }

            $query_string = !empty($query_params) ? '?' . http_build_query($query_params) : '';
            $response = $this->make_request("assistants$query_string", [], 30, 'GET');

            if (is_wp_error($response)) {
                return $response;
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);

            if (isset($body['error'])) {
                return new WP_Error('openai_error', $body['error']['message']);
            }

            // Cache the assistants
            if (!empty($body['data'])) {
                foreach ($body['data'] as $assistant) {
                    $this->assistants_cache[$assistant['id']] = $assistant;
                }
            }

            return $body;
        } catch (Exception $e) {
            $this->error_handler->log_error('Assistants list error', $e);
            return new WP_Error('assistants_list_error', $e->getMessage());
        }
    }

    /**
     * Create a thread for an assistant
     *
     * @return string|WP_Error Thread ID or error
     */
    public function create_thread() {
        try {
            if (empty($this->api_key)) {
                return new WP_Error('missing_api_key', __('API key is not configured', 'q-knowledge-base'));
            }

            $this->rate_limiter->throttle();

            $response = $this->make_request('threads', []);

            if (is_wp_error($response)) {
                return $response;
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);

            if (isset($body['error'])) {
                return new WP_Error('openai_error', $body['error']['message']);
            }

            return $body['id'];
        } catch (Exception $e) {
            $this->error_handler->log_error('Thread creation error', $e);
            return new WP_Error('thread_creation_error', $e->getMessage());
        }
    }

    /**
     * Add a message to a thread
     *
     * @param string $thread_id Thread ID
     * @param string $content Message content
     * @param string $role Message role (user or assistant)
     * @return string|WP_Error Message ID or error
     */
    public function add_message($thread_id, $content, $role = 'user') {
        try {
            if (empty($this->api_key)) {
                return new WP_Error('missing_api_key', __('API key is not configured', 'q-knowledge-base'));
            }

            $this->rate_limiter->throttle();

            $request_data = [
                'role' => $role,
                'content' => $content
            ];

            $response = $this->make_request("threads/$thread_id/messages", $request_data);

            if (is_wp_error($response)) {
                return $response;
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);

            if (isset($body['error'])) {
                return new WP_Error('openai_error', $body['error']['message']);
            }

            return $body['id'];
        } catch (Exception $e) {
            $this->error_handler->log_error('Message addition error', $e);
            return new WP_Error('message_addition_error', $e->getMessage());
        }
    }

    /**
     * Run an assistant on a thread
     *
     * @param string $thread_id Thread ID
     * @param string $assistant_id Assistant ID
     * @return string|WP_Error Run ID or error
     */
    public function run_assistant($thread_id, $assistant_id) {
        try {
            if (empty($this->api_key)) {
                return new WP_Error('missing_api_key', __('API key is not configured', 'q-knowledge-base'));
            }

            $this->rate_limiter->throttle();

            $request_data = [
                'assistant_id' => $assistant_id
            ];

       
            $response = $this->make_request("threads/$thread_id/runs", $request_data);

            if (is_wp_error($response)) {
                return $response;
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);

            if (isset($body['error'])) {
                return new WP_Error('openai_error', $body['error']['message']);
            }

            return $body['id'];
        } catch (Exception $e) {
            $this->error_handler->log_error('Assistant run error', $e);
            return new WP_Error('assistant_run_error', $e->getMessage());
        }
    }

    /**
     * Get the status of a run
     *
     * @param string $thread_id Thread ID
     * @param string $run_id Run ID
     * @return array|WP_Error Run status or error
     */
    public function get_run_status($thread_id, $run_id) {
        try {
            if (empty($this->api_key)) {
                return new WP_Error('missing_api_key', __('API key is not configured', 'q-knowledge-base'));
            }

            $this->rate_limiter->throttle();

            $response = $this->make_request("threads/$thread_id/runs/$run_id", [], 30, 'GET');

            if (is_wp_error($response)) {
                return $response;
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);

            if (isset($body['error'])) {
                return new WP_Error('openai_error', $body['error']['message']);
            }

            return $body;
        } catch (Exception $e) {
            $this->error_handler->log_error('Run status error', $e);
            return new WP_Error('run_status_error', $e->getMessage());
        }
    }

    /**
     * Get messages from a thread
     *
     * @param string $thread_id Thread ID
     * @param array $params Optional parameters
     * @return array|WP_Error Messages or error
     */
    public function get_messages($thread_id, $params = []) {
        try {
            if (empty($this->api_key)) {
                return new WP_Error('missing_api_key', __('API key is not configured', 'q-knowledge-base'));
            }

            $this->rate_limiter->throttle();

            $query_params = [];
            if (!empty($params['limit'])) {
                $query_params['limit'] = intval($params['limit']);
            }
            if (!empty($params['order'])) {
                $query_params['order'] = $params['order'];
            }
            if (!empty($params['after'])) {
                $query_params['after'] = $params['after'];
            }
            if (!empty($params['before'])) {
                $query_params['before'] = $params['before'];
            }

            $query_string = !empty($query_params) ? '?' . http_build_query($query_params) : '';
            $response = $this->make_request("threads/$thread_id/messages$query_string", [], 30, 'GET');

            if (is_wp_error($response)) {
                return $response;
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);

            if (isset($body['error'])) {
                return new WP_Error('openai_error', $body['error']['message']);
            }

            return $body;
        } catch (Exception $e) {
            $this->error_handler->log_error('Messages retrieval error', $e);
            return new WP_Error('messages_retrieval_error', $e->getMessage());
        }
    }





    /**
     * Sync an assistant with OpenAI
     *
     * @param int $term_id Assistant term ID
     * @param array $term_data Assistant term data
     */
    public function sync_assistant_with_openai($term_id, $term_data) {
        // Get OpenAI assistant ID
        $openai_assistant_id = get_term_meta($term_id, 'openai_assistant_id', true);

        // Get assistant data
        $assistant_data = [
            'name' => $term_data['name'],
            'description' => $term_data['description'] ?? '',
            'instructions' => get_term_meta($term_id, 'assistant_prompt', true),
            'model' => get_term_meta($term_id, 'assistant_model', true) ?: $this->model,
        ];
     
        if (empty($openai_assistant_id)) {
            // Create new assistant
            $result = $this->create_assistant($assistant_data);
            if (!is_wp_error($result)) {
                update_term_meta($term_id, 'openai_assistant_id', $result);
            }
        } else {
            // Update existing assistant
            $this->update_assistant($openai_assistant_id, $assistant_data);
        }
    }

    /**
     * Delete an assistant from OpenAI
     *
     * @param int $term_id Assistant term ID
     */
    public function delete_assistant_from_openai($term_id) {
        // Get OpenAI assistant ID
        $openai_assistant_id = get_term_meta($term_id, 'openai_assistant_id', true);

        if (!empty($openai_assistant_id)) {
            $this->delete_assistant($openai_assistant_id);
        }
    }

    /**
     * AJAX handler for creating an OpenAI assistant
     */
    public function ajax_create_assistant() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Get assistant data
        $assistant_data = [
            'name' => sanitize_text_field($_POST['name'] ?? ''),
            'description' => sanitize_textarea_field($_POST['description'] ?? ''),
            'instructions' => wp_kses_post($_POST['instructions'] ?? ''),
            'model' => sanitize_text_field($_POST['model'] ?? $this->model),
        ];

 

        // Create assistant
        $result = $this->create_assistant($assistant_data);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(['assistant_id' => $result]);
        }
    }

    /**
     * AJAX handler for updating an OpenAI assistant
     */
    public function ajax_update_assistant() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Get assistant ID
        $assistant_id = sanitize_text_field($_POST['assistant_id'] ?? '');
        if (empty($assistant_id)) {
            wp_send_json_error('Assistant ID is required');
        }

        // Get assistant data
        $assistant_data = [];

        if (isset($_POST['name'])) {
            $assistant_data['name'] = sanitize_text_field($_POST['name']);
        }

        if (isset($_POST['description'])) {
            $assistant_data['description'] = sanitize_textarea_field($_POST['description']);
        }

        if (isset($_POST['instructions'])) {
            $assistant_data['instructions'] = wp_kses_post($_POST['instructions']);
        }

        if (isset($_POST['model'])) {
            $assistant_data['model'] = sanitize_text_field($_POST['model']);
        }

    

        // Update assistant
        $result = $this->update_assistant($assistant_id, $assistant_data);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success();
        }
    }

    /**
     * AJAX handler for deleting an OpenAI assistant
     */
    public function ajax_delete_assistant() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Get assistant ID
        $assistant_id = sanitize_text_field($_POST['assistant_id'] ?? '');
        if (empty($assistant_id)) {
            wp_send_json_error('Assistant ID is required');
        }

        // Delete assistant
        $result = $this->delete_assistant($assistant_id);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success();
        }
    }

    /**
     * AJAX handler for getting OpenAI assistants
     */
    public function ajax_get_assistants() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Get assistants
        $result = $this->list_assistants();

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success($result);
        }
    }

    /**
     * Make a request to the OpenAI API
     *
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @param int $timeout Request timeout
     * @param string $method Request method
     * @return array|WP_Error Response or error
     */
    protected function make_request($endpoint, $data = [], $timeout = 30, $method = 'POST') {
        $args = [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->api_key,
                'Content-Type' => 'application/json',
            ],
            'timeout' => $timeout,
        ];

        if ($method === 'POST' || $method === 'PATCH') {
            $args['body'] = wp_json_encode($data);
        }

        if ($method === 'GET') {
            $response = wp_remote_get("https://api.openai.com/v1/$endpoint", $args);
        } elseif ($method === 'POST') {
            $response = wp_remote_post("https://api.openai.com/v1/$endpoint", $args);
        } elseif ($method === 'PATCH') {
            $args['method'] = 'PATCH';
            $response = wp_remote_request("https://api.openai.com/v1/$endpoint", $args);
        } elseif ($method === 'DELETE') {
            $args['method'] = 'DELETE';
            $response = wp_remote_request("https://api.openai.com/v1/$endpoint", $args);
        } else {
            return new WP_Error('invalid_method', __('Invalid request method', 'q-knowledge-base'));
        }

        // Track API usage
        do_action('qkb_after_openai_request', $response, $endpoint);

        return $response;
    }
}
