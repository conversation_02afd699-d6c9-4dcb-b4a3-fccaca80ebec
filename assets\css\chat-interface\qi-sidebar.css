/* Sidebar Styling */

.qi-sidebar {
    grid-area: sidebar;
    width: 270px;
    max-width: 270px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transform: translateX(0);
    position: relative;
    border-right: 1px solid var(--qkb-primary-light);
}

/* Branding Styling */

.qi-brand {
    display: flex;
    align-items: center;
    padding: var(--qkb-padding-md);
    gap: var(--qkb-gap-md);
    border-bottom: 2px solid var(--qkb-primary-light);
    padding-left: var(--qkb-gap-lg);
}

.qi-logo-wrapper {
    width: 50px;
    height: 50px;
    max-width: 100%;
    max-height: 100%;
    padding: var(--qkb-gap-md);
    background: var(--qkb-gradient);
    border-radius: var(--qkb-radius-sm);
    box-shadow: var(--qkb-shadow-sm);
}


.qi-primary-logo img.qkb-svg-image {
    filter: var(--qkb-filter);
}

.qi-primary-logo img {
    border-radius: var(--qkb-radius-xs);
}

.qi-brand-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    line-height: 1.2;
}

.qi-brand-name {
    font-size: calc(var(--qkb-font-size-xs) + var(--qkb-font-size-sm));
    font-weight: var(--qkb-font-weight-bold);
    color: var(--qkb-primary-dark);
    letter-spacing: var(--qkb-letter-spacing);
    margin: 0;
    padding: 0;
}

.qi-brand-slogan {
    font-size: var(--qkb-font-size-sm);
    color: var(--qkb-text-light);
    opacity: 0.8;
    margin: 0;
    padding: 0;
}

/* Assistant Selection Styling */


.qi-assistants-list h3,
.qi-chat-history h3 {
    font-size: var(--qkb-font-size-xs);
    color: var(--qkb-primary);
    opacity: 0.8;
    font-weight: var(--qkb-font-weight-semibold);
    text-transform: uppercase;
}

.qi-assistants-list {
    padding: var(--qkb-padding-md);
    flex: 0 0 auto;
}

.qi-assistants-wrapper {
    background: var(--qkb-primary-light);
    display: flex;
    flex-direction: column;
    min-height: 60px;
    max-height: 170px;
    overflow-y: auto;
    padding: var(--qkb-padding-sm);
    border-radius: var(--qkb-radius-xs);
    border: 1px solid var(--qkb-primary-light);
}

.qi-assistant-item {
    display: flex;
    align-items: center;
    gap: var(--qkb-gap-md);
    padding: var(--qkb-gap);
    border-radius: var(--qkb-radius-xs);
    border: 1px solid transparent;
    background: var(--qkb-bg);
    cursor: pointer;
    position: relative;
    margin-bottom: var(--qkb-gap);
    transition: var(--qkb-transition);
}

.qi-assistant-item:hover {
    border: 1px solid var(--qkb-primary-dark);
    transform: var(--qkb-transform);
}

.qi-assistant-item.active {
    background: var(--qkb-primary-dark);
    color: var(--qkb-bg);
}

.qi-assistant-item.active .qi-assistant-name {
    color: var(--qkb-bg);
}

.qi-assistant-item.active .qi-assistant-avatar {
    background: var(--qkb-bg);
}

.qi-assistant-item.active .qi-assistant-avatar i {
    color: var(--qkb-primary-dark);
}

.qi-assistant-name {
    font-size: var(--qkb-font-size-xs);
    font-weight: var(--qkb-font-weight-semibold);
    color: var(--qkb-text);
    overflow: hidden;
    text-overflow: ellipsis;
}

.qi-assistant-avatar {
    position: relative;
    width: 30px;
    height: 30px;
    background: var(--qkb-primary-light);
    border-radius: var(--qkb-radius-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--qkb-primary);
}


/* History Styling */

.qi-clear-history {
    background: transparent;
    color: var(--qkb-primary-light);
    cursor: pointer;
    transition: var(--qkb-transition);
    border: none;
}

.qi-clear-history:hover,
.qi-clear-history:hover i {
    color: var(--qkb-error);
    background: transparent;
    transform: var(--qkb-transform);
}


.qi-chat-history {
    padding: var(--qkb-padding-md);
    padding-top: 0;
    flex: 1 1 auto;
    min-width: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.qi-no-history {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    text-align: center;
    padding: var(--qkb-gap-xl);
    color: var(--qkb-text-light);
}

.qi-no-history-icon {
    font-size: calc(var(--qkb-font-size-lg) * 2);
    color: var(--qkb-text-light);
    opacity: 0.3;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qi-no-history p {
    margin: var(--qkb-gap-md) 0;
    font-size: var(--qkb-font-size-sm);
    font-weight: var(--qkb-font-weight-medium);
    color:var(--qkb-text);
    opacity: 0.5;
    text-transform: uppercase;
}

.qi-no-history-desc {
    color: var(--qkb-text-light) !important;
    font-size: var(--qkb-font-size-xs);
    opacity: 0.3 !important;
    font-weight: normal !important;
    text-transform: none !important;
}

.qi-history-wrapper {
    flex: 1;
    overflow-y: auto;
    padding: var(--qkb-padding-sm);
    display: flex;
    flex-direction: column;
    gap: var(--qkb-gap);
}

.qi-history-item {
    flex-shrink: 0;
    border-radius: var(--qkb-radius-xs);
    padding: var(--qkb-padding-sm);
    cursor: pointer;
    transition: var(--qkb-transition);
    position: relative;
    border: 1px solid var(--qkb-primary-light);
}

.qi-history-item:hover {
    background: var(--qkb-primary-light);
    transform: var(--qkb-transform);
    border: none;
}

.qi-history-assistant {
    font-size: var(--qkb-font-size-xs);
    font-weight: var(--qkb-font-weight-medium);
    color: var(--qkb-text);

}

.qi-history-title {
    font-size: var(--qkb-font-size-xs);
    color: var(--qkb-text-light);
}

.qi-delete-history {
    position: absolute;
    top: 0px;
    right: 4px;
    opacity: 0;
    background: transparent;
    padding: 0;
    border: none;
    cursor: pointer;
    transition: var(--qkb-transition);
}

.qi-history-meta {
    display: flex;
    justify-content: flex-end;
    font-size: calc(var(--qkb-font-size-xs) - 2px);
    color: var(--qkb-primary-dark);
    font-weight: var(--qkb-font-weight-medium);
    opacity: 0.8;
}


.qi-history-list {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
}

.qi-history-item:hover .qi-delete-history {
    opacity: 0.8;
    color: var(--qkb-primary-dark);
    background: transparent;
}

/* End History Styling */


/* End Sidebar Styling */