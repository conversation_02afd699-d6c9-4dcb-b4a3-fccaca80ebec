/**
 * Prompt Analytics JavaScript
 */
(function($) {
    'use strict';

    // Charts
    let usageChart = null;
    let performanceChart = null;

    // Initialize
    $(document).ready(function() {
        // Load initial data
        loadAnalyticsData();

        // Set up event listeners
        $('#qkb-refresh-analytics').on('click', loadAnalyticsData);
        $('#qkb-prompt-type-filter, #qkb-date-range-filter').on('change', loadAnalyticsData);
    });

    /**
     * Load analytics data via AJAX
     */
    function loadAnalyticsData() {
        const promptType = $('#qkb-prompt-type-filter').val();
        const dateRange = $('#qkb-date-range-filter').val();
        
        // Show loading state
        $('#qkb-refresh-analytics').addClass('loading');
        $('#qkb-refresh-analytics i').addClass('spin');
        $('#qkb-top-prompts-table').html('<tr><td colspan="5">Loading data...</td></tr>');
        
        // Make AJAX request
        $.ajax({
            url: qkbPromptAnalytics.ajaxUrl,
            type: 'POST',
            data: {
                action: 'qkb_get_prompt_analytics',
                nonce: qkbPromptAnalytics.nonce,
                prompt_type: promptType,
                date_range: dateRange
            },
            success: function(response) {
                if (response.success) {
                    updateAnalyticsDashboard(response.data);
                } else {
                    alert(response.data.message || qkbPromptAnalytics.errorMessage);
                }
            },
            error: function() {
                alert(qkbPromptAnalytics.errorMessage);
            },
            complete: function() {
                // Reset loading state
                $('#qkb-refresh-analytics').removeClass('loading');
                $('#qkb-refresh-analytics i').removeClass('spin');
            }
        });
    }

    /**
     * Update analytics dashboard with data
     * 
     * @param {Object} data Analytics data
     */
    function updateAnalyticsDashboard(data) {
        // Update summary metrics
        updateSummaryMetrics(data.summary);
        
        // Update charts
        updateUsageChart(data.usage_data);
        updatePerformanceChart(data.performance_data);
        
        // Update top prompts table
        updateTopPromptsTable(data.top_prompts);
    }

    /**
     * Update summary metrics
     * 
     * @param {Object} summary Summary data
     */
    function updateSummaryMetrics(summary) {
        $('#qkb-total-uses').text(summary.total_uses.toLocaleString());
        $('#qkb-avg-rating').text(summary.avg_rating.toFixed(1));
        $('#qkb-success-rate').text(summary.success_rate.toFixed(1) + '%');
        $('#qkb-avg-response-time').text(summary.avg_response_time.toLocaleString() + 'ms');
    }

    /**
     * Update usage chart
     * 
     * @param {Object} usageData Usage data
     */
    function updateUsageChart(usageData) {
        const ctx = document.getElementById('qkb-usage-chart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (usageChart) {
            usageChart.destroy();
        }
        
        // Create new chart
        usageChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: usageData.labels,
                datasets: [{
                    label: 'Prompt Usage',
                    data: usageData.data,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Uses'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                }
            }
        });
    }

    /**
     * Update performance chart
     * 
     * @param {Object} performanceData Performance data
     */
    function updatePerformanceChart(performanceData) {
        const ctx = document.getElementById('qkb-performance-chart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (performanceChart) {
            performanceChart.destroy();
        }
        
        // Create new chart
        performanceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: performanceData.labels,
                datasets: [
                    {
                        label: 'Success Rate (%)',
                        data: performanceData.success_data,
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 2,
                        tension: 0.3,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Avg. Rating',
                        data: performanceData.rating_data,
                        backgroundColor: 'rgba(255, 159, 64, 0.2)',
                        borderColor: 'rgba(255, 159, 64, 1)',
                        borderWidth: 2,
                        tension: 0.3,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Success Rate (%)'
                        },
                        position: 'left'
                    },
                    y1: {
                        beginAtZero: true,
                        max: 5,
                        title: {
                            display: true,
                            text: 'Rating (0-5)'
                        },
                        position: 'right',
                        grid: {
                            drawOnChartArea: false
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                }
            }
        });
    }

    /**
     * Update top prompts table
     * 
     * @param {Array} topPrompts Top prompts data
     */
    function updateTopPromptsTable(topPrompts) {
        let tableHtml = '';
        
        if (topPrompts.length === 0) {
            tableHtml = '<tr><td colspan="5">No data available</td></tr>';
        } else {
            topPrompts.forEach(function(prompt) {
                tableHtml += `
                    <tr>
                        <td>${prompt.name}</td>
                        <td>${formatPromptType(prompt.type)}</td>
                        <td>${prompt.uses.toLocaleString()}</td>
                        <td>${prompt.success_rate.toFixed(1)}%</td>
                        <td>${prompt.avg_rating.toFixed(1)}</td>
                    </tr>
                `;
            });
        }
        
        $('#qkb-top-prompts-table').html(tableHtml);
    }

    /**
     * Format prompt type for display
     * 
     * @param {string} type Prompt type
     * @return {string} Formatted type
     */
    function formatPromptType(type) {
        switch (type) {
            case 'system':
                return 'System';
            case 'assistant':
                return 'Assistant';
            case 'template':
                return 'Template';
            default:
                return type.charAt(0).toUpperCase() + type.slice(1);
        }
    }

})(jQuery);
