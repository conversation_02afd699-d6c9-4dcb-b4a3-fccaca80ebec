/**
 * Feedback Analytics JavaScript
 */
(function($) {
    'use strict';

    // Charts
    let feedbackTrendChart = null;
    let feedbackByAssistantChart = null;

    // Initialize
    $(document).ready(function() {
        // Load initial data
        loadAnalyticsData();

        // Set up event listeners
        $('#qkb-refresh-analytics').on('click', loadAnalyticsData);
        $('#qkb-date-range-filter, #qkb-assistant-filter').on('change', loadAnalyticsData);

        // Set up pagination
        $(document).on('click', '.qkb-pagination a', function(e) {
            e.preventDefault();
            const page = $(this).data('page');
            loadFeedbackTable(page);
        });

        // Set up search
        $('#qkb-feedback-search').on('keyup', function(e) {
            if (e.keyCode === 13) {
                loadFeedbackTable(1);
            }
        });

        // Set up export
        $('#qkb-export-feedback').on('click', exportFeedback);

        // Set up clear feedback
        $('#qkb-clear-feedback').on('click', clearFeedback);

        // Set up feedback details modal
        $(document).on('click', '.qkb-view-details', function() {
            const feedbackId = $(this).data('id');
            loadFeedbackDetails(feedbackId);
        });
    });

    /**
     * Load analytics data via AJAX
     */
    function loadAnalyticsData() {
        const dateRange = $('#qkb-date-range-filter').val();
        const assistantId = $('#qkb-assistant-filter').val();

        // Show loading state
        $('#qkb-refresh-analytics').addClass('loading');
        $('#qkb-refresh-analytics i').addClass('spin');
        $('.qkb-analytics-card .qkb-stat').html('<span class="spinner is-active"></span>');

        // Load feedback table (first page)
        loadFeedbackTable(1);

        // Make AJAX request for analytics data
        $.ajax({
            url: qkbFeedbackAnalytics.ajaxUrl,
            type: 'POST',
            data: {
                action: 'qkb_get_feedback_analytics',
                nonce: qkbFeedbackAnalytics.nonce,
                date_range: dateRange,
                assistant_id: assistantId
            },
            success: function(response) {
                if (response.success) {
                    updateAnalyticsDashboard(response.data);
                } else {
                    alert(response.data.message || qkbFeedbackAnalytics.errorMessage);
                }
            },
            error: function() {
                alert(qkbFeedbackAnalytics.errorMessage);
            },
            complete: function() {
                // Reset loading state
                $('#qkb-refresh-analytics').removeClass('loading');
                $('#qkb-refresh-analytics i').removeClass('spin');
            }
        });
    }

    /**
     * Load feedback table via AJAX
     *
     * @param {number} page Page number
     */
    function loadFeedbackTable(page) {
        const dateRange = $('#qkb-date-range-filter').val();
        const assistantId = $('#qkb-assistant-filter').val();
        const search = $('#qkb-feedback-search').val();

        // Show loading state
        $('#qkb-feedback-table').html('<div class="spinner is-active" style="float:none;width:100%;height:100px;padding:20px 0;"></div>');

        // Make AJAX request
        $.ajax({
            url: qkbFeedbackAnalytics.ajaxUrl,
            type: 'POST',
            data: {
                action: 'qkb_get_feedback_table',
                nonce: qkbFeedbackAnalytics.nonce,
                page: page,
                date_range: dateRange,
                assistant_id: assistantId,
                search: search
            },
            success: function(response) {
                if (response.success) {
                    $('#qkb-feedback-table').html(response.data.html);
                } else {
                    $('#qkb-feedback-table').html('<p class="error">' + (response.data.message || qkbFeedbackAnalytics.errorMessage) + '</p>');
                }
            },
            error: function() {
                $('#qkb-feedback-table').html('<p class="error">' + qkbFeedbackAnalytics.errorMessage + '</p>');
            }
        });
    }

    /**
     * Update analytics dashboard with data
     *
     * @param {Object} data Analytics data
     */
    function updateAnalyticsDashboard(data) {
        // Update summary metrics
        updateSummaryMetrics(data.summary);

        // Update charts
        updateFeedbackTrendChart(data.trend_data);
        updateFeedbackByAssistantChart(data.assistant_data);
    }

    /**
     * Update summary metrics
     *
     * @param {Object} summary Summary data
     */
    function updateSummaryMetrics(summary) {
        $('#qkb-total-interactions').text(summary.total_interactions.toLocaleString());

        // Satisfaction rate with trend indicator
        const trendClass = summary.satisfaction_trend > 0 ? 'positive' : (summary.satisfaction_trend < 0 ? 'negative' : '');
        const trendIcon = summary.satisfaction_trend > 0 ? '↑' : (summary.satisfaction_trend < 0 ? '↓' : '');
        const trendHtml = summary.satisfaction_trend !== 0 ?
            `<span class="qkb-trend ${trendClass}">${trendIcon} ${Math.abs(summary.satisfaction_trend)}%</span>` : '';

        $('#qkb-satisfaction-rate').html(summary.satisfaction_rate.toFixed(1) + '% ' + trendHtml);

        // Feedback rate
        $('#qkb-feedback-rate').text(summary.feedback_rate.toFixed(1) + '%');

        // Average response length
        $('#qkb-avg-response-length').text(summary.avg_response_length.toLocaleString());
    }

    /**
     * Update feedback trend chart
     *
     * @param {Object} trendData Trend data
     */
    function updateFeedbackTrendChart(trendData) {
        const ctx = document.getElementById('qkb-feedback-trend-chart').getContext('2d');

        // Destroy existing chart if it exists
        if (feedbackTrendChart) {
            feedbackTrendChart.destroy();
        }

        // Create new chart
        feedbackTrendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: trendData.labels,
                datasets: [
                    {
                        label: 'Positive Feedback',
                        data: trendData.positive,
                        borderColor: '#10b981', // Using our new success color
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true,
                        borderWidth: 2
                    },
                    {
                        label: 'Negative Feedback',
                        data: trendData.negative,
                        borderColor: '#ef4444', // Using our new danger color
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4,
                        fill: true,
                        borderWidth: 2
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#1f2937',
                        bodyColor: '#4b5563',
                        borderColor: '#e5e7eb',
                        borderWidth: 1,
                        padding: 12,
                        cornerRadius: 8,
                        boxPadding: 6,
                        usePointStyle: true,
                        titleFont: {
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(229, 231, 235, 0.5)'
                        },
                        border: {
                            dash: [4, 4]
                        },
                        ticks: {
                            color: '#6b7280',
                            padding: 10
                        },
                        title: {
                            display: true,
                            text: 'Number of Interactions',
                            color: '#4b5563',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            padding: {
                                bottom: 10
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6b7280',
                            padding: 10
                        },
                        title: {
                            display: true,
                            text: 'Date',
                            color: '#4b5563',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            padding: {
                                top: 10
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Update feedback by assistant chart
     *
     * @param {Object} assistantData Assistant data
     */
    function updateFeedbackByAssistantChart(assistantData) {
        const ctx = document.getElementById('qkb-feedback-by-assistant-chart').getContext('2d');

        // Destroy existing chart if it exists
        if (feedbackByAssistantChart) {
            feedbackByAssistantChart.destroy();
        }

        // Create new chart
        feedbackByAssistantChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: assistantData.labels,
                datasets: [
                    {
                        label: 'Satisfaction Rate (%)',
                        data: assistantData.satisfaction_rates,
                        backgroundColor: assistantData.satisfaction_rates.map(rate =>
                            rate >= 80 ? 'rgba(16, 185, 129, 0.8)' : // Success color
                            rate >= 60 ? 'rgba(234, 179, 8, 0.8)' :  // Warning color
                            'rgba(239, 68, 68, 0.8)'                 // Danger color
                        ),
                        borderRadius: 6,
                        borderWidth: 0,
                        maxBarThickness: 50
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const index = context.dataIndex;
                                return [
                                    'Satisfaction Rate: ' + context.raw + '%',
                                    'Total Interactions: ' + assistantData.total_interactions[index],
                                    'Positive Feedback: ' + assistantData.positive_feedback[index]
                                ];
                            }
                        },
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#1f2937',
                        bodyColor: '#4b5563',
                        borderColor: '#e5e7eb',
                        borderWidth: 1,
                        padding: 12,
                        cornerRadius: 8,
                        boxPadding: 6,
                        titleFont: {
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        grid: {
                            color: 'rgba(229, 231, 235, 0.5)'
                        },
                        border: {
                            dash: [4, 4]
                        },
                        ticks: {
                            color: '#6b7280',
                            padding: 10,
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        title: {
                            display: true,
                            text: 'Satisfaction Rate',
                            color: '#4b5563',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            padding: {
                                bottom: 10
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6b7280',
                            padding: 10
                        },
                        title: {
                            display: true,
                            text: 'Assistant',
                            color: '#4b5563',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            padding: {
                                top: 10
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Load feedback details via AJAX
     *
     * @param {number} feedbackId Feedback ID
     */
    function loadFeedbackDetails(feedbackId) {
        // Show loading state
        $('#qkb-feedback-details-modal .qkb-modal-content').html('<div class="spinner is-active" style="float:none;width:100%;height:100px;padding:20px 0;"></div>');

        // Show modal with animation
        $('#qkb-feedback-details-modal').show().addClass('active');

        // Make AJAX request
        $.ajax({
            url: qkbFeedbackAnalytics.ajaxUrl,
            type: 'POST',
            data: {
                action: 'qkb_get_feedback_details',
                nonce: qkbFeedbackAnalytics.nonce,
                feedback_id: feedbackId
            },
            success: function(response) {
                if (response.success) {
                    $('#qkb-feedback-details-modal .qkb-modal-content').html(response.data.html);
                } else {
                    $('#qkb-feedback-details-modal .qkb-modal-content').html('<p class="error">' + (response.data.message || qkbFeedbackAnalytics.errorMessage) + '</p>');
                }
            },
            error: function() {
                $('#qkb-feedback-details-modal .qkb-modal-content').html('<p class="error">' + qkbFeedbackAnalytics.errorMessage + '</p>');
            }
        });
    }

    /**
     * Export feedback data
     */
    function exportFeedback() {
        const dateRange = $('#qkb-date-range-filter').val();
        const assistantId = $('#qkb-assistant-filter').val();
        const format = $('#qkb-export-format').val();

        // Create form and submit
        const $form = $('<form>', {
            action: qkbFeedbackAnalytics.ajaxUrl,
            method: 'POST',
            target: '_blank'
        });

        // Add form fields
        $form.append($('<input>', {
            type: 'hidden',
            name: 'action',
            value: 'qkb_export_feedback'
        }));

        $form.append($('<input>', {
            type: 'hidden',
            name: 'nonce',
            value: qkbFeedbackAnalytics.nonce
        }));

        $form.append($('<input>', {
            type: 'hidden',
            name: 'date_range',
            value: dateRange
        }));

        $form.append($('<input>', {
            type: 'hidden',
            name: 'assistant_id',
            value: assistantId
        }));

        $form.append($('<input>', {
            type: 'hidden',
            name: 'format',
            value: format
        }));

        // Append form to body and submit
        $('body').append($form);
        $form.submit();
        $form.remove();
    }

    /**
     * Clear all feedback data
     */
    function clearFeedback() {
        // Show confirmation dialog
        const confirmMessage = 'Are you sure you want to clear ALL feedback data?\n\n' +
                              'This action cannot be undone and will permanently delete:\n' +
                              '• All user feedback (positive and negative)\n' +
                              '• All interaction history\n' +
                              '• All analytics data\n\n' +
                              'Type "DELETE" to confirm:';

        const userInput = prompt(confirmMessage);

        if (userInput !== 'DELETE') {
            if (userInput !== null) {
                alert('Clear operation cancelled. You must type "DELETE" exactly to confirm.');
            }
            return;
        }

        // Show loading state
        const $button = $('#qkb-clear-feedback');
        const originalText = $button.html();
        $button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Clearing...');

        // Make AJAX request
        $.ajax({
            url: qkbFeedbackAnalytics.ajaxUrl,
            type: 'POST',
            data: {
                action: 'qkb_clear_feedback',
                nonce: qkbFeedbackAnalytics.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert('Success: ' + response.data.message);

                    // Reload the analytics data to show empty state
                    loadAnalyticsData();
                } else {
                    alert('Error: ' + (response.data || 'Failed to clear feedback data.'));
                }
            },
            error: function(xhr, status, error) {
                console.error('Clear feedback error:', error);
                alert('Error: Failed to clear feedback data. Please try again.');
            },
            complete: function() {
                // Reset button state
                $button.prop('disabled', false).html(originalText);
            }
        });
    }

    // Close modal when clicking on close button or outside the modal
    $(document).on('click', '.qkb-modal-close, .qkb-modal-overlay', function() {
        const $modal = $('.qkb-modal');
        $modal.removeClass('active');

        // Wait for animation to complete before hiding
        setTimeout(function() {
            $modal.hide();
        }, 300);
    });

    // Prevent modal content clicks from closing the modal
    $(document).on('click', '.qkb-modal-content', function(e) {
        e.stopPropagation();
    });

})(jQuery);
