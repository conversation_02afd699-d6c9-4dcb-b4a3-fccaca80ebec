/**
 * Formidable Forms Handler for Quick Actions
 *
 * This script handles Formidable Forms submissions in quick action modals.
 */
jQuery(document).ready(function($) {
    console.log("Formidable Forms Handler initialized");

    // Function to handle Formidable Forms in modals
    function setupFormidableFormsInModals() {
        console.log("Setting up Formidable Forms in modals");

        // Process all Formidable Forms in the modal - use a more comprehensive selector
        $(".qkb-quick-access-modal .frm_forms form, .qkb-quick-access-modal form.frm-show-form, .qkb-quick-access-modal form[id^='form_']").each(function() {
            var $form = $(this);

            // Skip if already processed
            if ($form.data("qkb-processed")) {
                return;
            }

            // Mark as processed
            $form.data("qkb-processed", true);

            // Log form found
            console.log("Found Formidable Form in modal:", $form.attr("id"));

            // Create a loading overlay container
            var $overlayContainer = $("<div>", {
                "class": "qkb-form-overlay-container",
                "css": {
                    "position": "relative",
                    "min-height": "50px"
                }
            });

            // Wrap the form in the container if not already wrapped
            if (!$form.parent().hasClass("qkb-form-overlay-container")) {
                $form.wrap($overlayContainer);
            }

            // Add submit handler
            $form.off("submit.qkb").on("submit.qkb", function(e) {
                e.preventDefault(); // Prevent default form submission

                console.log("Form submit intercepted");

                var $form = $(this);
                var formId = $form.find("input[name='form_id']").val();

                if (!formId) {
                    var formIdMatch = $form.attr("id") ? $form.attr("id").match(/form_(\d+)/) : null;
                    if (formIdMatch) {
                        formId = formIdMatch[1];
                    }
                }

                if (!formId) {
                    console.error("Could not determine form ID");
                    return;
                }

                console.log("Submitting Formidable Form ID:", formId);

                // Create loading overlay
                var $loadingOverlay = $("<div class='qkb-form-loading-overlay'><div class='qkb-form-spinner'></div><div class='qkb-form-loading-text'>Submitting form...</div></div>");
                $form.parent().append($loadingOverlay);

                // Collect form data
                var formData = new FormData($form[0]);

                // Add a flag to indicate this is from a quick action
                formData.append("qkb_quick_action", "1");

                // Add frm_ajax=1 to ensure Formidable Forms processes it correctly
                formData.append("frm_ajax", "1");

                // Set a cookie to help server-side code identify this as a quick action
                document.cookie = "qkb_quick_action_active=1; path=/; max-age=3600";

                // Add assistant_action flag to indicate this is from an assistant action
                formData.append("qkb_assistant_action", "1");

                // Log form data for debugging
                console.log("Form data:");
                for (var pair of formData.entries()) {
                    console.log(pair[0] + ": " + pair[1]);
                }

                // Skip the direct Formidable Forms submission and use our custom endpoint directly
                // Convert FormData to a regular object for easier handling
                var formDataObj = {};
                var itemMeta = {};

                // Process form data to properly handle item_meta fields
                for (var pair of formData.entries()) {
                    // Check if this is an item_meta field
                    if (pair[0].startsWith('item_meta[')) {
                        // Extract the field ID from item_meta[X]
                        var fieldIdMatch = pair[0].match(/item_meta\[(\d+)\]/);
                        if (fieldIdMatch && fieldIdMatch[1]) {
                            var fieldId = fieldIdMatch[1];
                            // Store in the itemMeta object
                            itemMeta[fieldId] = pair[1];
                        }
                    }

                    // Store all fields in the main object as well
                    formDataObj[pair[0]] = pair[1];
                }

                // Log the form data for debugging
                console.log("Submitting form data to custom endpoint:", formDataObj);
                console.log("Extracted item_meta fields:", itemMeta);

                // Try two different approaches to submit the form
                // First, try the direct AJAX submission to our custom endpoint
                $.ajax({
                    url: qkbChatbot.ajax_url,
                    type: "POST",
                    data: {
                        action: "qkb_submit_formidable_form_direct",
                        nonce: qkbChatbot.nonce,
                        form_id: formId,
                        form_data: formDataObj,
                        item_meta: itemMeta
                    },
                    success: function(response) {
                        console.log("Form submission response:", response);

                        // Remove loading overlay
                        $loadingOverlay.remove();

                        if (response.success) {
                            // Show success message
                            var successMessage = response.data.message || "Form submitted successfully!";
                            $form.replaceWith("<div class=\"qkb-form-success\">" + successMessage +
                                "</div><button class=\"qkb-form-close-button\">Close</button>");

                            // Add close button handler
                            $(".qkb-form-close-button").on("click", function() {
                                $(".qkb-quick-access-modal").removeClass("active");
                            });
                        } else {
                            // Show error message
                            var errorMessage = response.data || "There was an error submitting the form. Please try again.";
                            $form.prepend("<div class=\"qkb-form-error\">" + errorMessage + "</div>");

                            // Auto-remove error message after 5 seconds
                            setTimeout(function() {
                                $form.find(".qkb-form-error").fadeOut(300, function() {
                                    $(this).remove();
                                });
                            }, 5000);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Form submission error:", error);
                        console.error("XHR response:", xhr.responseText);

                        // Try the fallback method - submit to Formidable's own AJAX handler
                        console.log("Trying fallback submission method...");

                        // Create a new FormData object
                        var fallbackFormData = new FormData($form[0]);
                        fallbackFormData.append("frm_ajax", "1");

                        // Submit directly to WordPress admin-ajax.php
                        $.ajax({
                            url: qkbChatbot.ajax_url,
                            type: "POST",
                            data: fallbackFormData,
                            processData: false,
                            contentType: false,
                            success: function(fallbackResponse) {
                                console.log("Fallback form submission response:", fallbackResponse);

                                // Remove loading overlay
                                $loadingOverlay.remove();

                                // Show success message
                                $form.replaceWith("<div class=\"qkb-form-success\">Form submitted successfully!</div><button class=\"qkb-form-close-button\">Close</button>");

                                // Add close button handler
                                $(".qkb-form-close-button").on("click", function() {
                                    $(".qkb-quick-access-modal").removeClass("active");
                                });
                            },
                            error: function(fallbackXhr, fallbackStatus, fallbackError) {
                                console.error("Fallback form submission error:", fallbackError);

                                // Remove loading overlay
                                $loadingOverlay.remove();

                                // Show error message
                                $form.prepend("<div class=\"qkb-form-error\">There was an error submitting the form. Please try again.</div>");

                                // Auto-remove error message after 5 seconds
                                setTimeout(function() {
                                    $form.find(".qkb-form-error").fadeOut(300, function() {
                                        $(this).remove();
                                    });
                                }, 5000);
                            }
                        });
                    }
                });
            });

            // Also handle submit button clicks directly
            $form.find("input[type='submit'], button[type='submit']").off("click.qkb").on("click.qkb", function(e) {
                console.log("Submit button clicked");
                // Let the form's submit handler take care of it
            });
        });
    }

    // Set up handlers when document is ready
    setupFormidableFormsInModals();

    // Also set up handlers when a quick action is triggered
    $(document).on("click", ".qkb-quick-action-trigger-button", function() {
        // Wait for modal content to be loaded
        setTimeout(setupFormidableFormsInModals, 500);
    });

    // Also set up when modal becomes active - using MutationObserver instead of deprecated DOMNodeInserted
    const modalObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                for (var i = 0; i < mutation.addedNodes.length; i++) {
                    var node = mutation.addedNodes[i];
                    if (node.nodeType === 1) { // Only process Element nodes
                        if ($(node).hasClass("qkb-quick-access-modal") || $(node).find(".qkb-quick-access-modal").length) {
                            // Use a longer timeout to ensure the form is fully loaded
                            setTimeout(setupFormidableFormsInModals, 500);
                            // And check again after a longer delay to catch any forms that might be loaded dynamically
                            setTimeout(setupFormidableFormsInModals, 1000);
                            break;
                        }
                    }
                }
            }
        });
    });

    // Start observing the document body for modal additions
    modalObserver.observe(document.body, { childList: true, subtree: true });

    // Monitor for any dynamically added forms
    var observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                for (var i = 0; i < mutation.addedNodes.length; i++) {
                    var node = mutation.addedNodes[i];
                    if (node.nodeType === 1) { // Only process Element nodes
                        if ($(node).hasClass("frm_forms") || $(node).find(".frm_forms").length) {
                            console.log("Formidable Form dynamically added to page");
                            setTimeout(setupFormidableFormsInModals, 100);
                            break;
                        }
                    }
                }
            }
        });
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, { childList: true, subtree: true });
});
