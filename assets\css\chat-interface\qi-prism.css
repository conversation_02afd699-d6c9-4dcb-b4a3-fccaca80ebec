/* Prism.js Syntax Highlighting for Q-Knowledge Base Chat Interface */

/* Base Prism styling */
.qi-code-block code[class*="language-"],
.qi-code-block pre[class*="language-"] {
  color: var(--qkb-text);
  background: none;
  text-shadow: none;
  font-family: "Courier New", Consolas, Monaco, "Andale Mono", "Ubuntu Mono",
    monospace;
  font-size: var(--qkb-font-size-xs);
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  tab-size: 4;
  hyphens: none;
}

/* Code blocks */
.qi-code-block pre[class*="language-"] {
  padding: var(--qkb-gap-md);
  margin: 0;
  overflow: auto;
  border-radius: 0;
  background-color: var(--qkb-bg);
}

/* Inline code */
.qi-code-block :not(pre) > code[class*="language-"] {
  padding: 2px 6px;
  border-radius: 4px;
  white-space: normal;
  background-color: var(--qkb-bg-light);
}

/* Token colors - Light theme */
.qi-code-block .token.comment,
.qi-code-block .token.prolog,
.qi-code-block .token.doctype,
.qi-code-block .token.cdata {
  color: #6a737d;
  font-style: italic;
}

.qi-code-block .token.punctuation {
  color: var(--qkb-text);
}

.qi-code-block .token.property,
.qi-code-block .token.tag,
.qi-code-block .token.boolean,
.qi-code-block .token.number,
.qi-code-block .token.constant,
.qi-code-block .token.symbol,
.qi-code-block .token.deleted {
  color: #d73a49;
}

.qi-code-block .token.selector,
.qi-code-block .token.attr-name,
.qi-code-block .token.string,
.qi-code-block .token.char,
.qi-code-block .token.builtin,
.qi-code-block .token.inserted {
  color: #032f62;
}

.qi-code-block .token.operator,
.qi-code-block .token.entity,
.qi-code-block .token.url,
.qi-code-block .language-css .token.string,
.qi-code-block .style .token.string {
  color: #d73a49;
}

.qi-code-block .token.atrule,
.qi-code-block .token.attr-value,
.qi-code-block .token.keyword {
  color: #d73a49;
  font-weight: 600;
}

.qi-code-block .token.function,
.qi-code-block .token.class-name {
  color: #6f42c1;
  font-weight: 600;
}

.qi-code-block .token.regex,
.qi-code-block .token.important,
.qi-code-block .token.variable {
  color: #e36209;
}

.qi-code-block .token.important,
.qi-code-block .token.bold {
  font-weight: bold;
}

.qi-code-block .token.italic {
  font-style: italic;
}

.qi-code-block .token.entity {
  cursor: help;
}

/* Language-specific styling */
.qi-code-block .language-javascript .token.template-string {
  color: #032f62;
}

.qi-code-block .language-javascript .token.template-punctuation {
  color: #d73a49;
}

.qi-code-block .language-css .token.selector {
  color: #6f42c1;
}

.qi-code-block .language-css .token.property {
  color: #005cc5;
}

.qi-code-block .language-html .token.tag .token.tag {
  color: #22863a;
}

.qi-code-block .language-html .token.tag .token.punctuation {
  color: #22863a;
}

.qi-code-block .language-html .token.attr-name {
  color: #6f42c1;
}

.qi-code-block .language-html .token.attr-value {
  color: #032f62;
}

/* JSON specific */
.qi-code-block .language-json .token.property {
  color: #005cc5;
}

.qi-code-block .language-json .token.string {
  color: #032f62;
}

.qi-code-block .language-json .token.number {
  color: #d73a49;
}

.qi-code-block .language-json .token.boolean {
  color: #d73a49;
}

.qi-code-block .language-json .token.null {
  color: #d73a49;
}

/* PHP specific */
.qi-code-block .language-php .token.delimiter.important {
  color: #d73a49;
  font-weight: normal;
}

.qi-code-block .language-php .token.variable {
  color: #e36209;
}

/* Python specific */
.qi-code-block .language-python .token.decorator {
  color: #6f42c1;
}

.qi-code-block .language-python .token.builtin {
  color: #005cc5;
}

/* SQL specific */
.qi-code-block .language-sql .token.keyword {
  color: #d73a49;
  text-transform: uppercase;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .qi-code-block .token.comment,
  .qi-code-block .token.prolog,
  .qi-code-block .token.doctype,
  .qi-code-block .token.cdata {
    color: #8b949e;
  }

  .qi-code-block .token.property,
  .qi-code-block .token.tag,
  .qi-code-block .token.boolean,
  .qi-code-block .token.number,
  .qi-code-block .token.constant,
  .qi-code-block .token.symbol,
  .qi-code-block .token.deleted {
    color: #ff7b72;
  }

  .qi-code-block .token.selector,
  .qi-code-block .token.attr-name,
  .qi-code-block .token.string,
  .qi-code-block .token.char,
  .qi-code-block .token.builtin,
  .qi-code-block .token.inserted {
    color: #a5d6ff;
  }

  .qi-code-block .token.operator,
  .qi-code-block .token.entity,
  .qi-code-block .token.url,
  .qi-code-block .language-css .token.string,
  .qi-code-block .style .token.string {
    color: #ff7b72;
  }

  .qi-code-block .token.atrule,
  .qi-code-block .token.attr-value,
  .qi-code-block .token.keyword {
    color: #ff7b72;
  }

  .qi-code-block .token.function,
  .qi-code-block .token.class-name {
    color: #d2a8ff;
  }

  .qi-code-block .token.regex,
  .qi-code-block .token.important,
  .qi-code-block .token.variable {
    color: #ffa657;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .qi-code-block code[class*="language-"],
  .qi-code-block pre[class*="language-"] {
    color: #000;
    background: #fff;
  }

  .qi-code-block .token.comment {
    color: #666;
  }

  .qi-code-block .token.string {
    color: #008000;
  }

  .qi-code-block .token.keyword {
    color: #0000ff;
    font-weight: bold;
  }

  .qi-code-block .token.function {
    color: #800080;
    font-weight: bold;
  }
}

/* Fallback styling when Prism.js is not available */
.qi-code-fallback {
  color: var(--qkb-text) !important;
  background: var(--qkb-bg) !important;
  font-family: "Courier New", Consolas, Monaco, monospace !important;
  white-space: pre-wrap !important;
  word-break: break-word !important;
}
