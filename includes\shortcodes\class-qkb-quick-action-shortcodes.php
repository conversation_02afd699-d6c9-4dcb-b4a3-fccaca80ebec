<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for handling quick action shortcodes
 */
class QKB_Quick_Action_Shortcodes
{
    /**
     * Constructor
     */
    public function __construct()
    {
        add_shortcode('qkb_trigger_quick_action', [$this, 'trigger_quick_action_shortcode']);
    }

    /**
     * Shortcode to trigger a quick action by name
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function trigger_quick_action_shortcode($atts)
    {
        $atts = shortcode_atts(
            [
                'name' => '',
            ],
            $atts,
            'qkb_trigger_quick_action'
        );

        if (empty($atts['name'])) {
            return '';
        }

        // Get quick access buttons
        $quick_access_buttons = get_option('qkb_quick_access_buttons', []);

        // Find the button with the matching name
        $found_button = null;
        foreach ($quick_access_buttons as $button) {
            if (isset($button['name']) && strtolower(trim($button['name'])) === strtolower(trim($atts['name']))) {
                $found_button = $button;
                break;
            }
        }

        if (!$found_button) {
            return '';
        }

        // Process shortcodes in the content
        $processed_content = do_shortcode($found_button['content']);

        // Create a unique ID for this button's content
        $content_id = 'qkb_content_' . md5($processed_content . uniqid());

        // Determine if it should be shown in a modal
        $show_in_modal = isset($found_button['show_in_modal']) && $found_button['show_in_modal'] ? 1 : 0;
        $modal_title = isset($found_button['modal_title']) ? esc_attr($found_button['modal_title']) : esc_attr($found_button['name']);

        // Create a more visible trigger that can be detected by JavaScript
        $output = '<span class="qkb-quick-action-trigger-wrapper">';
        $output .= '<button class="qkb-quick-action-trigger-button"
                      data-action-name="' . esc_attr($found_button['name']) . '"
                      data-content-id="' . esc_attr($content_id) . '"
                      data-show-modal="' . esc_attr($show_in_modal) . '"
                      data-modal-title="' . esc_attr($modal_title) . '">
                      <i class="' . esc_attr($found_button['icon']) . '"></i>
                      <span>' . esc_html($found_button['name']) . '</span>
                    </button>';
        $output .= '</span>';

        // Add the hidden content
        $output .= '<div id="' . esc_attr($content_id) . '" class="qkb-hidden-content" style="display: none;">' . $processed_content . '</div>';

        return $output;
    }
}

// Initialize the class
new QKB_Quick_Action_Shortcodes();
