jQuery(document).ready(function($) {
    $(".qkb-generate-embedding").on("click", function() {
        const button = $(this);
        const postId = button.data("post-id");

        button.prop("disabled", true);
        button.find(".dashicons").addClass("dashicons-update-spin");

        $.ajax({
            url: ajaxurl,
            type: "POST",
            data: {
                action: "qkb_generate_embedding",
                post_id: postId,
                nonce: qkb_meta_box.nonce
            },
            success: function(response) {
                if (response.success) {
                    $(".qkb-status").removeClass("qkb-status-warning").addClass("qkb-status-success").text("Generated");
                    location.reload();
                } else {
                    alert("Error: " + response.data);
                }
            },
            error: function() {
                alert("Error generating embedding. Please try again.");
            },
            complete: function() {
                button.prop("disabled", false);
                button.find(".dashicons").removeClass("dashicons-update-spin");
            }
        });
    });
});
