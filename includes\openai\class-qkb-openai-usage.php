<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for tracking and displaying OpenAI API usage
 */
class QKB_OpenAI_Usage
{
    private $usage_data;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->usage_data = get_option('qkb_openai_usage_data', [
            'completion_tokens' => 0,
            'prompt_tokens' => 0,
            'embedding_tokens' => 0,
            'total_requests' => 0,
            'last_reset' => time(),
            'monthly_usage' => []
        ]);

        add_action('qkb_after_openai_request', [$this, 'track_usage'], 10, 2);
        add_action('admin_menu', [$this, 'add_usage_page']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_styles']);
    }

    /**
     * Enqueue styles for the OpenAI usage page
     *
     * @param string $hook Current admin page hook
     */
    public function enqueue_styles($hook)
    {
        if ($hook !== 'kb_knowledge_base_page_qkb-openai-usage') {
            return;
        }

        // Enqueue the modern style for OpenAI usage
        wp_enqueue_style('qkb-modern-openai-usage', QKB_PLUGIN_URL . 'assets/css/modern-openai-usage.css', [], QKB_VERSION);

        // Enqueue root style variables if they exist
        if (file_exists(QKB_PLUGIN_DIR . 'assets/css/root-style.css')) {
            wp_enqueue_style('qkb-root-style', QKB_PLUGIN_URL . 'assets/css/root-style.css', [], QKB_VERSION);
        }
    }

    /**
     * Track API usage from response
     *
     * @param array $response The API response
     * @param string $endpoint The API endpoint used
     */
    public function track_usage($response, $endpoint)
    {
        if (is_wp_error($response)) {
            return;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        if (!isset($body['usage'])) {
            // Increment request count even if usage data isn't available
            $this->usage_data['total_requests']++;
            $this->save_usage_data();
            return;
        }

        $current_month = date('Y-m');
        if (!isset($this->usage_data['monthly_usage'][$current_month])) {
            $this->usage_data['monthly_usage'][$current_month] = [
                'completion_tokens' => 0,
                'prompt_tokens' => 0,
                'embedding_tokens' => 0,
                'total_requests' => 0
            ];
        }

        if ($endpoint === 'chat/completions') {
            $this->usage_data['completion_tokens'] += $body['usage']['completion_tokens'];
            $this->usage_data['prompt_tokens'] += $body['usage']['prompt_tokens'];

            $this->usage_data['monthly_usage'][$current_month]['completion_tokens'] += $body['usage']['completion_tokens'];
            $this->usage_data['monthly_usage'][$current_month]['prompt_tokens'] += $body['usage']['prompt_tokens'];
        } elseif ($endpoint === 'embeddings') {
            $this->usage_data['embedding_tokens'] += $body['usage']['total_tokens'];
            $this->usage_data['monthly_usage'][$current_month]['embedding_tokens'] += $body['usage']['total_tokens'];
        }

        $this->usage_data['total_requests']++;
        $this->usage_data['monthly_usage'][$current_month]['total_requests']++;

        $this->save_usage_data();
    }

    /**
     * Save usage data to database
     */
    private function save_usage_data()
    {
        update_option('qkb_openai_usage_data', $this->usage_data);
    }

    /**
     * Add usage page to admin menu
     */
    public function add_usage_page()
    {
        add_submenu_page(
            'edit.php?post_type=kb_knowledge_base',
            __('OpenAI Usage', 'q-knowledge-base'),
            __('OpenAI Usage', 'q-knowledge-base'),
            'manage_options',
            'qkb-openai-usage',
            [$this, 'render_usage_page']
        );
    }

    /**
     * Render the usage page
     */
    public function render_usage_page()
    {
        // Calculate estimated costs
        $completion_cost = $this->estimate_cost($this->usage_data['completion_tokens'], 'completion');
        $prompt_cost = $this->estimate_cost($this->usage_data['prompt_tokens'], 'prompt');
        $embedding_cost = $this->estimate_cost($this->usage_data['embedding_tokens'], 'embedding');
        $total_cost = $completion_cost + $prompt_cost + $embedding_cost;

        // Get current model for display
        $current_model = get_option('qkb_openai_model', 'gpt-4o');
        $embedding_model = get_option('qkb_openai_embedding_model', 'text-embedding-3-small');

        // Format last reset date
        $last_reset = date('F j, Y', $this->usage_data['last_reset']);

        ?>
        <div class="wrap">
            <h1><?php _e('OpenAI API Usage', 'q-knowledge-base'); ?></h1>

            <div class="qkb-usage-dashboard">
                <div class="qkb-usage-summary">
                    <h2><?php _e('Usage Summary', 'q-knowledge-base'); ?></h2>
                    <p><?php printf(__('Tracking usage since %s', 'q-knowledge-base'), $last_reset); ?></p>

                    <div class="qkb-usage-cards">
                        <div class="qkb-usage-card">
                            <h3><?php _e('Total Tokens', 'q-knowledge-base'); ?></h3>
                            <div class="qkb-usage-value">
                                <?php echo number_format($this->usage_data['completion_tokens'] + $this->usage_data['prompt_tokens'] + $this->usage_data['embedding_tokens']); ?>
                            </div>
                            <div class="qkb-usage-subtitle">
                                <?php printf(__('Estimated cost: $%.2f', 'q-knowledge-base'), $total_cost); ?>
                            </div>
                        </div>

                        <div class="qkb-usage-card">
                            <h3><?php _e('Completion Tokens', 'q-knowledge-base'); ?></h3>
                            <div class="qkb-usage-value">
                                <?php echo number_format($this->usage_data['completion_tokens']); ?>
                            </div>
                            <div class="qkb-usage-subtitle">
                                <?php printf(__('Model: %s', 'q-knowledge-base'), $current_model); ?>
                            </div>
                        </div>

                        <div class="qkb-usage-card">
                            <h3><?php _e('Prompt Tokens', 'q-knowledge-base'); ?></h3>
                            <div class="qkb-usage-value">
                                <?php echo number_format($this->usage_data['prompt_tokens']); ?>
                            </div>
                            <div class="qkb-usage-subtitle">
                                <?php printf(__('Model: %s', 'q-knowledge-base'), $current_model); ?>
                            </div>
                        </div>

                        <div class="qkb-usage-card">
                            <h3><?php _e('Embedding Tokens', 'q-knowledge-base'); ?></h3>
                            <div class="qkb-usage-value">
                                <?php echo number_format($this->usage_data['embedding_tokens']); ?>
                            </div>
                            <div class="qkb-usage-subtitle">
                                <?php printf(__('Model: %s', 'q-knowledge-base'), $embedding_model); ?>
                            </div>
                        </div>

                        <div class="qkb-usage-card">
                            <h3><?php _e('Total Requests', 'q-knowledge-base'); ?></h3>
                            <div class="qkb-usage-value">
                                <?php echo number_format($this->usage_data['total_requests']); ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="qkb-monthly-usage">
                    <h2><?php _e('Monthly Usage', 'q-knowledge-base'); ?></h2>

                    <table class="wp-list-table widefat fixed">
                        <thead>
                            <tr>
                                <th><?php _e('Month', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Completion Tokens', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Prompt Tokens', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Embedding Tokens', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Total Tokens', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Requests', 'q-knowledge-base'); ?></th>
                                <th><?php _e('Est. Cost', 'q-knowledge-base'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if (empty($this->usage_data['monthly_usage'])) {
                                echo '<tr><td colspan="7">' . __('No usage data available yet.', 'q-knowledge-base') . '</td></tr>';
                            } else {
                                // Sort by most recent month first
                                krsort($this->usage_data['monthly_usage']);

                                foreach ($this->usage_data['monthly_usage'] as $month => $data) {
                                    $month_display = date('F Y', strtotime($month . '-01'));
                                    $total_tokens = $data['completion_tokens'] + $data['prompt_tokens'] + $data['embedding_tokens'];

                                    $month_completion_cost = $this->estimate_cost($data['completion_tokens'], 'completion');
                                    $month_prompt_cost = $this->estimate_cost($data['prompt_tokens'], 'prompt');
                                    $month_embedding_cost = $this->estimate_cost($data['embedding_tokens'], 'embedding');
                                    $month_total_cost = $month_completion_cost + $month_prompt_cost + $month_embedding_cost;

                                    echo '<tr>';
                                    echo '<td>' . esc_html($month_display) . '</td>';
                                    echo '<td>' . number_format($data['completion_tokens']) . '</td>';
                                    echo '<td>' . number_format($data['prompt_tokens']) . '</td>';
                                    echo '<td>' . number_format($data['embedding_tokens']) . '</td>';
                                    echo '<td>' . number_format($total_tokens) . '</td>';
                                    echo '<td>' . number_format($data['total_requests']) . '</td>';
                                    echo '<td>$' . number_format($month_total_cost, 2) . '</td>';
                                    echo '</tr>';
                                }
                            }
                            ?>
                        </tbody>
                    </table>
                </div>

                <div class="qkb-usage-actions">
                    <form method="post" action="">
                        <?php wp_nonce_field('qkb_reset_usage_stats', 'qkb_usage_nonce'); ?>
                        <input type="hidden" name="action" value="qkb_reset_usage">
                        <button type="submit"
                            onclick="return confirm('<?php _e('Are you sure you want to reset all usage statistics? This cannot be undone.', 'q-knowledge-base'); ?>');">
                            <?php _e('Reset Usage Statistics', 'q-knowledge-base'); ?>
                        </button>
                    </form>
                </div>

                <div class="qkb-usage-help">
                    <h3><?php _e('About Cost Estimates', 'q-knowledge-base'); ?></h3>
                    <p><?php _e('Cost estimates are approximate and based on OpenAI\'s published pricing. Actual costs may vary based on your specific OpenAI plan and any custom pricing arrangements.', 'q-knowledge-base'); ?>
                    </p>
                    <p><?php _e('For the most accurate cost information, please refer to your OpenAI billing dashboard.', 'q-knowledge-base'); ?>
                    </p>
                </div>
            </div>
        </div>


        <?php

        // Handle reset action
        if (isset($_POST['action']) && $_POST['action'] === 'qkb_reset_usage' && isset($_POST['qkb_usage_nonce'])) {
            if (wp_verify_nonce($_POST['qkb_usage_nonce'], 'qkb_reset_usage_stats')) {
                $this->reset_usage_data();
                echo '<div class="notice notice-success is-dismissible"><p>' . __('Usage statistics have been reset.', 'q-knowledge-base') . '</p></div>';
                echo '<script>window.location.reload();</script>';
            }
        }
    }

    /**
     * Reset usage data
     */
    public function reset_usage_data()
    {
        $this->usage_data = [
            'completion_tokens' => 0,
            'prompt_tokens' => 0,
            'embedding_tokens' => 0,
            'total_requests' => 0,
            'last_reset' => time(),
            'monthly_usage' => [
                date('Y-m') => [
                    'completion_tokens' => 0,
                    'prompt_tokens' => 0,
                    'embedding_tokens' => 0,
                    'total_requests' => 0
                ]
            ]
        ];

        $this->save_usage_data();
    }

    /**
     * Estimate cost based on token usage
     *
     * @param int $tokens Number of tokens
     * @param string $type Type of tokens (completion, prompt, embedding)
     * @return float Estimated cost in USD
     */
    private function estimate_cost($tokens, $type)
    {
        $model = get_option('qkb_openai_model', 'gpt-4o');
        $embedding_model = get_option('qkb_openai_embedding_model', 'text-embedding-3-small');

        // Pricing per 1000 tokens (as of May 2023)
        $pricing = [
            'gpt-3.5-turbo' => [
                'completion' => 0.002,
                'prompt' => 0.0015
            ],
            'gpt-4' => [
                'completion' => 0.06,
                'prompt' => 0.03
            ],
            'gpt-4o' => [
                'completion' => 0.015,
                'prompt' => 0.01
            ],
            'gpt-4-turbo' => [
                'completion' => 0.03,
                'prompt' => 0.01
            ],
            'text-embedding-3-small' => [
                'embedding' => 0.00002
            ],
            'text-embedding-3-large' => [
                'embedding' => 0.00013
            ],
            'text-embedding-ada-002' => [
                'embedding' => 0.0001
            ]
        ];

        if ($type === 'embedding') {
            if (isset($pricing[$embedding_model][$type])) {
                return ($tokens / 1000) * $pricing[$embedding_model][$type];
            }
            // Default to small embedding model pricing if model not found
            return ($tokens / 1000) * 0.00002;
        } else {
            if (isset($pricing[$model][$type])) {
                return ($tokens / 1000) * $pricing[$model][$type];
            }
            // Default to GPT-3.5 pricing if model not found
            return ($tokens / 1000) * ($type === 'completion' ? 0.002 : 0.0015);
        }
    }
}

// Initialize the usage tracker
add_action('plugins_loaded', function () {
    new QKB_OpenAI_Usage();
});
