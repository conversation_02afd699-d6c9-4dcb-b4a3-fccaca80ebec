<?php
/**
 * Fix Feedback Table Script (Simple Version)
 * 
 * This script will fix the feedback table structure issue by directly modifying the database.
 */

// Load WordPress
require_once dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';

// Get the database connection
global $wpdb;

// Define the table name
$table_name = $wpdb->prefix . 'qkb_ml_interactions';

echo "<h1>Q Knowledge Base - Fix Feedback Table</h1>";

// Check if table exists
if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
    echo "<p>Table does not exist. Creating new table...</p>";
    
    // Create the table
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        message_id varchar(255) NOT NULL,
        query text NOT NULL,
        response text NOT NULL,
        feedback int(11) NOT NULL,
        assistant_id bigint(20) NOT NULL,
        created_at datetime NOT NULL,
        PRIMARY KEY  (id),
        KEY assistant_id (assistant_id),
        KEY created_at (created_at)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name) {
        echo "<p style='color: green; font-weight: bold;'>SUCCESS: Table created successfully!</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>ERROR: Failed to create table.</p>";
        if ($wpdb->last_error) {
            echo "<p>Database Error: " . $wpdb->last_error . "</p>";
        }
    }
} else {
    echo "<p>Table exists. Checking structure...</p>";
    
    // Get current columns
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    
    if (empty($columns)) {
        echo "<p style='color: red; font-weight: bold;'>ERROR: Table exists but has no columns.</p>";
    } else {
        echo "<p>Table has " . count($columns) . " columns.</p>";
        
        // Check if we need to recreate the table
        $recreate = false;
        
        // Check for required columns
        $required_columns = ['id', 'message_id', 'query', 'response', 'feedback', 'assistant_id', 'created_at'];
        $missing_columns = [];
        
        $column_names = [];
        foreach ($columns as $col) {
            $column_names[] = $col->Field;
        }
        
        foreach ($required_columns as $req_col) {
            if (!in_array($req_col, $column_names)) {
                $missing_columns[] = $req_col;
                $recreate = true;
            }
        }
        
        if (!empty($missing_columns)) {
            echo "<p style='color: red;'>Missing columns: " . implode(', ', $missing_columns) . "</p>";
        } else {
            echo "<p style='color: green;'>All required columns exist.</p>";
        }
        
        if ($recreate) {
            echo "<p>Recreating table with correct structure...</p>";
            
            // Drop the table
            $wpdb->query("DROP TABLE IF EXISTS $table_name");
            
            // Create the table
            $charset_collate = $wpdb->get_charset_collate();
            
            $sql = "CREATE TABLE $table_name (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                message_id varchar(255) NOT NULL,
                query text NOT NULL,
                response text NOT NULL,
                feedback int(11) NOT NULL,
                assistant_id bigint(20) NOT NULL,
                created_at datetime NOT NULL,
                PRIMARY KEY  (id),
                KEY assistant_id (assistant_id),
                KEY created_at (created_at)
            ) $charset_collate;";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
            
            if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name) {
                echo "<p style='color: green; font-weight: bold;'>SUCCESS: Table recreated successfully!</p>";
            } else {
                echo "<p style='color: red; font-weight: bold;'>ERROR: Failed to recreate table.</p>";
                if ($wpdb->last_error) {
                    echo "<p>Database Error: " . $wpdb->last_error . "</p>";
                }
            }
        } else {
            echo "<p style='color: green; font-weight: bold;'>Table structure appears to be valid. No action needed.</p>";
        }
    }
}

echo "<p>You can now go back to the <a href='" . admin_url('edit.php?post_type=kb_knowledge_base&page=qkb-feedback-analytics') . "'>Feedback Analytics</a> page.</p>";
