<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for testing OpenAI prompts
 */
class QKB_OpenAI_Prompt_Tester
{
    private $openai_handler;
    private $templates_handler;
    private $prompts_handler;
    private $context_handler;

    /**
     * Constructor
     */
    public function __construct()
    {
        add_action('admin_menu', [$this, 'add_tester_page']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_scripts']);
        add_action('wp_ajax_qkb_test_prompt', [$this, 'ajax_test_prompt']);
        add_action('wp_ajax_qkb_estimate_tokens', [$this, 'ajax_estimate_tokens']);

        // Initialize handlers
        $this->openai_handler = new QKB_OpenAI_Handler();
        $this->templates_handler = new QKB_OpenAI_Prompt_Templates();
        $this->prompts_handler = new QKB_OpenAI_Prompts();
        $this->context_handler = new QKB_OpenAI_Context();
    }

    /**
     * Add prompt tester admin page
     */
    public function add_tester_page()
    {
        add_submenu_page(
            'edit.php?post_type=kb_knowledge_base',
            __('Prompt Tester', 'q-knowledge-base'),
            __('Prompt Tester', 'q-knowledge-base'),
            'manage_options',
            'qkb-prompt-tester',
            [$this, 'render_tester_page']
        );
    }

    /**
     * Enqueue scripts and styles for the tester page
     */
    public function enqueue_scripts($hook)
    {
        if ($hook !== 'kb_knowledge_base_page_qkb-prompt-tester') {
            return;
        }

        wp_enqueue_style('codemirror', 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.0/codemirror.min.css', [], '5.62.0');
        wp_enqueue_script('codemirror', 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.0/codemirror.min.js', [], '5.62.0', true);
        wp_enqueue_script('codemirror-mode-javascript', 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.0/mode/javascript/javascript.min.js', ['codemirror'], '5.62.0', true);
        wp_enqueue_script('codemirror-mode-markdown', 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.0/mode/markdown/markdown.min.js', ['codemirror'], '5.62.0', true);

        wp_enqueue_script('qkb-prompt-tester', QKB_PLUGIN_URL . 'assets/js/prompt-tester.js', ['jquery', 'codemirror'], QKB_VERSION, true);

        // Enqueue the modern style instead of the original
        wp_enqueue_style('qkb-prompt-tester-modern', QKB_PLUGIN_URL . 'assets/css/prompt-tester-modern.css', [], QKB_VERSION);

        wp_localize_script('qkb-prompt-tester', 'qkbPromptTester', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('qkb_prompt_tester_nonce'),
            'testingPrompt' => __('Testing prompt...', 'q-knowledge-base'),
            'estimatingTokens' => __('Estimating tokens...', 'q-knowledge-base'),
            'errorMessage' => __('An error occurred. Please try again.', 'q-knowledge-base')
        ]);
    }

    /**
     * Render the prompt tester admin page
     */
    public function render_tester_page()
    {
        // Get assistants for dropdown
        $assistants = get_terms([
            'taxonomy' => 'kb_assistant',
            'hide_empty' => false
        ]);

        // Get templates for dropdown
        $templates = $this->templates_handler->get_templates(true); // Force refresh to get latest templates

        ?>
        <div class="wrap">
            <h1><?php _e('OpenAI Prompt Tester', 'q-knowledge-base'); ?></h1>

            <div class="qkb-prompt-tester-container">
                <div class="qkb-prompt-tester-sidebar">
                    <div class="qkb-prompt-tester-form">
                        <h2><?php _e('Test Parameters', 'q-knowledge-base'); ?></h2>

                        <div class="qkb-form-group">
                            <label for="qkb-test-query"><?php _e('Test Query', 'q-knowledge-base'); ?></label>
                            <textarea id="qkb-test-query" class="large-text" rows="3"
                                placeholder="<?php esc_attr_e('Enter a test query...', 'q-knowledge-base'); ?>"></textarea>
                            <p class="description">
                                <?php _e('Enter a sample user query to test the prompt with.', 'q-knowledge-base'); ?>
                            </p>
                        </div>

                        <div class="qkb-form-group">
                            <label for="qkb-test-assistant"><?php _e('Assistant', 'q-knowledge-base'); ?></label>
                            <select id="qkb-test-assistant">
                                <option value=""><?php _e('None (Default)', 'q-knowledge-base'); ?></option>
                                <?php foreach ($assistants as $assistant): ?>
                                    <option value="<?php echo esc_attr($assistant->term_id); ?>">
                                        <?php echo esc_html($assistant->name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php _e('Select an assistant to use for the test.', 'q-knowledge-base'); ?>
                            </p>
                        </div>

                        <div class="qkb-form-group">
                            <label for="qkb-prompt-source"><?php _e('Prompt Source', 'q-knowledge-base'); ?></label>
                            <select id="qkb-prompt-source">
                                <option value="system"><?php _e('System Default', 'q-knowledge-base'); ?></option>
                                <option value="custom"><?php _e('Custom Prompt', 'q-knowledge-base'); ?></option>
                                <option value="template"><?php _e('Template', 'q-knowledge-base'); ?></option>
                            </select>
                        </div>

                        <div class="qkb-form-group qkb-template-selector" style="display: none;">
                            <label for="qkb-prompt-template"><?php _e('Template', 'q-knowledge-base'); ?></label>
                            <select id="qkb-prompt-template">
                                <?php foreach ($templates as $template): ?>
                                    <option value="<?php echo esc_attr($template['id']); ?>">
                                        <?php echo esc_html($template['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="qkb-form-group qkb-custom-prompt" style="display: none;">
                            <label for="qkb-custom-prompt-text"><?php _e('Custom Prompt', 'q-knowledge-base'); ?></label>
                            <textarea id="qkb-custom-prompt-text" class="large-text code" rows="10"
                                placeholder="<?php esc_attr_e('Enter your custom prompt...', 'q-knowledge-base'); ?>"></textarea>
                            <p class="description">
                                <?php _e('You can use the following variables:', 'q-knowledge-base'); ?><br>
                                <code>{kb_content}</code> - <?php _e('Knowledge base content', 'q-knowledge-base'); ?><br>
                                <code>{user_query}</code> - <?php _e('User question', 'q-knowledge-base'); ?><br>
                                <code>{assistant_name}</code> - <?php _e('Assistant name', 'q-knowledge-base'); ?><br>
                                <code>{site_name}</code> - <?php _e('Website name', 'q-knowledge-base'); ?>
                            </p>
                        </div>

                        <div class="qkb-form-group">
                            <label for="qkb-include-kb-content">
                                <input type="checkbox" id="qkb-include-kb-content" checked>
                                <?php _e('Include Knowledge Base Content', 'q-knowledge-base'); ?>
                            </label>
                            <p class="description">
                                <?php _e('Fetch and include relevant KB content in the prompt.', 'q-knowledge-base'); ?>
                            </p>
                        </div>

                        <div class="qkb-form-group">
                            <label for="qkb-include-conversation-history">
                                <input type="checkbox" id="qkb-include-conversation-history">
                                <?php _e('Simulate Conversation History', 'q-knowledge-base'); ?>
                            </label>
                            <p class="description">
                                <?php _e('Include sample conversation history in the prompt.', 'q-knowledge-base'); ?>
                            </p>
                        </div>

                        <div class="qkb-form-group qkb-conversation-history" style="display: none;">
                            <label
                                for="qkb-conversation-history-text"><?php _e('Conversation History', 'q-knowledge-base'); ?></label>
                            <textarea id="qkb-conversation-history-text" class="large-text" rows="5"
                                placeholder="<?php esc_attr_e('User: Previous question\nAssistant: Previous answer', 'q-knowledge-base'); ?>"></textarea>
                            <p class="description">
                                <?php _e('Format: "User: message" followed by "Assistant: response" on new lines.', 'q-knowledge-base'); ?>
                            </p>
                        </div>

                        <div class="qkb-form-actions">
                            <button type="button" id="qkb-estimate-tokens" class="button button-secondary">
                                <span class="dashicons dashicons-calculator"></span>
                                <?php _e('Estimate Tokens', 'q-knowledge-base'); ?>
                            </button>
                            <button type="button" id="qkb-preview-prompt" class="button button-secondary">
                                <span class="dashicons dashicons-visibility"></span>
                                <?php _e('Preview Prompt', 'q-knowledge-base'); ?>
                            </button>
                            <button type="button" id="qkb-test-prompt" class="button button-primary">
                                <span class="dashicons dashicons-controls-play"></span>
                                <?php _e('Test Prompt', 'q-knowledge-base'); ?>
                            </button>
                        </div>
                    </div>

                    <div class="qkb-token-estimation">
                        <h3><?php _e('Token Estimation', 'q-knowledge-base'); ?></h3>
                        <div class="qkb-token-stats">
                            <div class="qkb-token-stat">
                                <span class="qkb-token-label"><?php _e('System Message:', 'q-knowledge-base'); ?></span>
                                <span class="qkb-token-value" id="qkb-system-tokens">0</span>
                            </div>
                            <div class="qkb-token-stat">
                                <span class="qkb-token-label"><?php _e('User Message:', 'q-knowledge-base'); ?></span>
                                <span class="qkb-token-value" id="qkb-user-tokens">0</span>
                            </div>
                            <div class="qkb-token-stat">
                                <span class="qkb-token-label"><?php _e('KB Content:', 'q-knowledge-base'); ?></span>
                                <span class="qkb-token-value" id="qkb-kb-tokens">0</span>
                            </div>
                            <div class="qkb-token-stat">
                                <span class="qkb-token-label"><?php _e('History:', 'q-knowledge-base'); ?></span>
                                <span class="qkb-token-value" id="qkb-history-tokens">0</span>
                            </div>
                            <div class="qkb-token-stat qkb-token-total">
                                <span class="qkb-token-label"><?php _e('Total:', 'q-knowledge-base'); ?></span>
                                <span class="qkb-token-value" id="qkb-total-tokens">0</span>
                            </div>
                        </div>
                        <div class="qkb-token-warning" id="qkb-token-warning" style="display: none;">
                            <?php _e('Warning: This prompt is approaching the model\'s token limit.', 'q-knowledge-base'); ?>
                        </div>
                    </div>
                </div>

                <div class="qkb-prompt-tester-main">
                    <div class="qkb-prompt-preview-container">
                        <h2><?php _e('Prompt Preview', 'q-knowledge-base'); ?></h2>
                        <div class="qkb-prompt-preview">
                            <div class="qkb-prompt-section">
                                <h3><?php _e('System Message', 'q-knowledge-base'); ?></h3>
                                <div id="qkb-system-message-preview" class="qkb-prompt-content"></div>
                            </div>
                            <div class="qkb-prompt-section">
                                <h3><?php _e('User Message', 'q-knowledge-base'); ?></h3>
                                <div id="qkb-user-message-preview" class="qkb-prompt-content"></div>
                            </div>
                        </div>
                    </div>

                    <div class="qkb-response-container">
                        <h2><?php _e('AI Response', 'q-knowledge-base'); ?></h2>
                        <div id="qkb-response-output" class="qkb-response-output">
                            <div class="qkb-response-placeholder">
                                <?php _e('Test a prompt to see the AI response here.', 'q-knowledge-base'); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * AJAX handler for testing prompts
     */
    public function ajax_test_prompt()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_prompt_tester_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'q-knowledge-base')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have sufficient permissions', 'q-knowledge-base')]);
        }

        // Get parameters
        $query = sanitize_text_field($_POST['query'] ?? '');
        $assistant_id = intval($_POST['assistant_id'] ?? 0);
        $prompt_source = sanitize_text_field($_POST['prompt_source'] ?? 'system');
        $template_id = intval($_POST['template_id'] ?? 0);
        $custom_prompt = wp_kses_post($_POST['custom_prompt'] ?? '');
        $include_kb = isset($_POST['include_kb']) ? (bool) $_POST['include_kb'] : true;
        $include_history = isset($_POST['include_history']) ? (bool) $_POST['include_history'] : false;
        $conversation_history = sanitize_textarea_field($_POST['conversation_history'] ?? '');

        if (empty($query)) {
            wp_send_json_error(['message' => __('Query is required', 'q-knowledge-base')]);
        }

        try {
            // Get KB content if needed
            $kb_content = '';
            if ($include_kb) {
                $kb_result = $this->context_handler->get_kb_context($query, [
                    'assistant_id' => $assistant_id,
                    'include_sources' => true,
                    'format_results' => true
                ]);
                $kb_content = $kb_result['context'] ?? '';
            }

            // Parse conversation history if needed
            $history = [];
            if ($include_history && !empty($conversation_history)) {
                $history = $this->parse_conversation_history($conversation_history);
            }

            // Build messages array
            $messages = [];

            // Add system message
            if ($prompt_source === 'system') {
                // Use default system prompt
                $system_message = $this->prompts_handler->get_system_instructions($assistant_id);
                $messages[] = $system_message;
            } elseif ($prompt_source === 'template' && $template_id > 0) {
                // Use template
                $template = $this->templates_handler->get_template($template_id);
                if ($template && $template['type'] === 'system') {
                    $template_content = $this->templates_handler->process_template($template['content'], [
                        'kb_content' => $kb_content,
                        'user_query' => $query,
                        'assistant_name' => $this->get_assistant_name($assistant_id)
                    ]);
                    $messages[] = [
                        'role' => 'system',
                        'content' => $template_content
                    ];
                }
            } elseif ($prompt_source === 'custom' && !empty($custom_prompt)) {
                // Use custom prompt
                $processed_prompt = $this->templates_handler->process_template($custom_prompt, [
                    'kb_content' => $kb_content,
                    'user_query' => $query,
                    'assistant_name' => $this->get_assistant_name($assistant_id)
                ]);
                $messages[] = [
                    'role' => 'system',
                    'content' => $processed_prompt
                ];
            }

            // Add conversation history
            foreach ($history as $message) {
                $messages[] = $message;
            }

            // Add user query
            if ($prompt_source === 'template' && $template_id > 0) {
                $template = $this->templates_handler->get_template($template_id);
                if ($template && $template['type'] === 'user') {
                    $template_content = $this->templates_handler->process_template($template['content'], [
                        'kb_content' => $kb_content,
                        'user_query' => $query,
                        'assistant_name' => $this->get_assistant_name($assistant_id)
                    ]);
                    $messages[] = [
                        'role' => 'user',
                        'content' => $template_content
                    ];
                } else {
                    $messages[] = [
                        'role' => 'user',
                        'content' => $query
                    ];
                }
            } else {
                $messages[] = [
                    'role' => 'user',
                    'content' => $query
                ];
            }

            // Get completion
            $completion = $this->openai_handler->get_completion($query, $kb_content, $assistant_id);

            if (is_wp_error($completion)) {
                wp_send_json_error([
                    'message' => $completion->get_error_message()
                ]);
            }

            // Format the preview messages for display
            $system_preview = '';
            $user_preview = '';

            foreach ($messages as $message) {
                if ($message['role'] === 'system') {
                    $system_preview = $message['content'];
                } elseif ($message['role'] === 'user' && empty($user_preview)) {
                    $user_preview = $message['content'];
                }
            }

            wp_send_json_success([
                'response' => $completion,
                'preview' => [
                    'system' => $system_preview,
                    'user' => $user_preview
                ],
                'messages' => $messages
            ]);

        } catch (Exception $e) {
            wp_send_json_error([
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * AJAX handler for estimating token usage
     */
    public function ajax_estimate_tokens()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_prompt_tester_nonce')) {
            wp_send_json_error(['message' => __('Security check failed', 'q-knowledge-base')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have sufficient permissions', 'q-knowledge-base')]);
        }

        // Get parameters
        $query = sanitize_text_field($_POST['query'] ?? '');
        $assistant_id = intval($_POST['assistant_id'] ?? 0);
        $prompt_source = sanitize_text_field($_POST['prompt_source'] ?? 'system');
        $template_id = intval($_POST['template_id'] ?? 0);
        $custom_prompt = wp_kses_post($_POST['custom_prompt'] ?? '');
        $include_kb = isset($_POST['include_kb']) ? (bool) $_POST['include_kb'] : true;
        $include_history = isset($_POST['include_history']) ? (bool) $_POST['include_history'] : false;
        $conversation_history = sanitize_textarea_field($_POST['conversation_history'] ?? '');

        // Get KB content if needed
        $kb_content = '';
        if ($include_kb && !empty($query)) {
            $kb_result = $this->context_handler->get_kb_context($query, [
                'assistant_id' => $assistant_id,
                'include_sources' => true,
                'format_results' => true
            ]);
            $kb_content = $kb_result['context'] ?? '';
        }

        // Parse conversation history if needed
        $history = [];
        if ($include_history && !empty($conversation_history)) {
            $history = $this->parse_conversation_history($conversation_history);
        }

        // Get system message
        $system_message = '';
        if ($prompt_source === 'system') {
            // Use default system prompt
            $system_instruction = $this->prompts_handler->get_system_instructions($assistant_id);
            $system_message = $system_instruction['content'] ?? '';
        } elseif ($prompt_source === 'template' && $template_id > 0) {
            // Use template
            $template = $this->templates_handler->get_template($template_id);
            if ($template && $template['type'] === 'system') {
                $system_message = $this->templates_handler->process_template($template['content'], [
                    'kb_content' => $kb_content,
                    'user_query' => $query,
                    'assistant_name' => $this->get_assistant_name($assistant_id)
                ]);
            }
        } elseif ($prompt_source === 'custom' && !empty($custom_prompt)) {
            // Use custom prompt
            $system_message = $this->templates_handler->process_template($custom_prompt, [
                'kb_content' => $kb_content,
                'user_query' => $query,
                'assistant_name' => $this->get_assistant_name($assistant_id)
            ]);
        }

        // Get user message
        $user_message = $query;
        if ($prompt_source === 'template' && $template_id > 0) {
            $template = $this->templates_handler->get_template($template_id);
            if ($template && $template['type'] === 'user') {
                $user_message = $this->templates_handler->process_template($template['content'], [
                    'kb_content' => $kb_content,
                    'user_query' => $query,
                    'assistant_name' => $this->get_assistant_name($assistant_id)
                ]);
            }
        }

        // Calculate token estimates
        $system_tokens = $this->estimate_token_count($system_message);
        $user_tokens = $this->estimate_token_count($user_message);
        $kb_tokens = $this->estimate_token_count($kb_content);
        $history_tokens = 0;

        foreach ($history as $message) {
            $history_tokens += $this->estimate_token_count($message['content']);
        }

        $total_tokens = $system_tokens + $user_tokens + $kb_tokens + $history_tokens;
        $model = get_option('qkb_openai_model', 'gpt-4o');
        $model_token_limit = $this->get_model_token_limit($model);
        $is_warning = ($total_tokens > $model_token_limit * 0.7);

        wp_send_json_success([
            'system_tokens' => $system_tokens,
            'user_tokens' => $user_tokens,
            'kb_tokens' => $kb_tokens,
            'history_tokens' => $history_tokens,
            'total_tokens' => $total_tokens,
            'model_limit' => $model_token_limit,
            'warning' => $is_warning
        ]);
    }

    /**
     * Parse conversation history from text format
     *
     * @param string $history_text Text with "User:" and "Assistant:" prefixes
     * @return array Array of message objects
     */
    private function parse_conversation_history($history_text)
    {
        $messages = [];
        $lines = explode("\n", $history_text);
        $current_role = null;
        $current_content = '';

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) {
                continue;
            }

            if (preg_match('/^User:\s*(.*)/i', $line, $matches)) {
                // Save previous message if exists
                if ($current_role && !empty($current_content)) {
                    $messages[] = [
                        'role' => $current_role,
                        'content' => trim($current_content)
                    ];
                }

                $current_role = 'user';
                $current_content = $matches[1];
            } elseif (preg_match('/^Assistant:\s*(.*)/i', $line, $matches)) {
                // Save previous message if exists
                if ($current_role && !empty($current_content)) {
                    $messages[] = [
                        'role' => $current_role,
                        'content' => trim($current_content)
                    ];
                }

                $current_role = 'assistant';
                $current_content = $matches[1];
            } else {
                // Continue previous message
                if ($current_role) {
                    $current_content .= "\n" . $line;
                }
            }
        }

        // Add the last message
        if ($current_role && !empty($current_content)) {
            $messages[] = [
                'role' => $current_role,
                'content' => trim($current_content)
            ];
        }

        return $messages;
    }

    /**
     * Get assistant name by ID
     *
     * @param int $assistant_id Assistant ID
     * @return string Assistant name or default
     */
    private function get_assistant_name($assistant_id)
    {
        if (!$assistant_id) {
            return 'AI Assistant';
        }

        $assistant = get_term($assistant_id, 'kb_assistant');
        if (is_wp_error($assistant) || !$assistant) {
            return 'AI Assistant';
        }

        // Handle both object and array formats for assistant
        return is_object($assistant) ? $assistant->name : (isset($assistant['name']) ? $assistant['name'] : 'AI Assistant');
    }

    /**
     * Estimate token count for a text string
     *
     * @param string $text Text to estimate tokens for
     * @return int Estimated token count
     */
    private function estimate_token_count($text)
    {
        if (empty($text)) {
            return 0;
        }

        // A very rough estimate: 1 token ≈ 4 characters or 0.75 words
        $char_count = mb_strlen($text);
        return (int) ceil($char_count / 4);
    }

    /**
     * Get token limit for a specific model
     *
     * @param string $model Model name
     * @return int Token limit
     */
    private function get_model_token_limit($model)
    {
        $limits = [
            'gpt-3.5-turbo' => 4096,
            'gpt-4' => 8192,
            'gpt-4-turbo' => 128000,
            'gpt-4o' => 128000
        ];

        return $limits[$model] ?? 4096;
    }
}

// Initialize the prompt tester
add_action('plugins_loaded', function () {
    new QKB_OpenAI_Prompt_Tester();
});
