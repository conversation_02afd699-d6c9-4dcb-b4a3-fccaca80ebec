jQuery(document).ready(function($) {
    // Handle form submission to ensure TinyMCE content is updated
    $('form').on('submit', function() {
        if (typeof tinyMCE !== 'undefined') {
            tinyMCE.triggerSave();
        }
    });

    // Media upload for chatbot icon
    $('.qkb-media-upload').on('click', function(e) {
        e.preventDefault();
        var button = $(this);
        var customUploader = wp.media({
            title: 'Choose Chatbot Icon',
            library: {
                type: 'image'
            },
            button: {
                text: 'Use this icon'
            },
            multiple: false
        }).on('select', function() {
            var attachment = customUploader.state().get('selection').first().toJSON();
            button.siblings('input[type="url"]').val(attachment.url);
            button.siblings('.qkb-icon-preview').find('img').attr('src', attachment.url);
        }).open();
    });

    // Media upload for chat interface icon
    $('.qkb-interface-media-upload').on('click', function(e) {
        e.preventDefault();
        var button = $(this);
        var customUploader = wp.media({
            title: 'Choose Interface Icon',
            library: {
                type: 'image'
            },
            button: {
                text: 'Use this icon'
            },
            multiple: false
        }).on('select', function() {
            var attachment = customUploader.state().get('selection').first().toJSON();
            button.siblings('input[type="url"]').val(attachment.url);
            button.siblings('.qkb-interface-icon-preview').find('img').attr('src', attachment.url);
        }).open();
    });
});