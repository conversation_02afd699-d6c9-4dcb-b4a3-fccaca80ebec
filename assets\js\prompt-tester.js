/**
 * Prompt Tester JavaScript
 */
(function($) {
    'use strict';

    // Initialize CodeMirror editors
    let systemEditor = null;
    let userEditor = null;
    let responseEditor = null;

    $(document).ready(function() {
        // Initialize CodeMirror for custom prompt
        if ($('#qkb-custom-prompt-text').length) {
            CodeMirror.fromTextArea(document.getElementById('qkb-custom-prompt-text'), {
                mode: 'markdown',
                lineNumbers: true,
                lineWrapping: true
            });
        }

        // Initialize preview editors
        if ($('#qkb-system-message-preview').length) {
            systemEditor = CodeMirror(document.getElementById('qkb-system-message-preview'), {
                mode: 'markdown',
                lineNumbers: true,
                lineWrapping: true,
                readOnly: true
            });
        }

        if ($('#qkb-user-message-preview').length) {
            userEditor = CodeMirror(document.getElementById('qkb-user-message-preview'), {
                mode: 'markdown',
                lineNumbers: true,
                lineWrapping: true,
                readOnly: true
            });
        }

        if ($('#qkb-response-output').length) {
            // Clear placeholder
            $('#qkb-response-output').empty();

            responseEditor = CodeMirror(document.getElementById('qkb-response-output'), {
                mode: 'markdown',
                lineNumbers: true,
                lineWrapping: true,
                readOnly: true
            });
        }

        // Handle prompt source change
        $('#qkb-prompt-source').on('change', function() {
            const source = $(this).val();

            if (source === 'template') {
                $('.qkb-template-selector').show();
                $('.qkb-custom-prompt').hide();
            } else if (source === 'custom') {
                $('.qkb-template-selector').hide();
                $('.qkb-custom-prompt').show();
            } else {
                $('.qkb-template-selector').hide();
                $('.qkb-custom-prompt').hide();
            }
        });

        // Handle conversation history checkbox
        $('#qkb-include-conversation-history').on('change', function() {
            if ($(this).is(':checked')) {
                $('.qkb-conversation-history').show();
            } else {
                $('.qkb-conversation-history').hide();
            }
        });

        // Preview prompt button
        $('#qkb-preview-prompt').on('click', function() {
            previewPrompt();
        });

        // Estimate tokens button
        $('#qkb-estimate-tokens').on('click', function() {
            estimateTokens();
        });

        // Test prompt button
        $('#qkb-test-prompt').on('click', function() {
            testPrompt();
        });
    });

    /**
     * Preview the prompt
     */
    function previewPrompt() {
        const data = getFormData();

        // Show loading state
        $('#qkb-preview-prompt').html('<span class="dashicons dashicons-update"></span> ' + qkbPromptTester.estimatingTokens).prop('disabled', true);

        $.ajax({
            url: qkbPromptTester.ajaxUrl,
            type: 'POST',
            data: {
                action: 'qkb_estimate_tokens',
                nonce: qkbPromptTester.nonce,
                query: data.query,
                assistant_id: data.assistantId,
                prompt_source: data.promptSource,
                template_id: data.templateId,
                custom_prompt: data.customPrompt,
                include_kb: data.includeKb,
                include_history: data.includeHistory,
                conversation_history: data.conversationHistory
            },
            success: function(response) {
                if (response.success) {
                    // Update token counts
                    updateTokenCounts(response.data);

                    // Get the preview content
                    $.ajax({
                        url: qkbPromptTester.ajaxUrl,
                        type: 'POST',
                        data: {
                            action: 'qkb_test_prompt',
                            nonce: qkbPromptTester.nonce,
                            query: data.query,
                            assistant_id: data.assistantId,
                            prompt_source: data.promptSource,
                            template_id: data.templateId,
                            custom_prompt: data.customPrompt,
                            include_kb: data.includeKb,
                            include_history: data.includeHistory,
                            conversation_history: data.conversationHistory,
                            preview_only: true
                        },
                        success: function(previewResponse) {
                            if (previewResponse.success) {
                                // Update preview editors
                                systemEditor.setValue(previewResponse.data.preview.system || '');
                                userEditor.setValue(previewResponse.data.preview.user || '');
                            } else {
                                alert(previewResponse.data.message || qkbPromptTester.errorMessage);
                            }

                            // Reset button
                            $('#qkb-preview-prompt').html('<span class="dashicons dashicons-visibility"></span> Preview Prompt').prop('disabled', false);
                        },
                        error: function() {
                            alert(qkbPromptTester.errorMessage);
                            $('#qkb-preview-prompt').html('<span class="dashicons dashicons-visibility"></span> Preview Prompt').prop('disabled', false);
                        }
                    });
                } else {
                    alert(response.data.message || qkbPromptTester.errorMessage);
                    $('#qkb-preview-prompt').html('<span class="dashicons dashicons-visibility"></span> Preview Prompt').prop('disabled', false);
                }
            },
            error: function() {
                alert(qkbPromptTester.errorMessage);
                $('#qkb-preview-prompt').html('<span class="dashicons dashicons-visibility"></span> Preview Prompt').prop('disabled', false);
            }
        });
    }

    /**
     * Estimate token usage
     */
    function estimateTokens() {
        const data = getFormData();

        // Show loading state
        $('#qkb-estimate-tokens').html('<span class="dashicons dashicons-update"></span> ' + qkbPromptTester.estimatingTokens).prop('disabled', true);

        $.ajax({
            url: qkbPromptTester.ajaxUrl,
            type: 'POST',
            data: {
                action: 'qkb_estimate_tokens',
                nonce: qkbPromptTester.nonce,
                query: data.query,
                assistant_id: data.assistantId,
                prompt_source: data.promptSource,
                template_id: data.templateId,
                custom_prompt: data.customPrompt,
                include_kb: data.includeKb,
                include_history: data.includeHistory,
                conversation_history: data.conversationHistory
            },
            success: function(response) {
                if (response.success) {
                    updateTokenCounts(response.data);
                } else {
                    alert(response.data.message || qkbPromptTester.errorMessage);
                }

                // Reset button
                $('#qkb-estimate-tokens').html('<span class="dashicons dashicons-calculator"></span> Estimate Tokens').prop('disabled', false);
            },
            error: function() {
                alert(qkbPromptTester.errorMessage);
                $('#qkb-estimate-tokens').html('<span class="dashicons dashicons-calculator"></span> Estimate Tokens').prop('disabled', false);
            }
        });
    }

    /**
     * Test the prompt with OpenAI
     */
    function testPrompt() {
        const data = getFormData();

        if (!data.query) {
            alert('Please enter a test query.');
            return;
        }

        // Show loading state
        $('#qkb-test-prompt').html('<span class="dashicons dashicons-update"></span> ' + qkbPromptTester.testingPrompt).prop('disabled', true);
        responseEditor.setValue('Loading...');

        // First preview the prompt
        previewPrompt();

        // Then test it
        $.ajax({
            url: qkbPromptTester.ajaxUrl,
            type: 'POST',
            data: {
                action: 'qkb_test_prompt',
                nonce: qkbPromptTester.nonce,
                query: data.query,
                assistant_id: data.assistantId,
                prompt_source: data.promptSource,
                template_id: data.templateId,
                custom_prompt: data.customPrompt,
                include_kb: data.includeKb,
                include_history: data.includeHistory,
                conversation_history: data.conversationHistory
            },
            success: function(response) {
                if (response.success) {
                    // Update response editor
                    responseEditor.setValue(response.data.response || '');
                } else {
                    responseEditor.setValue('Error: ' + (response.data.message || qkbPromptTester.errorMessage));
                }

                // Reset button
                $('#qkb-test-prompt').html('<span class="dashicons dashicons-controls-play"></span> Test Prompt').prop('disabled', false);
            },
            error: function() {
                responseEditor.setValue('Error: ' + qkbPromptTester.errorMessage);
                $('#qkb-test-prompt').html('<span class="dashicons dashicons-controls-play"></span> Test Prompt').prop('disabled', false);
            }
        });
    }

    /**
     * Update token count display
     */
    function updateTokenCounts(data) {
        $('#qkb-system-tokens').text(data.system_tokens);
        $('#qkb-user-tokens').text(data.user_tokens);
        $('#qkb-kb-tokens').text(data.kb_tokens);
        $('#qkb-history-tokens').text(data.history_tokens);
        $('#qkb-total-tokens').text(data.total_tokens);

        if (data.warning) {
            $('#qkb-token-warning').show();
        } else {
            $('#qkb-token-warning').hide();
        }
    }

    /**
     * Get form data
     */
    function getFormData() {
        return {
            query: $('#qkb-test-query').val(),
            assistantId: $('#qkb-test-assistant').val(),
            promptSource: $('#qkb-prompt-source').val(),
            templateId: $('#qkb-prompt-template').val(),
            customPrompt: $('#qkb-custom-prompt-text').val(),
            includeKb: $('#qkb-include-kb-content').is(':checked'),
            includeHistory: $('#qkb-include-conversation-history').is(':checked'),
            conversationHistory: $('#qkb-conversation-history-text').val()
        };
    }

})(jQuery);
