<?php
/**
 * Fix Feedback Table Script
 * 
 * This script directly creates the feedback table with the correct structure.
 */

// Load WordPress
require_once dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';

// Get the database connection
global $wpdb;

// Define the table name
$table_name = $wpdb->prefix . 'qkb_ml_interactions';

// Drop the table if it exists
echo "Dropping existing table if it exists...\n";
$wpdb->query("DROP TABLE IF EXISTS $table_name");

// Create the table with the correct structure
echo "Creating new table with correct structure...\n";
$charset_collate = $wpdb->get_charset_collate();

$sql = "CREATE TABLE $table_name (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    message_id varchar(255) NOT NULL,
    query text NOT NULL,
    response text NOT NULL,
    feedback int(11) NOT NULL,
    assistant_id bigint(20) NOT NULL,
    created_at datetime NOT NULL,
    PRIMARY KEY  (id),
    <PERSON><PERSON><PERSON> assistant_id (assistant_id),
    <PERSON><PERSON><PERSON> created_at (created_at)
) $charset_collate;";

require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
$result = dbDelta($sql);

// Check if table was created successfully
if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name) {
    echo "SUCCESS: Feedback table created successfully.\n";
    
    // Insert a test record
    echo "Inserting test record...\n";
    $result = $wpdb->insert(
        $table_name,
        [
            'message_id' => 'test_' . time(),
            'query' => 'This is a test query',
            'response' => 'This is a test response',
            'feedback' => 1,
            'assistant_id' => 1,
            'created_at' => current_time('mysql')
        ],
        ['%s', '%s', '%s', '%d', '%d', '%s']
    );
    
    if ($result) {
        echo "SUCCESS: Test record inserted successfully.\n";
    } else {
        echo "ERROR: Failed to insert test record. DB Error: " . $wpdb->last_error . "\n";
    }
} else {
    echo "ERROR: Failed to create feedback table.\n";
}

echo "Done.\n";
