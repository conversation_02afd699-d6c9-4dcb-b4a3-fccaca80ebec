.qkb-settings-apps fieldset {
    margin: 0;
    padding: 8px 0;
}

.qkb-settings-apps label {
    display: block;
    margin-bottom: 8px;
    font-weight: normal;
}

.qkb-settings-apps input[type="checkbox"] {
    margin-right: 8px;
}

.qkb-settings-apps .description {
    margin-top: 8px;
    color: #666;
}

/* URL Repeater Styles */
.qkb-url-repeater {
    max-width: 600px;
}

.qkb-url-fields {
    margin-bottom: 12px;
}

.qkb-url-field {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.qkb-url-field input[type="url"] {
    flex: 1;
}

.qkb-url-field .qkb-remove-url {
    color: #dc3545;
    border-color: #dc3545;
}

.qkb-url-field .qkb-remove-url:hover {
    background: #dc3545;
    color: #fff;
}

.qkb-add-url {
    margin-bottom: 8px !important;
}

.qkb-assistant-roles {
    margin-bottom: 20px;
    padding: 15px;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
}

.qkb-assistant-roles strong {
    display: block;
    margin-bottom: 10px;
    color: #1d2327;
}

.qkb-assistant-roles label {
    display: inline-block;
    margin-right: 15px;
    margin-bottom: 5px;
}

.qkb-assistant-roles input[type="checkbox"] {
    margin-right: 5px;
}

.qkb-icon-preview,
.qkb-interface-icon-preview {
    display: inline-block;
    padding: 10px;
    background: #f0f0f1;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin-right: 10px;
}

.qkb-icon-preview img,
.qkb-interface-icon-preview img {
    display: block;
    max-width: 50px;
    height: auto;
}

.qkb-media-upload,
.qkb-interface-media-upload {
    margin-left: 5px !important;
    vertical-align: middle !important;
}

/* Add these styles at the end of the file */
.qkb-settings fieldset label {
    display: inline-block;
    margin-right: 20px;
    margin-bottom: 10px;
}

.qkb-settings fieldset input[type="radio"] {
    margin-right: 5px;
}

.qkb-settings fieldset .description {
    margin-top: 10px;
}

/* Quick Access Buttons Styles */
.qkb-quick-access-repeater {
    max-width: 800px;
    border: 1px solid #e2e4e7;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.qkb-quick-access-header-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    padding: 12px 15px;
    border-bottom: 1px solid #e2e4e7;
}

.qkb-quick-access-header-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 14px;
}

.qkb-quick-access-header-actions {
    display: flex;
    gap: 8px;
}

.qkb-quick-access-fields {
    margin-bottom: 0;
    padding: 15px;
    background: #fff;
    max-height: 600px;
    overflow-y: auto;
}

.qkb-quick-access-field {
    margin-bottom: 15px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.qkb-quick-access-field:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.qkb-quick-access-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #e2e4e7;
    cursor: pointer;
    background: #f0f0f1;
    border-radius: 8px 8px 0 0;
}

.qkb-quick-access-drag-handle {
    cursor: move;
    color: #666;
    margin-right: 10px;
    padding: 5px;
}

.qkb-quick-access-drag-handle:hover {
    color: #2271b1;
}

.qkb-quick-access-title {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    font-weight: 500;
}

.qkb-quick-access-actions {
    display: flex;
    gap: 5px;
}

.qkb-toggle-quick-access,
.qkb-remove-quick-access {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.qkb-toggle-quick-access:hover {
    background: #e0e0e0;
}

.qkb-remove-quick-access {
    color: #dc3545;
}

.qkb-remove-quick-access:hover {
    background: #dc3545;
    color: #fff;
}

.qkb-quick-access-content-wrapper {
    padding: 15px;
}

.qkb-quick-access-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.qkb-quick-access-icon,
.qkb-quick-access-name {
    flex: 1;
}

.qkb-icon-selector {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}

.qkb-icon-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 18px;
}

.qkb-quick-access-icon label,
.qkb-quick-access-name label,
.qkb-quick-access-content label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.qkb-quick-access-content {
    margin-bottom: 15px;
}

.qkb-quick-access-actions-bar {
    padding: 15px;
    background: #f8f9fa;
    border-top: 1px solid #e2e4e7;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.qkb-sortable-placeholder {
    border: 2px dashed #2271b1;
    background: rgba(34, 113, 177, 0.05);
    margin-bottom: 15px;
    border-radius: 8px;
    height: 50px;
}