/**
 * Prompt Template Generator JavaScript
 *
 * Handles the Generate Template Content button functionality
 */
(function($) {
    'use strict';

    // Initialize
    $(document).ready(function() {
        // Set up event listeners
        $('#generate-template-content').on('click', generateTemplateContent);
    });

    /**
     * Generate template content
     */
    function generateTemplateContent() {
        const $button = $(this);
        const $spinner = $button.siblings('.spinner');

        // Get form data
        const templateName = $('#template_name').val();
        const templateDescription = $('#template_description').val();
        const templateType = $('#template_type').val();
        const tone = $('#tone').val();
        const style = $('#style').val();
        const format = $('#format').val();
        const audience = $('#audience').val();
        const purpose = $('#purpose').val();
        const constraints = $('#constraints').val();

        // Validate required fields
        if (!templateName) {
            alert('Template Name is required');
            $('#template_name').focus();
            return;
        }

        if (!purpose) {
            alert('Purpose is required');
            $('#purpose').focus();
            return;
        }

        // Show loading state
        $button.prop('disabled', true).text(qkbPromptTemplateGenerator.generating);
        $spinner.addClass('is-active');

        // Add a temporary message above the template content
        const $templateContent = $('#template_content');
        const $message = $('<div class="notice notice-info inline"><p>Generating template content using AI. This may take a few moments...</p></div>');
        $templateContent.before($message);

        // Make AJAX request
        $.ajax({
            url: qkbPromptTemplateGenerator.ajaxUrl,
            type: 'POST',
            data: {
                action: 'qkb_generate_template_content',
                nonce: qkbPromptTemplateGenerator.nonce,
                template_name: templateName,
                template_description: templateDescription,
                template_type: templateType,
                tone: tone,
                style: style,
                format: format,
                audience: audience,
                purpose: purpose,
                constraints: constraints
            },
            success: function(response) {
                // Remove the temporary message
                $message.remove();

                if (response.success) {
                    // Set content in editor
                    $('#template_content').val(response.data.content);

                    // Add success message
                    const $successMessage = $('<div class="notice notice-success inline"><p>Template content generated successfully! Review and edit as needed before saving.</p></div>');
                    $templateContent.before($successMessage);

                    // Auto-scroll to the template content
                    $('html, body').animate({
                        scrollTop: $templateContent.offset().top - 100
                    }, 500);

                    // Focus on the template content
                    $templateContent.focus();

                    // Remove success message after 5 seconds
                    setTimeout(function() {
                        $successMessage.fadeOut(500, function() {
                            $(this).remove();
                        });
                    }, 5000);
                } else {
                    // Add error message
                    const errorMsg = response.data || qkbPromptTemplateGenerator.errorMessage;
                    const $errorMessage = $('<div class="notice notice-error inline"><p>' + errorMsg + '</p></div>');
                    $templateContent.before($errorMessage);

                    // Remove error message after 5 seconds
                    setTimeout(function() {
                        $errorMessage.fadeOut(500, function() {
                            $(this).remove();
                        });
                    }, 5000);
                }
            },
            error: function() {
                // Remove the temporary message
                $message.remove();

                // Add error message
                const $errorMessage = $('<div class="notice notice-error inline"><p>' + qkbPromptTemplateGenerator.errorMessage + '</p></div>');
                $templateContent.before($errorMessage);

                // Remove error message after 5 seconds
                setTimeout(function() {
                    $errorMessage.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 5000);
            },
            complete: function() {
                // Reset button state
                $button.prop('disabled', false).text(qkbPromptTemplateGenerator.generate);
                $spinner.removeClass('is-active');
            }
        });
    }

})(jQuery);
