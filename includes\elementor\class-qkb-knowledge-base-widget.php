<?php
if (!defined('ABSPATH')) {
    exit;
}

class QKB_Knowledge_Base_Widget extends \Elementor\Widget_Base
{
    public function get_name()
    {
        return 'qkb_knowledge_base';
    }

    public function get_title()
    {
        return esc_html__('Q Knowledge', 'q-knowledge-base');
    }

    public function get_icon()
    {
        return 'eicon-document-file';
    }

    public function get_categories()
    {
        return ['general'];
    }

    private function get_knowledge_base()         
    {
        $knowledge_base = [];
        
        // Get the knowledge-base assistant term
        $kb_assistant = get_term_by('slug', 'knowledge-base', 'kb_assistant');
        
        if (!$kb_assistant) {
            return $knowledge_base;
        }

        // Get posts with the knowledge-base assistant
        $posts = get_posts([
            'post_type' => 'kb_knowledge_base',
            'posts_per_page' => -1,
            'orderby' => 'title',
            'order' => 'ASC',
            'tax_query' => [
                [
                    'taxonomy' => 'kb_assistant',
                    'field' => 'term_id',
                    'terms' => $kb_assistant->term_id,
                ]
            ]
        ]);

        foreach ($posts as $post) {
            $knowledge_base[$post->ID] = $post->post_title;
        }

        return $knowledge_base;
    }

    protected function register_controls()
    {
        $this->start_controls_section(
            'content_section',
            [
                'label' => esc_html__('Content', 'q-knowledge-base'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'knowledge_base_id',
            [
                'label' => esc_html__('Select Knowledge Base', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::SELECT2,
                'options' => $this->get_knowledge_base(),
                'default' => '',
                'label_block' => true,
            ]
        );

        $this->add_control(
            'display_in_modal',
            [
                'label' => esc_html__('Display in Modal', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => esc_html__('Yes', 'q-knowledge-base'),
                'label_off' => esc_html__('No', 'q-knowledge-base'),
                'return_value' => 'yes',
                'default' => 'no',
            ]
        );

        $this->add_control(
            'modal_button_icon',
            [
                'label' => esc_html__('Button Icon', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::ICONS,
                'default' => [
                    'value' => 'fas fa-book',
                    'library' => 'fa-solid',
                ],
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
                'label_block' => true,
            ]
        );
        
        $this->add_control(
            'modal_button_text',
            [
                'label' => esc_html__('Modal Button Text', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => esc_html__('View Knowledge Base', 'q-knowledge-base'),
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
                'render_type' => 'template', 
            ]
        );

        $this->add_control(
            'modal_button_class',
            [
                'label' => esc_html__('Modal Button Class', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => '',
                'placeholder' => esc_html__('Enter custom class', 'q-knowledge-base'),
            ]
        );

        $this->add_control(
            'modal_title',
            [
                'label' => esc_html__('Modal Title', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => esc_html__('Knowledge Base', 'q-knowledge-base'),
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'footer_buttons_section',
            [
                'label' => esc_html__('Footer Buttons', 'q-knowledge-base'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        $repeater = new \Elementor\Repeater();

        $repeater->add_control(
            'button_text',
            [
                'label' => esc_html__('Button Text', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => esc_html__('Close', 'q-knowledge-base'),
            ]
        );

        $repeater->add_control(
            'button_action',
            [
                'label' => esc_html__('Button Action', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'options' => [
                    'close' => esc_html__('Close Modal', 'q-knowledge-base'),
                    'custom' => esc_html__('Custom Action', 'q-knowledge-base'),
                ],
                'default' => 'close',
            ]
        );

        $repeater->add_control(
            'button_url',
            [
                'label' => esc_html__('Button URL', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::URL,
                'placeholder' => esc_html__('https://your-link.com', 'q-knowledge-base'),
                'condition' => [
                    'button_action' => 'custom',
                ],
            ]
        );

        $repeater->add_control(
            'button_icon',
            [
                'label' => esc_html__('Button Icon', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::ICONS,
                'default' => [
                    'value' => 'fas fa-check',
                    'library' => 'fa-solid',
                ],
            ]
        );

        $repeater->add_control(
            'button_color',
            [
                'label' => esc_html__('Button Text Color', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
            ]
        );

        $repeater->add_control(
            'button_background_color',
            [
                'label' => esc_html__('Button Background Color', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#0073aa',
            ]
        );

        $this->add_control(
            'footer_buttons',
            [
                'label' => esc_html__('Footer Buttons', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::REPEATER,
                'fields' => $repeater->get_controls(),
                'default' => [],
            ]
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'style_section',
            [
                'label' => esc_html__('Style', 'q-knowledge-base'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'button_style_heading',
            [
                'label' => esc_html__('Modal Button', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'button_typography',
                'selector' => '{{WRAPPER}} .qkb-modal-button',
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'button_border_radius',
            [
                'label' => esc_html__('Border Radius', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .qkb-modal-button' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'button_border',
                'selector' => '{{WRAPPER}} .qkb-modal-button',
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'button_box_shadow',
                'selector' => '{{WRAPPER}} .qkb-modal-button',
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->start_controls_tabs('button_styles');

        $this->start_controls_tab(
            'button_normal',
            [
                'label' => esc_html__('Normal', 'q-knowledge-base'),
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'button_background_color',
            [
                'label' => esc_html__('Background Color', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .qkb-modal-button' => 'background-color: {{VALUE}};',
                ],
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'button_text_color',
            [
                'label' => esc_html__('Text Color', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .qkb-modal-button' => 'color: {{VALUE}};',
                    '{{WRAPPER}} .qkb-modal-button .qkb-modal-button-icon' => 'color: {{VALUE}};',
                    '{{WRAPPER}} .qkb-modal-button svg' => 'fill: {{VALUE}};',
                ],
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->end_controls_tab();

        $this->start_controls_tab(
            'button_hover',
            [
                'label' => esc_html__('Hover', 'q-knowledge-base'),
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'button_background_color_hover',
            [
                'label' => esc_html__('Background Color', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .qkb-modal-button:hover' => 'background-color: {{VALUE}};',
                ],
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'button_text_color_hover',
            [
                'label' => esc_html__('Text Color', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .qkb-modal-button:hover' => 'color: {{VALUE}};',
                    '{{WRAPPER}} .qkb-modal-button:hover .qkb-modal-button-icon' => 'color: {{VALUE}};',
                    '{{WRAPPER}} .qkb-modal-button:hover svg' => 'fill: {{VALUE}};',
                ],
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->end_controls_tab();

        $this->end_controls_tabs();

        $this->add_control(
            'modal_style_heading',
            [
                'label' => esc_html__('Modal', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'modal_background_color',
            [
                'label' => esc_html__('Background Color', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .qkb-modal-content' => 'background-color: {{VALUE}};',
                ],
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'modal_overlay_color',
            [
                'label' => esc_html__('Overlay Color', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .qkb-modal' => 'background-color: {{VALUE}};',
                ],
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->add_responsive_control(
            'modal_width',
            [
                'label' => esc_html__('Width', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', '%', 'vw'],
                'range' => [
                    'px' => [
                        'min' => 300,
                        'max' => 1200,
                    ],
                    '%' => [
                        'min' => 20,
                        'max' => 100,
                    ],
                    'vw' => [
                        'min' => 20,
                        'max' => 100,
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .qkb-modal-content' => 'width: {{SIZE}}{{UNIT}};',
                ],
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'modal_border_radius',
            [
                'label' => esc_html__('Border Radius', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .qkb-modal-content' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'condition' => [
                    'display_in_modal' => 'yes',
                ],
            ]
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'content_style_section',
            [
                'label' => esc_html__('Content', 'q-knowledge-base'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'content_typography',
                'selector' => '{{WRAPPER}} .qkb-knowledge-base-content',
            ]
        );

        $this->add_control(
            'content_text_color',
            [
                'label' => esc_html__('Text Color', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .qkb-knowledge-base-content' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'content_padding',
            [
                'label' => esc_html__('Padding', 'q-knowledge-base'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .qkb-knowledge-base-content' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();
    }

    protected function render()
    {
        $settings = $this->get_settings_for_display();

        if (empty($settings['knowledge_base_id'])) {
            return;
        }

        // Get the knowledge base post
        $post = get_post($settings['knowledge_base_id']);
        if (!$post || $post->post_type !== 'kb_knowledge_base') {
            return;
        }

        // Prepare the content
        $content = '';
        if (class_exists('\Elementor\Plugin')) {
            $document = \Elementor\Plugin::$instance->documents->get($post->ID);
            if ($document && $document->is_built_with_elementor()) {
                $content = \Elementor\Plugin::$instance->frontend->get_builder_content($post->ID, true);
            } else {
                $content = apply_filters('the_content', $post->post_content);
            }
        } else {
            $content = apply_filters('the_content', $post->post_content);
        }

        if ($settings['display_in_modal'] === 'yes') {
            $modal_id = 'qkb-modal-' . $settings['knowledge_base_id'];
            ?>
            <button class="qkb-modal-button <?php echo esc_attr($settings['modal_button_class']); ?>"
                aria-haspopup="dialog"
                aria-expanded="false"
                onclick="document.getElementById('<?php echo esc_attr($modal_id); ?>').style.display='block'; this.setAttribute('aria-expanded', 'true');">
                <?php 
                // Properly render the icon using Elementor's icon rendering
                if (!empty($settings['modal_button_icon']['value'])) {
                    \Elementor\Icons_Manager::render_icon($settings['modal_button_icon'], [
                        'aria-hidden' => 'true',
                        'class' => 'qkb-modal-button-icon'
                    ]);
                }
                ?>
                <span class="qkb-modal-button-text"><?php echo esc_html($settings['modal_button_text']); ?></span>
            </button>
            <div id="<?php echo esc_attr($modal_id); ?>" class="qkb-modal" role="dialog" aria-labelledby="modal-title" aria-modal="true" style="display: none;">
                <div class="qkb-modal-content qkb-animation-<?php echo esc_attr($settings['modal_animation'] ?? ''); ?>">
                    <div class="qkb-modal-header">
                        <h2 id="modal-title" class="qkb-modal-title">
                            <?php echo esc_html($settings['modal_title']); ?>
                        </h2>
                        <button type="button" 
                                class="qkb-modal-close" 
                                aria-label="<?php echo esc_attr__('Close modal', 'q-knowledge-base'); ?>"
                                onclick="this.closest('.qkb-modal').style.display='none'; document.querySelector('.qkb-modal-button').setAttribute('aria-expanded', 'false');">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="qkb-modal-body">
                        <div class="qkb-knowledge-base">
                            <div class="qkb-knowledge-base-content">
                                <div class="qkb-knowledge-base-body">
                                    <?php echo $content; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="qkb-modal-footer">
                        <?php
                        if (!empty($settings['footer_buttons'])) {
                            foreach ($settings['footer_buttons'] as $button) {
                                $button_text = $button['button_text'] ?? '';
                                $button_action = $button['button_action'] ?? '';
                                $button_url = $button['button_url']['url'] ?? '';
                                $button_icon = $button['button_icon'] ?? [];
                                $button_color = $button['button_color'] ?? '';
                                $button_background_color = $button['button_background_color'] ?? '';
                                ?>
                                <button class="qkb-modal-close-footer" 
                                    style="color: <?php echo esc_attr($button_color); ?>; background-color: <?php echo esc_attr($button_background_color); ?>;"
                                    onclick="<?php echo $button_action === 'close' ? "document.getElementById('" . esc_attr($modal_id) . "').style.display='none'; document.querySelector('.qkb-modal-close').style.display='none'; this.closest('.qkb-modal').style.display='none';" : "window.open('{$button_url}', '_blank');"; ?>">
                                    <?php if (!empty($button_icon['value'])): ?>
                                        <i class="<?php echo esc_attr($button_icon['value']); ?>" style="margin-right: 5px;"></i>
                                    <?php endif; ?>
                                    <?php echo esc_html($button_text); ?>
                                </button>
                                <?php
                            }
                        }
                        ?>
                    </div>
                </div>
            </div>
            <script>
                document.querySelector('.qkb-modal-button').onclick = function() {
                    document.getElementById('<?php echo esc_attr($modal_id); ?>').style.display = 'block';
                    this.setAttribute('aria-expanded', 'true');
                };

                document.getElementById('<?php echo esc_attr($modal_id); ?>').onclick = function(event) {
                    if (event.target === this) {
                        this.style.display = 'none';
                        document.querySelector('.qkb-modal-close').style.display = 'none';
                        document.querySelector('.qkb-modal-button').setAttribute('aria-expanded', 'false');
                    }
                };

                document.addEventListener('keydown', function(event) {
                    if (event.key === 'Escape') {
                        var modal = document.getElementById('<?php echo esc_attr($modal_id); ?>');
                        if (modal.style.display === 'block') {
                            modal.style.display = 'none';
                            document.querySelector('.qkb-modal-close').style.display = 'none';
                            document.querySelector('.qkb-modal-button').setAttribute('aria-expanded', 'false');
                        }
                    }
                });
            </script>
            <style>
                .qkb-modal-button {
                    background-color: var(--qkb-primary);
                    color: var(--qkb-bg);
                    border: none;
                    padding: var(--qkb-padding);
                    border-radius: var(--qkb-radius-sm);
                    cursor: pointer;
                    transition: all 0.3s ease;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                    line-height: 1;
                    gap: 8px;
                }

                .qkb-modal-button i,
                .qkb-modal-button svg {
                    width: 1em;
                    height: 1em;
                    fill: currentColor;
                    color: inherit;
                }

                .qkb-modal-button:hover {
                    background-color: var(--qkb-primary-dark);
                    opacity: 0.9;
                    box-shadow: var(--qkb-shadow);
                    transform: scale(1.05);
                }

                .qkb-modal-button-icon {
                    color: inherit;
                    fill: currentColor;
                }

                .qkb-modal {
                    display: none;
                    position: fixed;
                    z-index: 999999;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    overflow: auto;
                    background-color: rgba(0, 0, 0, 0.8);
                    backdrop-filter: blur(2px);
                    transition: opacity 0.3s ease;
                }

                .qkb-modal-content {
                    z-index: 1000000;
                    background-color: #fefefe;
                    border: 1px solid #888;
                    border-radius: 10px;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    box-sizing: border-box;
                    display: flex;
                    flex-direction: column;
                    width: 650px;
                    min-height: 600px;
                    max-width: 80%;
                    max-height: 80%;
                    overflow: hidden;
                    transition: transform 0.3s ease;
                }

                .qkb-modal-body {
                    flex-grow: 1;
                    overflow-y: auto;
                }

                .qkb-modal-footer {
                    padding: 10px;
                    text-align: right; 
                    background-color: #f1f1f1; 
                    border-top: 1px solid #ccc; 
                }

                .qkb-modal-close-footer {
                    background-color: var(--qkb-error) !important; 
                    color: #fff; 
                    border: none;
                    padding: var(--qkb-padding);
                    border-radius: 5px;
                    cursor: pointer;
                    opacity: 1;
                }

                .qkb-modal-close-footer:hover {
                    background-color: var(--qkb-error) !important;
                    opacity: 0.8; 
                    transform: var(--qkb-transform);
                }

                .qkb-animation-zoom-in {
                    animation: qkbZoomIn var(--animation-duration, 0.3s) ease-out;
                }

                @keyframes qkbZoomIn {
                    from {
                        opacity: 0;
                        transform: translate(-50%, -50%) scale(0.5);
                    }
                    to {
                        opacity: 1;
                        transform: translate(-50%, -50%) scale(1);
                    }
                }

                .qkb-modal-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: var(--qkb-padding);
                    background-color: var(--qkb-primary);
                    border-top-left-radius: var(--qkb-radius-sm);
                    border-top-right-radius: var(--qkb-radius-sm);
                }

                .qkb-modal-title {
                    margin: 0;
                    font-size: 1.25rem;
                    font-weight: bold;
                    color: var(--qkb-bg);
                    line-height: 1.5;
                    font-family: 'Inter', sans-serif;
                }

                .qkb-modal-close {
                    padding: 0;
                    background: transparent;
                    border: 0;
                    font-size: 1.5rem;
                    font-weight: 700;
                    line-height: 1;
                    color: var(--qkb-bg);
                    opacity: 0.5;
                    cursor: pointer;
                    transition: opacity .15s;
                }

                .qkb-modal-close:hover {
                    opacity: 1;
                    background: transparent;
                }

                .qkb-modal-close span {
                    display: block;
                    width: 35px;
                    height: 35px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .qkb-modal-button {
                    display: inline-flex;
                    align-items: center;
                    gap: 8px;
                }
                
                .qkb-modal-button .qkb-modal-button-icon {
                    margin-right: 8px;
                    line-height: 1;
                }
                
              
                
                .qkb-modal-button i,
                .qkb-modal-button svg {
                    width: 1em;
                    height: 1em;
                 
                }
            </style>
            <?php
        } else {
            ?>
            <div class="qkb-knowledge-base">
                <div class="qkb-knowledge-base-content">
                    <div class="qkb-knowledge-base-body">
                        <?php echo $content; ?>
                    </div>
                </div>
            </div>
            <?php
        }
    }

    protected function content_template()
    {
        ?>
        <div class="elementor-placeholder"><?php echo esc_html__('Knowledge Base will be displayed here', 'q-knowledge-base'); ?></div>
        <?php
    }
}