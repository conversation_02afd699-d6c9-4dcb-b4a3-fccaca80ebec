<?php
/**
 * AJAX handler for loading the chatbot
 */
function qkb_load_chatbot_ajax()
{
    check_ajax_referer('qkb_ajax_nonce', 'nonce');

    // Get the selected assistant for suggested prompts (same logic as main chatbot)
    $selected_assistant_id = get_option('qkb_default_chatbot_assistant', 0);
    if ($selected_assistant_id === 0) {
        // Get default assistant ID
        $default_assistant = get_term_by('slug', 'knowledge-base', 'kb_assistant');
        $selected_assistant_id = $default_assistant ? $default_assistant->term_id : 0;
    }

    $suggested_prompts = [];

    // Get the assistant term
    if ($selected_assistant_id > 0) {
        $assistant = get_term($selected_assistant_id, 'kb_assistant');
        if (!is_wp_error($assistant) && $assistant) {
            $suggested_prompts = get_term_meta($assistant->term_id, 'assistant_suggested_prompts', true);
        }
    }

    // If no suggested prompts found, try the default assistant as fallback
    if (empty($suggested_prompts) || !is_array($suggested_prompts)) {
        $default_assistant = get_term_by('slug', 'knowledge-base', 'kb_assistant');
        if ($default_assistant) {
            $suggested_prompts = get_term_meta($default_assistant->term_id, 'assistant_suggested_prompts', true);
        }
    }

    // If still no suggested prompts, use default ones
    if (empty($suggested_prompts) || !is_array($suggested_prompts)) {
        $suggested_prompts = [
            'What can you help me with?',
            'How do I get started?',
            'Tell me about your features'
        ];
    }

    // Render the chatbot and get the HTML
    ob_start();
    require_once QKB_PLUGIN_DIR . 'includes/chat/class-qkb-chatbot.php';
    $chatbot = new QKB_Chatbot();
    $chatbot->render_chatbot();
    $html = ob_get_clean();

    // Add the suggested prompts to the response
    $html .= '<script type="text/javascript">
        if (typeof qkbChatbot !== "undefined") {
            qkbChatbot.suggested_prompts = ' . json_encode($suggested_prompts) . ';
        }
    </script>';

    // Send the HTML as a JSON response
    wp_send_json_success($html);
}

// Hook the function to the AJAX action
add_action('wp_ajax_qkb_load_chatbot', 'qkb_load_chatbot_ajax');
add_action('wp_ajax_nopriv_qkb_load_chatbot', 'qkb_load_chatbot_ajax');
