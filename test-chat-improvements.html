<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Improvements Test</title>
    <link rel="stylesheet" href="assets/css/root-style.css">
    <link rel="stylesheet" href="assets/css/chatbot/qkb-chatbot.css">
    <link rel="stylesheet" href="assets/css/chat-interface/qi-message.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .test-input {
            width: 100%;
            min-height: 150px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            margin-bottom: 10px;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        .test-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        .test-btn-primary {
            background: #007cba;
            color: white;
        }
        .test-btn-primary:hover {
            background: #005a87;
        }
        .message-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #fafafa;
            min-height: 200px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Chat Response Formatting Test</h1>
        <p>This page tests the improved HTML handling and code output formatting for chat responses.</p>
        
        <div class="test-section">
            <div class="test-title">Test Input</div>
            <textarea class="test-input" id="test-input" placeholder="Enter test content with markdown, code blocks, etc.">Here's a test response with various formatting:

## Code Examples

Here's some JavaScript code:

```javascript
function formatMessage(content) {
    if (!content) return "";
    
    // Process code blocks
    content = content.replace(/```(\w+)?\s*\n?([\s\S]*?)```/g, (match, language, code) => {
        return `<div class="code-block">${code}</div>`;
    });
    
    return content;
}
```

And some PHP code:

```php
<?php
class ChatHandler {
    public function processMessage($message) {
        // Sanitize input
        $message = htmlspecialchars($message);
        return $message;
    }
}
?>
```

Some inline code: `console.log('Hello World')`

**Bold text** and *italic text*

- List item 1
- List item 2
- List item 3

> This is a blockquote

[Link example](https://example.com)</textarea>
            
            <div class="test-buttons">
                <button class="test-btn test-btn-primary" onclick="formatWithQKB()">Format with QKB Chatbot</button>
                <button class="test-btn test-btn-primary" onclick="formatWithQI()">Format with QI Chat</button>
                <button class="test-btn test-btn-primary" onclick="clearOutput()">Clear Output</button>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">QKB Chatbot Output</div>
            <div id="qkb-output" class="message-container qkb-message-content">
                <p>Click "Format with QKB Chatbot" to see the enhanced formatting in action.</p>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">QI Chat Output</div>
            <div id="qi-output" class="message-container qi-message-content">
                <p>Click "Format with QI Chat" to see the enhanced formatting in action.</p>
            </div>
        </div>
    </div>

    <script src="assets/js/qi-chat.js"></script>
    <script src="assets/js/chatbot.js"></script>
    <script>
        function formatWithQKB() {
            const input = document.getElementById('test-input').value;
            
            try {
                // Create a mock QKB chatbot instance
                const chatbot = {
                    formatMarkdown: function(content) {
                        // Use the actual formatMarkdown function from chatbot.js
                        const instance = new QKBChatbot();
                        return instance.formatMarkdown(content);
                    }
                };
                
                const formatted = chatbot.formatMarkdown(input);
                document.getElementById('qkb-output').innerHTML = formatted;
                
                // Apply syntax highlighting
                if (typeof Prism !== 'undefined') {
                    Prism.highlightAll();
                }
                
                // Add copy functionality
                addCopyFunctionality('#qkb-output');
                
            } catch (error) {
                console.error('QKB Formatting error:', error);
                document.getElementById('qkb-output').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
        
        function formatWithQI() {
            const input = document.getElementById('test-input').value;
            
            try {
                // Create a mock QI chat instance
                const chat = {
                    formatMessage: function(content) {
                        // Use the actual formatMessage function from qi-chat.js
                        const instance = new QIChat();
                        return instance.formatMessage(content);
                    }
                };
                
                const formatted = chat.formatMessage(input);
                document.getElementById('qi-output').innerHTML = formatted;
                
                // Apply syntax highlighting
                if (typeof Prism !== 'undefined') {
                    Prism.highlightAll();
                }
                
                // Add copy functionality
                addCopyFunctionality('#qi-output');
                
            } catch (error) {
                console.error('QI Formatting error:', error);
                document.getElementById('qi-output').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
        
        function clearOutput() {
            document.getElementById('qkb-output').innerHTML = '<p>Click "Format with QKB Chatbot" to see the enhanced formatting in action.</p>';
            document.getElementById('qi-output').innerHTML = '<p>Click "Format with QI Chat" to see the enhanced formatting in action.</p>';
        }
        
        function addCopyFunctionality(selector) {
            $(selector).find('.qkb-copy-code-btn, .qi-copy-code-btn').off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const $button = $(this);
                const $codeBlock = $button.closest('.qkb-code-block, .qi-code-block');
                const $code = $codeBlock.find('code');
                
                // Get the code text
                let codeText = $button.data('code') || $code.text();
                
                // Copy to clipboard
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(codeText).then(() => {
                        showCopySuccess($button);
                    }).catch(() => {
                        fallbackCopyToClipboard(codeText, $button);
                    });
                } else {
                    fallbackCopyToClipboard(codeText, $button);
                }
            });
        }
        
        function showCopySuccess($button) {
            const originalIcon = $button.find('i').attr('class');
            $button.find('i').attr('class', 'fas fa-check');
            $button.attr('title', 'Copied!');
            
            setTimeout(() => {
                $button.find('i').attr('class', originalIcon);
                $button.attr('title', 'Copy code');
            }, 2000);
        }
        
        function fallbackCopyToClipboard(text, $button) {
            try {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);
                
                if (successful) {
                    showCopySuccess($button);
                }
            } catch (error) {
                console.error('Fallback copy failed:', error);
            }
        }
        
        // Mock classes for testing
        class QKBChatbot {
            formatMarkdown(text) {
                // This would use the actual implementation from chatbot.js
                // For testing, we'll create a simplified version
                if (!text) return '';
                
                // Basic implementation for testing
                return text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                          .replace(/\*(.*?)\*/g, '<em>$1</em>')
                          .replace(/```(\w+)?\s*\n?([\s\S]*?)```/g, (match, language, code) => {
                              const lang = language || 'text';
                              const escapedCode = code.trim().replace(/</g, '&lt;').replace(/>/g, '&gt;');
                              return `<div class="qkb-code-block">
                                        <div class="qkb-code-header">
                                          <span class="qkb-code-language">${lang.toUpperCase()}</span>
                                          <button class="qkb-copy-code-btn" title="Copy code" data-code="${escapedCode}">
                                            <i class="fas fa-copy"></i>
                                          </button>
                                        </div>
                                        <pre><code class="language-${lang}">${escapedCode}</code></pre>
                                      </div>`;
                          });
            }
        }
        
        class QIChat {
            formatMessage(content) {
                // This would use the actual implementation from qi-chat.js
                // For testing, we'll create a simplified version
                if (!content) return '';
                
                // Basic implementation for testing
                return content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                             .replace(/\*(.*?)\*/g, '<em>$1</em>')
                             .replace(/```(\w+)?\s*\n?([\s\S]*?)```/g, (match, language, code) => {
                                 const lang = language || 'text';
                                 const escapedCode = code.trim().replace(/</g, '&lt;').replace(/>/g, '&gt;');
                                 return `<div class="qi-code-block">
                                           <div class="qi-code-header">
                                             <span class="qi-code-language">${lang.toUpperCase()}</span>
                                             <button class="qi-copy-code-btn" title="Copy code" data-code="${escapedCode}">
                                               <i class="fas fa-copy"></i>
                                             </button>
                                           </div>
                                           <pre><code class="language-${lang}">${escapedCode}</code></pre>
                                         </div>`;
                             });
            }
        }
    </script>
</body>
</html>
