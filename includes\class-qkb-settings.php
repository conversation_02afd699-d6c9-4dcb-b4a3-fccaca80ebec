<?php
if (!defined('ABSPATH')) {
    exit;
}

class QKB_Settings
{
    public function __construct()
    {

        if (!function_exists('add_action')) {
            require_once(ABSPATH . 'wp-includes/plugin.php');
        }

        add_action('admin_menu', [$this, 'add_settings_page']);
        add_action('admin_init', [$this, 'register_settings']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_editor']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_content_gaps_scripts']);

        add_action('wp_ajax_qkb_reset_appearance', [$this, 'handle_reset_appearance']);
        add_action('wp_ajax_qkb_create_gap_draft', [$this, 'handle_create_gap_draft']);
        add_action('wp_ajax_qkb_generate_gap_suggestion', [$this, 'handle_generate_gap_suggestion']);
    }

    public function add_settings_page()
    {
        if (!function_exists('add_submenu_page')) {
            require_once(ABSPATH . 'wp-admin/includes/admin.php');
        }

        add_submenu_page(
            'edit.php?post_type=kb_knowledge_base',
            __('Settings', 'q-knowledge-base'),
            __('Settings', 'q-knowledge-base'),
            'manage_options',
            'qkb-settings',
            [$this, 'render_settings_page']
        );

        // Add Content Gaps submenu
        add_submenu_page(
            'edit.php?post_type=kb_knowledge_base',
            __('Content Gaps', 'q-knowledge-base'),
            __('Content Gaps', 'q-knowledge-base'),
            'manage_options',
            'qkb-content-gaps',
            [$this, 'render_content_gaps_page']
        );
    }

    public function register_settings()
    {
        register_setting('qkb_settings', 'qkb_openai_api_key', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field',
        ]);
        register_setting('qkb_settings', 'qkb_openai_model', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'gpt-4o'
        ]);
        register_setting('qkb_settings', 'qkb_openai_max_tokens', [
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 2000
        ]);
        register_setting('qkb_settings', 'qkb_openai_temperature', [
            'type' => 'number',
            'sanitize_callback' => [$this, 'sanitize_float'],
            'default' => 0.7
        ]);
        register_setting('qkb_settings', 'qkb_openai_embedding_model', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'text-embedding-3-small'
        ]);
        register_setting('qkb_settings', 'qkb_openai_presence_penalty', [
            'type' => 'number',
            'sanitize_callback' => [$this, 'sanitize_float'],
            'default' => 0.1
        ]);
        register_setting('qkb_settings', 'qkb_openai_frequency_penalty', [
            'type' => 'number',
            'sanitize_callback' => [$this, 'sanitize_float'],
            'default' => 0.1
        ]);

        register_setting('qkb_settings', 'qkb_excluded_urls', [
            'type' => 'array',
            'default' => [],
            'sanitize_callback' => [$this, 'sanitize_excluded_urls']
        ]);
        // Deprecated - keeping for backward compatibility
        register_setting('qkb_settings', 'qkb_primary_color', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_hex_color',
            'default' => '#2271b1'
        ]);

        // Floating Chatbot primary color
        register_setting('qkb_settings', 'qkb_chatbot_primary_color', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_hex_color',
            'default' => '#2271b1'
        ]);

        // Chat Interface primary color
        register_setting('qkb_settings', 'qkb_chat_interface_primary_color', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_hex_color',
            'default' => '#2271b1'
        ]);
        register_setting('qkb_settings', 'qkb_external_urls', [
            'type' => 'array',
            'default' => [],
            'sanitize_callback' => [$this, 'sanitize_external_urls']
        ]);
        register_setting('qkb_settings', 'qkb_crawl_frequency', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'monthly'
        ]);
        register_setting('qkb_settings', 'qkb_assistant_roles', [
            'type' => 'array',
            'default' => [],
            'sanitize_callback' => [$this, 'sanitize_assistant_roles']
        ]);
        // Floating Chatbot Settings
        register_setting('qkb_settings', 'qkb_chatbot_name', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'Ask Q'
        ]);
        register_setting('qkb_settings', 'qkb_splash_subtitle', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'Your AI Assistant'
        ]);
        register_setting('qkb_settings', 'qkb_chatbot_icon', [
            'type' => 'string',
            'sanitize_callback' => 'esc_url_raw',
            'default' => QKB_PLUGIN_URL . 'assets/images/q.svg'
        ]);

        // Default assistant for the floating chatbot
        register_setting('qkb_settings', 'qkb_default_chatbot_assistant', [
            'type' => 'integer',
            'sanitize_callback' => 'absint',
            'default' => 0
        ]);

        // Quick Access Buttons for Floating Chatbot
        register_setting('qkb_settings', 'qkb_quick_access_buttons', [
            'type' => 'array',
            'default' => [],
            'sanitize_callback' => [$this, 'sanitize_quick_access_buttons']
        ]);

        // Chat Interface Settings
        register_setting('qkb_settings', 'qkb_chat_interface_name', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'Ask Q'
        ]);
        register_setting('qkb_settings', 'qkb_chat_interface_subtitle', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'Your AI Assistant'
        ]);
        register_setting('qkb_settings', 'qkb_chat_interface_icon', [
            'type' => 'string',
            'sanitize_callback' => 'esc_url_raw',
            'default' => QKB_PLUGIN_URL . 'assets/images/q.svg'
        ]);
        register_setting('qkb_settings', 'qkb_chat_interface_fullscreen_default', [
            'type' => 'boolean',
            'sanitize_callback' => 'rest_sanitize_boolean',
            'default' => false
        ]);
        register_setting('qkb_settings', 'qkb_enable_assistants', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'disabled'
        ]);
        register_setting('qkb_settings', 'qkb_sidebar_visibility', [
            'type' => 'array',
            'default' => [],
            'sanitize_callback' => [$this, 'sanitize_sidebar_visibility']
        ]);
        register_setting('qkb_settings', 'qkb_add_knowledge_roles', [
            'type' => 'array',
            'sanitize_callback' => [$this, 'sanitize_add_knowledge_roles']
        ]);
        register_setting('qkb_settings', 'qkb_export_button_roles', [
            'type' => 'array',
            'default' => [],
            'sanitize_callback' => [$this, 'sanitize_export_button_roles']
        ]);
        register_setting('qkb_settings', 'qkb_chatbot_visibility_roles', [
            'type' => 'array',
            'sanitize_callback' => [$this, 'sanitize_chatbot_visibility_roles'],
            'default' => []
        ]);
        register_setting('qkb_settings', 'qkb_response_style', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'balanced'
        ]);
        register_setting('qkb_settings', 'qkb_enable_followup', [
            'type' => 'boolean',
            'sanitize_callback' => 'rest_sanitize_boolean',
            'default' => true
        ]);
        register_setting('qkb_settings', 'qkb_global_system_prompt', [
            'type' => 'string',
            'sanitize_callback' => 'wp_kses_post',
            'default' => $this->get_default_system_prompt()
        ]);
        register_setting('qkb_settings', 'qkb_response_format', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'markdown'
        ]);
        register_setting('qkb_settings', 'qkb_citation_style', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'inline'
        ]);
        register_setting('qkb_settings', 'qkb_response_tone', [
            'type' => 'string',
            'sanitize_callback' => 'sanitize_text_field',
            'default' => 'balanced'
        ]);
        register_setting('qkb_settings', 'qkb_context_priority', [
            'type' => 'array',
            'sanitize_callback' => [$this, 'sanitize_context_priority'],
            'default' => [
                'kb_content' => 10,
                'similar_content' => 7,
                'conversation_history' => 5,
                'assistant_context' => 3,
                'user_context' => 2
            ]
        ]);
    }


    public function sanitize_excluded_urls($input)
    {

        if (is_null($input) || !is_array($input)) {
            return [];
        }

        $sanitized = array_filter(array_map(function ($url) {
            if (is_null($url) || !is_string($url)) {
                return '';
            }
            $url = trim($url);
            return !empty($url) ? untrailingslashit(esc_url_raw($url)) : '';
        }, $input));

        return array_values($sanitized);
    }

    public function sanitize_external_urls($input)
    {
        if (is_null($input) || !is_array($input)) {
            return [];
        }

        return array_filter(array_map(function ($url) {
            if (is_null($url) || !is_string($url)) {
                return '';
            }
            return esc_url_raw(trim($url));
        }, $input));
    }

    public function sanitize_assistant_roles($input)
    {
        if (!is_array($input)) {
            return [];
        }

        $sanitized = [];
        foreach ($input as $assistant_id => $roles) {
            $assistant_id = intval($assistant_id);
            if ($assistant_id > 0 && is_array($roles)) {
                $sanitized[$assistant_id] = array_map('sanitize_text_field', $roles);
            }
        }

        return $sanitized;
    }

    public function sanitize_sidebar_visibility($input)
    {
        if (!is_array($input)) {
            return [];
        }

        $roles = wp_roles()->get_names();
        $sanitized = [];

        foreach ($roles as $role_id => $role_name) {
            $sanitized[$role_id] = isset($input[$role_id]) ? 'show' : 'hide';
        }

        return $sanitized;
    }

    public function sanitize_add_knowledge_roles($input)
    {
        if (!is_array($input)) {
            return [];
        }

        return array_filter($input, function ($role) {
            return array_key_exists($role, wp_roles()->get_names());
        });
    }

    public function sanitize_export_button_roles($input)
    {
        if (!is_array($input)) {
            return [];
        }
        $valid_roles = wp_roles()->get_names();
        $sanitized = [];
        foreach ($input as $role) {
            if (isset($valid_roles[$role])) {
                $sanitized[] = sanitize_text_field($role);
            }
        }
        return $sanitized;
    }

    public function sanitize_chatbot_visibility_roles($input)
    {
        if (!is_array($input)) {
            return [];
        }

        $valid_roles = wp_roles()->get_names();
        $sanitized = [];

        foreach ($input as $role) {
            if (isset($valid_roles[$role])) {
                $sanitized[] = sanitize_text_field($role);
            }
        }

        return $sanitized;
    }

    public function sanitize_float($value)
    {
        return is_numeric($value) ? floatval($value) : 0.0;
    }

    public function sanitize_context_priority($input)
    {
        if (!is_array($input)) {
            return [
                'kb_content' => 10,
                'similar_content' => 7,
                'conversation_history' => 5,
                'assistant_context' => 3,
                'user_context' => 2
            ];
        }

        $default_priorities = [
            'kb_content' => 10,
            'similar_content' => 7,
            'conversation_history' => 5,
            'assistant_context' => 3,
            'user_context' => 2
        ];

        $sanitized = [];
        foreach ($default_priorities as $context_type => $default_priority) {
            $sanitized[$context_type] = isset($input[$context_type]) ?
                intval($input[$context_type]) : $default_priority;
        }

        return $sanitized;
    }

    /**
     * Sanitize quick access buttons
     *
     * @param array $input The input array to sanitize
     * @return array Sanitized array
     */
    public function sanitize_quick_access_buttons($input)
    {
        if (!is_array($input)) {
            return [];
        }

        $sanitized = [];

        // Define allowed HTML tags and attributes for wp_kses
        $allowed_html = wp_kses_allowed_html('post');

        // Add iframe support
        $allowed_html['iframe'] = array(
            'src' => array(),
            'height' => array(),
            'width' => array(),
            'frameborder' => array(),
            'allowfullscreen' => array(),
            'style' => array(),
            'loading' => array(),
            'scrolling' => array(),
            'title' => array(),
            'allow' => array(),
            'class' => array(),
            'id' => array(),
        );

        // Make sure shortcodes are preserved
        if (!isset($allowed_html['shortcode'])) {
            $allowed_html['shortcode'] = array();
        }

        foreach ($input as $button) {
            if (is_array($button) && isset($button['icon'], $button['name'], $button['content'])) {
                $sanitized[] = [
                    'icon' => sanitize_text_field($button['icon']),
                    'name' => sanitize_text_field($button['name']),
                    'content' => wp_kses($button['content'], $allowed_html), // Use custom allowed HTML
                    'show_in_modal' => isset($button['show_in_modal']) ? 1 : 0,
                    'modal_title' => isset($button['modal_title']) ? sanitize_text_field($button['modal_title']) : ''
                ];
            }
        }

        return $sanitized;
    }

    public function get_default_system_prompt()
    {
        return "You are Q, an intelligent AI assistant developed by Q-Ai specializing in knowledge base content delivery. You must strictly adhere to the following principles and response format:\n\n" .
            "Core Principles:\n" .
            "1. Only provide information from your specifically assigned knowledge base content\n" .
            "2. Present information in clear, well-structured formats\n" .
            "3. Focus responses on the specific query context\n" .
            "4. Maintain a helpful, professional tone\n" .
            "5. Always acknowledge when information is outside your assigned knowledge scope\n\n" .
            "Response Format:\n" .
            "1. Begin with a concise, direct answer to the query\n" .
            "2. Provide relevant details and context from the knowledge base\n" .
            "4. Limitations: Clearly state if the query cannot be fully answered with available knowledge\n\n" .
            "Technical Guidelines:\n" .
            "- Markdown\n" .
            "- Professional";
    }

    public function render_settings_page()
    {
        $excluded_urls = get_option('qkb_excluded_urls', []);
        $primary_color = get_option('qkb_primary_color', '#2271b1');
        ?>
        <div class="wrap">
            <h1>Settings</h1>



            <form method="post" action="options.php" class="qkb-settings-form">
                <?php
                settings_fields('qkb_settings');
                do_settings_sections('qkb_settings');
                ?>

                <!-- Settings Tabs Navigation -->
                <div class="qkb-settings-tabs">
                    <div class="qkb-settings-tab-nav">
                        <button type="button" class="qkb-settings-tab-button" data-tab="api">
                            <span class="dashicons dashicons-admin-generic qkb-tab-icon"></span> API Settings
                        </button>
                        <button type="button" class="qkb-settings-tab-button" data-tab="appearance">
                            <span class="dashicons dashicons-admin-comments qkb-tab-icon"></span> Chat
                        </button>
                        <button type="button" class="qkb-settings-tab-button" data-tab="response">
                            <span class="dashicons dashicons-format-chat qkb-tab-icon"></span> Response
                        </button>
                        <button type="button" class="qkb-settings-tab-button" data-tab="advanced">
                            <span class="dashicons dashicons-admin-tools qkb-tab-icon"></span> Advanced
                        </button>
                    </div>

                    <!-- API Settings Tab -->
                    <div class="qkb-settings-tab-pane" data-tab="api">
                        <div class="qkb-settings-card">
                            <h2 class="title">OpenAI API Settings</h2>
                            <table class="form-table">
                                <tr>
                                    <th scope="row">API Key</th>
                                    <td>
                                        <input type="password" name="qkb_openai_api_key"
                                            value="<?php echo esc_attr(get_option('qkb_openai_api_key')); ?>"
                                            class="regular-text">
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">OpenAI Model</th>
                                    <td>
                                        <select name="qkb_openai_model">
                                            <option value="gpt-3.5-turbo" <?php selected(get_option('qkb_openai_model'), 'gpt-3.5-turbo'); ?>>GPT-3.5 Turbo</option>
                                            <option value="gpt-4" <?php selected(get_option('qkb_openai_model'), 'gpt-4'); ?>>
                                                GPT-4
                                            </option>
                                            <option value="gpt-4-turbo" <?php selected(get_option('qkb_openai_model'), 'gpt-4-turbo'); ?>>
                                                GPT-4 Turbo</option>
                                            <option value="gpt-4o" <?php selected(get_option('qkb_openai_model'), 'gpt-4o'); ?>>
                                                GPT-4o
                                            </option>
                                        </select>
                                        <p class="description">Select the OpenAI model to use for chat completions</p>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Embedding Model</th>
                                    <td>
                                        <select name="qkb_openai_embedding_model">
                                            <option value="text-embedding-3-small" <?php selected(get_option('qkb_openai_embedding_model', 'text-embedding-3-small'), 'text-embedding-3-small'); ?>>text-embedding-3-small</option>
                                            <option value="text-embedding-3-large" <?php selected(get_option('qkb_openai_embedding_model', 'text-embedding-3-small'), 'text-embedding-3-large'); ?>>text-embedding-3-large</option>
                                            <option value="text-embedding-ada-002" <?php selected(get_option('qkb_openai_embedding_model', 'text-embedding-3-small'), 'text-embedding-ada-002'); ?>>text-embedding-ada-002 (Legacy)
                                            </option>
                                        </select>
                                        <p class="description">Model used for generating embeddings for knowledge base search
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="qkb-settings-card">
                            <h2 class="title">Advanced API Parameters</h2>
                            <p class="description">Configure advanced parameters for OpenAI API calls</p>
                            <table class="form-table">
                                <tr>
                                    <th scope="row">Max Tokens</th>
                                    <td>
                                        <input type="number" name="qkb_openai_max_tokens"
                                            value="<?php echo esc_attr(get_option('qkb_openai_max_tokens', 2000)); ?>"
                                            class="small-text" min="100" max="8000" step="100">
                                        <p class="description">Maximum number of tokens to generate in the response</p>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Temperature</th>
                                    <td>
                                        <input type="range" name="qkb_openai_temperature"
                                            value="<?php echo esc_attr(get_option('qkb_openai_temperature', 0.7)); ?>" min="0"
                                            max="1" step="0.1" class="qkb-range-slider" id="temperature-slider">
                                        <span class="qkb-range-value"
                                            id="temperature-value"><?php echo esc_attr(get_option('qkb_openai_temperature', 0.7)); ?></span>
                                        <p class="description">Controls randomness: 0 is deterministic, 1 is very creative</p>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Presence Penalty</th>
                                    <td>
                                        <input type="range" name="qkb_openai_presence_penalty"
                                            value="<?php echo esc_attr(get_option('qkb_openai_presence_penalty', 0.1)); ?>"
                                            min="-2" max="2" step="0.1" class="qkb-range-slider" id="presence-slider">
                                        <span class="qkb-range-value"
                                            id="presence-value"><?php echo esc_attr(get_option('qkb_openai_presence_penalty', 0.1)); ?></span>
                                        <p class="description">Positive values penalize new tokens based on whether they appear
                                            in
                                            the text
                                            so far</p>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Frequency Penalty</th>
                                    <td>
                                        <input type="range" name="qkb_openai_frequency_penalty"
                                            value="<?php echo esc_attr(get_option('qkb_openai_frequency_penalty', 0.1)); ?>"
                                            min="-2" max="2" step="0.1" class="qkb-range-slider" id="frequency-slider">
                                        <span class="qkb-range-value"
                                            id="frequency-value"><?php echo esc_attr(get_option('qkb_openai_frequency_penalty', 0.1)); ?></span>
                                        <p class="description">Positive values penalize new tokens based on their frequency in
                                            the
                                            text so
                                            far</p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Response Settings Tab -->
                    <div class="qkb-settings-tab-pane" data-tab="response">
                        <div class="qkb-settings-card">
                            <h2 class="title">Prompt Settings</h2>
                            <p class="description">Configure how the AI assistant responds to queries</p>
                            <table class="form-table">
                                <tr>
                                    <th scope="row">System Prompt</th>
                                    <td>
                                        <textarea name="qkb_global_system_prompt" rows="10" cols="50"
                                            class="large-text code"><?php echo esc_textarea(get_option('qkb_global_system_prompt', $this->get_default_system_prompt())); ?></textarea>
                                        <p class="description">The system prompt provides instructions to the AI on how to
                                            respond.
                                            <button type="button" class="button qkb-reset-prompt">Reset to Default</button>
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Response Format</th>
                                    <td>
                                        <select name="qkb_response_format">
                                            <option value="markdown" <?php selected(get_option('qkb_response_format', 'markdown'), 'markdown'); ?>>Markdown</option>
                                            <option value="html" <?php selected(get_option('qkb_response_format', 'markdown'), 'html'); ?>>
                                                HTML</option>
                                            <option value="text" <?php selected(get_option('qkb_response_format', 'markdown'), 'text'); ?>>
                                                Plain Text</option>
                                        </select>
                                        <p class="description">Format used for AI responses</p>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Citation Style</th>
                                    <td>
                                        <select name="qkb_citation_style">
                                            <option value="inline" <?php selected(get_option('qkb_citation_style', 'inline'), 'inline'); ?>>
                                                Inline Citations</option>
                                            <option value="footnote" <?php selected(get_option('qkb_citation_style', 'inline'), 'footnote'); ?>>Footnotes</option>
                                            <option value="endnotes" <?php selected(get_option('qkb_citation_style', 'inline'), 'endnotes'); ?>>Endnotes</option>
                                            <option value="none" <?php selected(get_option('qkb_citation_style', 'inline'), 'none'); ?>>No
                                                Citations</option>
                                        </select>
                                        <p class="description">How sources should be cited in responses</p>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Response Tone</th>
                                    <td>
                                        <select name="qkb_response_tone">
                                            <option value="formal" <?php selected(get_option('qkb_response_tone', 'balanced'), 'formal'); ?>>Formal</option>
                                            <option value="balanced" <?php selected(get_option('qkb_response_tone', 'balanced'), 'balanced'); ?>>Balanced</option>
                                            <option value="conversational" <?php selected(get_option('qkb_response_tone', 'balanced'), 'conversational'); ?>>Conversational</option>
                                            <option value="technical" <?php selected(get_option('qkb_response_tone', 'balanced'), 'technical'); ?>>Technical</option>
                                        </select>
                                        <p class="description">The tone used in AI responses</p>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Response Style</th>
                                    <td>
                                        <select name="qkb_response_style">
                                            <option value="concise" <?php selected(get_option('qkb_response_style'), 'concise'); ?>>
                                                <?php _e('Concise Answers', 'q-knowledge-base'); ?>
                                            </option>
                                            <option value="detailed" <?php selected(get_option('qkb_response_style'), 'detailed'); ?>>
                                                <?php _e('Detailed Explanations', 'q-knowledge-base'); ?>
                                            </option>
                                            <option value="balanced" <?php selected(get_option('qkb_response_style'), 'balanced'); ?>>
                                                <?php _e('Balanced', 'q-knowledge-base'); ?>
                                            </option>
                                        </select>
                                        <p class="description">
                                            <?php _e('Control how detailed the chatbot responses should be', 'q-knowledge-base'); ?>
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Follow-up Questions</th>
                                    <td>
                                        <label>
                                            <input type="checkbox" name="qkb_enable_followup" <?php checked(get_option('qkb_enable_followup', true)); ?>>
                                            <?php _e('Enable automatic follow-up questions', 'q-knowledge-base'); ?>
                                        </label>
                                        <p class="description">When enabled, the AI will suggest follow-up questions after each
                                            response</p>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="qkb-settings-card">
                            <h2 class="title">Context Management</h2>
                            <p class="description">Control how different types of context are prioritized in responses</p>
                            <table class="form-table">
                                <tr>
                                    <th scope="row">Context Priorities</th>
                                    <td>
                                        <?php
                                        $context_priorities = get_option('qkb_context_priority', [
                                            'kb_content' => 10,
                                            'similar_content' => 7,
                                            'conversation_history' => 5,
                                            'assistant_context' => 3,
                                            'user_context' => 2
                                        ]);

                                        $context_labels = [
                                            'kb_content' => 'Knowledge Base Content',
                                            'similar_content' => 'Similar Content',
                                            'conversation_history' => 'Conversation History',
                                            'assistant_context' => 'Assistant Context',
                                            'user_context' => 'User Context'
                                        ];

                                        foreach ($context_priorities as $context_type => $priority):
                                            ?>
                                            <div class="qkb-context-priority">
                                                <label>
                                                    <span
                                                        class="context-label"><?php echo esc_html($context_labels[$context_type]); ?></span>
                                                    <input type="range"
                                                        name="qkb_context_priority[<?php echo esc_attr($context_type); ?>]"
                                                        value="<?php echo esc_attr($priority); ?>" min="0" max="10" step="1"
                                                        class="qkb-range-slider context-slider"
                                                        id="<?php echo esc_attr($context_type); ?>-slider">
                                                    <span class="qkb-range-value"
                                                        id="<?php echo esc_attr($context_type); ?>-value"><?php echo esc_attr($priority); ?></span>
                                                </label>
                                            </div>
                                        <?php endforeach; ?>
                                        <p class="description">Higher values give more importance to that context type</p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Chat Settings Tab -->
                    <div class="qkb-settings-tab-pane" data-tab="appearance">
                        <!-- Chat Sub-Tabs -->
                        <div class="qkb-settings-subtabs">
                            <button type="button" class="qkb-settings-subtab active" data-subtab="chatbot">Floating
                                Chatbot</button>
                            <button type="button" class="qkb-settings-subtab" data-subtab="interface">Chat Interface</button>
                        </div>

                        <!-- Floating Chatbot Appearance Sub-Tab -->
                        <div class="qkb-settings-subtab-pane active" data-subtab="chatbot">
                            <div class="qkb-settings-card">
                                <h2 class="title">Floating Chatbot Appearance</h2>
                                <p class="description">These settings apply to the floating chatbot button and popup interface
                                </p>
                                <table class="form-table">
                                    <tr>
                                        <th scope="row">Primary Color</th>
                                        <td>
                                            <input type="color" name="qkb_chatbot_primary_color"
                                                value="<?php echo esc_attr(get_option('qkb_chatbot_primary_color', get_option('qkb_primary_color', '#2271b1'))); ?>"
                                                class="qkb-color-picker">
                                            <p class="description">Choose the primary color for the floating chatbot interface
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Chatbot Name</th>
                                        <td>
                                            <input type="text" name="qkb_chatbot_name"
                                                value="<?php echo esc_attr(get_option('qkb_chatbot_name', 'Ask Q')); ?>"
                                                class="regular-text">
                                            <p class="description">The name displayed in the floating chatbot interface</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Chatbot Subtitle</th>
                                        <td>
                                            <input type="text" name="qkb_splash_subtitle"
                                                value="<?php echo esc_attr(get_option('qkb_splash_subtitle', 'Your AI Assistant')); ?>"
                                                class="regular-text">
                                            <p class="description">The subtitle displayed in the floating chatbot interface</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Chatbot Icon</th>
                                        <td>
                                            <div class="qkb-icon-preview" style="margin-bottom: 10px;">
                                                <img src="<?php echo esc_url(get_option('qkb_chatbot_icon', QKB_PLUGIN_URL . 'assets/images/q.svg')); ?>"
                                                    alt="Chatbot Icon" style="max-width: 50px; height: auto;">
                                            </div>
                                            <input type="url" name="qkb_chatbot_icon"
                                                value="<?php echo esc_url(get_option('qkb_chatbot_icon', QKB_PLUGIN_URL . 'assets/images/q.svg')); ?>"
                                                class="regular-text">
                                            <button type="button" class="button qkb-media-upload">Choose Icon</button>
                                            <p class="description">URL to the floating chatbot's icon image (SVG recommended)
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Default Assistant</th>
                                        <td>
                                            <select name="qkb_default_chatbot_assistant">
                                                <?php
                                                $assistants = get_terms([
                                                    'taxonomy' => 'kb_assistant',
                                                    'hide_empty' => false
                                                ]);

                                                $default_assistant_id = get_option('qkb_default_chatbot_assistant', 0);
                                                if ($default_assistant_id === 0) {
                                                    // If no assistant is selected, use the default knowledge-base assistant
                                                    $default_assistant = get_term_by('slug', 'knowledge-base', 'kb_assistant');
                                                    if ($default_assistant) {
                                                        $default_assistant_id = $default_assistant->term_id;
                                                    }
                                                }

                                                // Add option for default assistant
                                                echo '<option value="0" ' . selected($default_assistant_id, 0, false) . '>Default Knowledge Base Assistant</option>';

                                                // Add all available assistants
                                                foreach ($assistants as $assistant) {
                                                    echo '<option value="' . esc_attr($assistant->term_id) . '" ' .
                                                        selected($default_assistant_id, $assistant->term_id, false) . '>' .
                                                        esc_html($assistant->name) . '</option>';
                                                }
                                                ?>
                                            </select>
                                            <p class="description">Select the default assistant for the floating chatbot</p>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div class="qkb-settings-card">
                                <h2 class="title">Quick Access Buttons</h2>
                                <p class="description">Configure quick access buttons that appear below the chatbot input area
                                </p>
                                <table class="form-table">
                                    <tr>
                                        <th scope="row">Quick Access Buttons</th>
                                        <td>
                                            <div class="qkb-quick-access-repeater">
                                                <div class="qkb-quick-access-fields">
                                                    <?php
                                                    $quick_access_buttons = get_option('qkb_quick_access_buttons', []);
                                                    if (!empty($quick_access_buttons)) {
                                                        foreach ($quick_access_buttons as $index => $button) {
                                                            ?>
                                                            <div class="qkb-quick-access-field">
                                                                <div class="qkb-quick-access-row">
                                                                    <div class="qkb-quick-access-icon">
                                                                        <label>Icon (FontAwesome)</label>
                                                                        <input type="text"
                                                                            name="qkb_quick_access_buttons[<?php echo $index; ?>][icon]"
                                                                            value="<?php echo esc_attr($button['icon']); ?>"
                                                                            class="regular-text" placeholder="fas fa-info-circle">
                                                                    </div>
                                                                    <div class="qkb-quick-access-name">
                                                                        <label>Button Name</label>
                                                                        <input type="text"
                                                                            name="qkb_quick_access_buttons[<?php echo $index; ?>][name]"
                                                                            value="<?php echo esc_attr($button['name']); ?>"
                                                                            class="regular-text" placeholder="Help">
                                                                    </div>
                                                                </div>
                                                                <div class="qkb-quick-access-content">
                                                                    <label>Content</label>
                                                                    <?php
                                                                    $content = isset($button['content']) ? $button['content'] : '';
                                                                    $editor_id = 'qkb_quick_access_content_' . $index;
                                                                    $settings = [
                                                                        'textarea_name' => "qkb_quick_access_buttons[$index][content]",
                                                                        'textarea_rows' => 5,
                                                                        'media_buttons' => false,
                                                                        'teeny' => true,
                                                                    ];
                                                                    wp_editor($content, $editor_id, $settings);
                                                                    ?>
                                                                </div>
                                                                <div class="qkb-quick-access-options">
                                                                    <label>
                                                                        <input type="checkbox"
                                                                            name="qkb_quick_access_buttons[<?php echo $index; ?>][show_in_modal]"
                                                                            value="1" class="qkb-modal-toggle" <?php checked(isset($button['show_in_modal']) && $button['show_in_modal']); ?>>
                                                                        Open in Modal
                                                                    </label>
                                                                </div>
                                                                <div class="qkb-quick-access-modal-title"
                                                                    style="<?php echo !isset($button['show_in_modal']) || !$button['show_in_modal'] ? 'display:none;' : ''; ?>">
                                                                    <label>Modal Title</label>
                                                                    <input type="text"
                                                                        name="qkb_quick_access_buttons[<?php echo $index; ?>][modal_title]"
                                                                        value="<?php echo isset($button['modal_title']) ? esc_attr($button['modal_title']) : ''; ?>"
                                                                        class="regular-text" placeholder="Enter modal title">
                                                                </div>
                                                                <button type="button"
                                                                    class="button qkb-remove-quick-access">Remove</button>
                                                            </div>
                                                            <?php
                                                        }
                                                    }
                                                    ?>
                                                </div>
                                                <button type="button" class="button qkb-add-quick-access">Add Quick Access
                                                    Button</button>
                                                <p class="description">Add quick access buttons that will appear below the
                                                    chatbot input area. Users can click these buttons to see the content in the
                                                    chat.</p>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div class="qkb-settings-card">
                                <h2 class="title">Visibility Settings</h2>
                                <p class="description">Configure where the chatbot should not appear</p>
                                <table class="form-table">
                                    <tr>
                                        <th scope="row">Excluded URLs</th>
                                        <td>
                                            <div class="qkb-url-repeater">
                                                <div class="qkb-url-fields">
                                                    <?php
                                                    $excluded_urls = get_option('qkb_excluded_urls', []);
                                                    if (!empty($excluded_urls)) {
                                                        foreach ($excluded_urls as $url) {
                                                            ?>
                                                            <div class="qkb-url-field">
                                                                <input type="url" name="qkb_excluded_urls[]"
                                                                    value="<?php echo esc_attr($url); ?>" class="regular-text"
                                                                    placeholder="https://example.com/page">
                                                                <button type="button" class="button qkb-remove-url">Remove</button>
                                                            </div>
                                                            <?php
                                                        }
                                                    }
                                                    ?>
                                                </div>
                                                <button type="button" class="button qkb-add-url">Add Excluded URL</button>
                                                <p class="description">Enter the full URLs where the chatbot should not appear.
                                                    Wildcards are not supported.</p>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div class="qkb-settings-card">
                                <h2 class="title">User Role Visibility</h2>
                                <p class="description">
                                    <?php _e('Control which user roles can see the chatbot button', 'q-knowledge-base'); ?>
                                </p>
                                <table class="form-table">
                                    <tr>
                                        <th scope="row"><?php _e('Visible to Roles', 'q-knowledge-base'); ?></th>
                                        <td>
                                            <?php
                                            $roles = wp_roles()->get_names();
                                            $allowed_roles = get_option('qkb_chatbot_visibility_roles', []);

                                            foreach ($roles as $role_id => $role_name):
                                                $is_allowed = in_array($role_id, $allowed_roles);
                                                ?>
                                                <label style="display: block; margin-bottom: 8px;">
                                                    <input type="checkbox" name="qkb_chatbot_visibility_roles[]"
                                                        value="<?php echo esc_attr($role_id); ?>" <?php checked($is_allowed); ?>>
                                                    <?php echo esc_html($role_name); ?>
                                                </label>
                                            <?php endforeach; ?>
                                            <p class="description">
                                                <?php _e('Select roles that should see the chatbot button. If none are selected, it will be visible to all roles.', 'q-knowledge-base'); ?>
                                            </p>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Embedded Chat Interface Appearance Sub-Tab -->
                        <div class="qkb-settings-subtab-pane" data-subtab="interface">
                            <div class="qkb-settings-card">
                                <h2 class="title">Embedded Chat Interface Appearance</h2>
                                <p class="description">These settings apply to the embedded chat interface (via shortcode)</p>
                                <table class="form-table">
                                    <tr>
                                        <th scope="row">Primary Color</th>
                                        <td>
                                            <input type="color" name="qkb_chat_interface_primary_color"
                                                value="<?php echo esc_attr(get_option('qkb_chat_interface_primary_color', get_option('qkb_primary_color', '#2271b1'))); ?>"
                                                class="qkb-color-picker">
                                            <p class="description">Choose the primary color for the embedded chat interface</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Interface Name</th>
                                        <td>
                                            <input type="text" name="qkb_chat_interface_name"
                                                value="<?php echo esc_attr(get_option('qkb_chat_interface_name', get_option('qkb_chatbot_name', 'Ask Q'))); ?>"
                                                class="regular-text">
                                            <p class="description">The name displayed in the embedded chat interface</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Interface Subtitle</th>
                                        <td>
                                            <input type="text" name="qkb_chat_interface_subtitle"
                                                value="<?php echo esc_attr(get_option('qkb_chat_interface_subtitle', get_option('qkb_splash_subtitle', 'Your AI Assistant'))); ?>"
                                                class="regular-text">
                                            <p class="description">The subtitle displayed in the embedded chat interface</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Interface Icon</th>
                                        <td>
                                            <div class="qkb-interface-icon-preview" style="margin-bottom: 10px;">
                                                <img src="<?php echo esc_url(get_option('qkb_chat_interface_icon', get_option('qkb_chatbot_icon', QKB_PLUGIN_URL . 'assets/images/q.svg'))); ?>"
                                                    alt="Interface Icon" style="max-width: 50px; height: auto;">
                                            </div>
                                            <input type="url" name="qkb_chat_interface_icon"
                                                value="<?php echo esc_url(get_option('qkb_chat_interface_icon', get_option('qkb_chatbot_icon', QKB_PLUGIN_URL . 'assets/images/q.svg'))); ?>"
                                                class="regular-text">
                                            <button type="button" class="button qkb-interface-media-upload">Choose Icon</button>
                                            <p class="description">URL to the embedded chat interface's icon image (SVG
                                                recommended)
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Default Fullscreen Mode</th>
                                        <td>
                                            <label>
                                                <input type="checkbox" name="qkb_chat_interface_fullscreen_default" value="1"
                                                    <?php checked(get_option('qkb_chat_interface_fullscreen_default', false)); ?>>
                                                Enable fullscreen mode by default
                                            </label>
                                            <p class="description">When enabled, the chat interface will open in fullscreen mode
                                                by default</p>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div class="qkb-settings-card">
                                <h2 class="title"><?php _e('Sidebar Visibility', 'q-knowledge-base'); ?></h2>
                                <p class="description">
                                    <?php _e('Control which user roles can see the sidebar in full-page chat mode', 'q-knowledge-base'); ?>
                                </p>
                                <table class="form-table">
                                    <tr>
                                        <th scope="row"><?php _e('Sidebar Visibility', 'q-knowledge-base'); ?></th>
                                        <td>
                                            <?php
                                            $roles = wp_roles()->get_names();
                                            $sidebar_visibility = get_option('qkb_sidebar_visibility', []);

                                            foreach ($roles as $role_id => $role_name) {
                                                $is_visible = isset($sidebar_visibility[$role_id]) && $sidebar_visibility[$role_id] === 'show';
                                                ?>
                                                <label style="display: block; margin-bottom: 8px;">
                                                    <input type="checkbox"
                                                        name="qkb_sidebar_visibility[<?php echo esc_attr($role_id); ?>]"
                                                        value="show" <?php checked($is_visible); ?>>
                                                    <?php echo esc_html($role_name); ?>
                                                </label>
                                                <?php
                                            }
                                            ?>
                                            <p class="description">
                                                <?php _e('Check the roles that should see the sidebar in full-page chat mode', 'q-knowledge-base'); ?>
                                            </p>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div class="qkb-settings-card">
                                <h2 class="title"><?php _e('Assistant Settings', 'q-knowledge-base'); ?></h2>
                                <table class="form-table">
                                    <tr>
                                        <th scope="row"><?php _e('Multiple assistants', 'q-knowledge-base'); ?></th>
                                        <td>
                                            <fieldset>
                                                <label>
                                                    <input type="radio" name="qkb_enable_assistants" value="enabled" <?php checked(get_option('qkb_enable_assistants', 'disabled'), 'enabled'); ?>>
                                                    <?php _e('Enabled', 'q-knowledge-base'); ?>
                                                </label>
                                                <br>
                                                <label>
                                                    <input type="radio" name="qkb_enable_assistants" value="disabled" <?php checked(get_option('qkb_enable_assistants', 'disabled'), 'disabled'); ?>>
                                                    <?php _e('Disabled', 'q-knowledge-base'); ?>
                                                </label>
                                                <p class="description">
                                                    <?php _e('Enable or disable multiple assistants functionality. When disabled, only the default knowledge base assistant will be available.', 'q-knowledge-base'); ?>
                                                </p>
                                            </fieldset>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div class="qkb-settings-card">
                                <h2 class="title"><?php _e('Assistant Access Control', 'q-knowledge-base'); ?></h2>
                                <p class="description">
                                    <?php _e('Control which user roles can access each assistant', 'q-knowledge-base'); ?>
                                </p>
                                <table class="form-table">
                                    <tr>
                                        <th scope="row"><?php _e('Assistant Permissions', 'q-knowledge-base'); ?></th>
                                        <td>
                                            <?php
                                            $assistants = get_terms([
                                                'taxonomy' => 'kb_assistant',
                                                'hide_empty' => false
                                            ]);

                                            $roles = wp_roles()->get_names();
                                            $assistant_roles = get_option('qkb_assistant_roles', []);

                                            if (!empty($assistants)) {
                                                foreach ($assistants as $assistant) {
                                                    $selected_roles = isset($assistant_roles[$assistant->term_id]) ? $assistant_roles[$assistant->term_id] : [];
                                                    ?>
                                                    <div class="qkb-assistant-roles">
                                                        <strong><?php echo esc_html($assistant->name); ?></strong><br>
                                                        <?php foreach ($roles as $role_id => $role_name): ?>
                                                            <label>
                                                                <input type="checkbox"
                                                                    name="qkb_assistant_roles[<?php echo esc_attr($assistant->term_id); ?>][]"
                                                                    value="<?php echo esc_attr($role_id); ?>" <?php checked(in_array($role_id, $selected_roles)); ?>>
                                                                <?php echo esc_html($role_name); ?>
                                                            </label><br>
                                                        <?php endforeach; ?>
                                                        <br>
                                                    </div>
                                                    <?php
                                                }
                                            } else {
                                                echo '<p>' . __('No assistants found.', 'q-knowledge-base') . '</p>';
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div class="qkb-settings-card">
                                <h2 class="title"><?php _e('Add Knowledge Button Access', 'q-knowledge-base'); ?></h2>
                                <p class="description">
                                    <?php _e('Control which user roles can see and use the Add Knowledge button', 'q-knowledge-base'); ?>
                                </p>
                                <table class="form-table">
                                    <tr>
                                        <th scope="row"><?php _e('Show Add Knowledge Button', 'q-knowledge-base'); ?></th>
                                        <td>
                                            <?php
                                            $roles = wp_roles()->get_names();
                                            $allowed_roles = get_option('qkb_add_knowledge_roles', []);

                                            foreach ($roles as $role_id => $role_name) {
                                                $is_allowed = in_array($role_id, $allowed_roles);
                                                ?>
                                                <label style="display: block; margin-bottom: 8px;">
                                                    <input type="checkbox" name="qkb_add_knowledge_roles[]"
                                                        value="<?php echo esc_attr($role_id); ?>" <?php checked($is_allowed); ?>>
                                                    <?php echo esc_html($role_name); ?>
                                                </label>
                                                <?php
                                            }
                                            ?>
                                            <p class="description">
                                                <?php _e('Select which user roles can see and use the Add Knowledge button in the chat interface.', 'q-knowledge-base'); ?>
                                            </p>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div class="qkb-settings-card">
                                <h2 class="title"><?php _e('Export Button Access', 'q-knowledge-base'); ?></h2>
                                <p class="description">
                                    <?php _e('Control which user roles can see and use the Export Transcript button', 'q-knowledge-base'); ?>
                                </p>
                                <table class="form-table">
                                    <tr>
                                        <th scope="row"><?php _e('Show Export Button', 'q-knowledge-base'); ?></th>
                                        <td>
                                            <?php
                                            $roles = wp_roles()->get_names();
                                            $export_roles = get_option('qkb_export_button_roles', []);
                                            foreach ($roles as $role_id => $role_name):
                                                $is_allowed = in_array($role_id, $export_roles);
                                                ?>
                                                <label style="display: block; margin-bottom: 8px;">
                                                    <input type="checkbox" name="qkb_export_button_roles[]"
                                                        value="<?php echo esc_attr($role_id); ?>" <?php checked($is_allowed); ?>>
                                                    <?php echo esc_html($role_name); ?>
                                                </label>
                                            <?php endforeach; ?>
                                            <p class="description">
                                                <?php _e('Select which user roles can see and use the Export Transcript button. Default: none.', 'q-knowledge-base'); ?>
                                            </p>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Global Appearance Reset Button -->
                        <div class="qkb-settings-subtab-pane" data-subtab="global">
                            <p class="appearance-actions">
                                <button type="button" class="button button-secondary qkb-reset-global-appearance">
                                    <?php _e('Reset Global Settings', 'q-knowledge-base'); ?>
                                </button>
                            </p>
                        </div>

                        <!-- Chatbot Appearance Reset Button -->
                        <div class="qkb-settings-subtab-pane" data-subtab="chatbot">
                            <p class="appearance-actions">
                                <button type="button" class="button button-secondary qkb-reset-chatbot-appearance">
                                    <?php _e('Reset Chatbot Settings', 'q-knowledge-base'); ?>
                                </button>
                            </p>
                        </div>

                        <!-- Interface Appearance Reset Button -->
                        <div class="qkb-settings-subtab-pane" data-subtab="interface">
                            <p class="appearance-actions">
                                <button type="button" class="button button-secondary qkb-reset-interface-appearance">
                                    <?php _e('Reset Interface Settings', 'q-knowledge-base'); ?>
                                </button>
                            </p>
                        </div>

                        <!-- Reset All Appearance Settings Button -->
                        <p class="appearance-actions">
                            <button type="button" class="button button-secondary qkb-reset-appearance">
                                <?php _e('Reset All Appearance Settings', 'q-knowledge-base'); ?>
                            </button>
                        </p>
                    </div>



                    <!-- Advanced Tab -->
                    <div class="qkb-settings-tab-pane" data-tab="advanced">
                        <div class="qkb-settings-card">
                            <h2 class="title"><?php _e('External Knowledge Sources', 'q-knowledge-base'); ?></h2>
                            <p class="description">
                                <?php _e('Add external website URLs to expand the knowledge base', 'q-knowledge-base'); ?>
                            </p>
                            <table class="form-table">
                                <tr>
                                    <th scope="row"><?php _e('External URLs', 'q-knowledge-base'); ?></th>
                                    <td>
                                        <div class="qkb-external-sources">
                                            <div class="qkb-url-fields">
                                                <?php
                                                $external_urls = get_option('qkb_external_urls', array());
                                                foreach ($external_urls as $url) {
                                                    ?>
                                                    <div class="qkb-url-field">
                                                        <input type="url" name="qkb_external_urls[]"
                                                            value="<?php echo esc_url($url); ?>" class="regular-text"
                                                            placeholder="https://example.com/page">
                                                        <button type="button" class="button qkb-remove-url">Remove</button>
                                                    </div>
                                                    <?php
                                                }
                                                ?>
                                            </div>
                                            <button type="button" class="button qkb-add-url">Add External URL</button>
                                            <p class="description">
                                                <?php _e('Add URLs to crawl and include in the knowledge base', 'q-knowledge-base'); ?>
                                            </p>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row"><?php _e('Crawl Frequency', 'q-knowledge-base'); ?></th>
                                    <td>
                                        <select name="qkb_crawl_frequency">
                                            <option value="daily" <?php selected(get_option('qkb_crawl_frequency', 'monthly'), 'daily'); ?>>
                                                Daily</option>
                                            <option value="weekly" <?php selected(get_option('qkb_crawl_frequency', 'monthly'), 'weekly'); ?>>Weekly</option>
                                            <option value="monthly" <?php selected(get_option('qkb_crawl_frequency', 'monthly'), 'monthly'); ?>>Monthly</option>
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row"><?php _e('Manual Crawl', 'q-knowledge-base'); ?></th>
                                    <td>
                                        <button type="button" class="button button-secondary qkb-crawl-now">
                                            <?php _e('Crawl Now', 'q-knowledge-base'); ?>
                                            <span class="spinner" style="float:none;"></span>
                                        </button>
                                        <p class="description">
                                            <?php _e('Manually trigger crawling of external URLs', 'q-knowledge-base'); ?>
                                        </p>
                                        <div class="qkb-crawl-status"></div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Submit Button Wrapper -->
                    <div class="qkb-settings-submit-wrapper">
                        <?php submit_button(__('Save Settings', 'q-knowledge-base'), 'primary', 'submit', false); ?>
                    </div>


            </form>
        </div>

        <script>
            jQuery(document).ready(function ($) {
                // Range slider values
                $('.qkb-range-slider').on('input', function () {
                    const id = $(this).attr('id');
                    const value = $(this).val();
                    $('#' + id.replace('-slider', '-value')).text(value);
                });

                // Reset prompt button
                $('.qkb-reset-prompt').on('click', function () {
                    if (confirm('Are you sure you want to reset the system prompt to default? This will overwrite your current prompt.')) {
                        $('textarea[name="qkb_global_system_prompt"]').val(<?php echo json_encode($this->get_default_system_prompt()); ?>);
                    }
                });

                // URL repeater functionality
                $('.qkb-url-repeater .qkb-add-url').on('click', function () {
                    const field = `
                    <div class="qkb-url-field">
                        <input type="url"
                            name="qkb_excluded_urls[]"
                            value=""
                            class="regular-text"
                            placeholder="https://example.com/page">
                        <button type="button" class="button qkb-remove-url">Remove</button>
                    </div>
                `;
                    $('.qkb-url-repeater .qkb-url-fields').append(field);
                });

                $('.qkb-external-sources .qkb-add-url').on('click', function () {
                    const field = `
                    <div class="qkb-url-field">
                        <input type="url"
                            name="qkb_external_urls[]"
                            value=""
                            class="regular-text"
                            placeholder="https://example.com/page">
                        <button type="button" class="button qkb-remove-url">Remove</button>
                    </div>
                `;
                    $('.qkb-external-sources .qkb-url-fields').append(field);
                });

                $(document).on('click', '.qkb-remove-url', function () {
                    $(this).closest('.qkb-url-field').remove();
                });

                // Quick Access Buttons repeater functionality
                $('.qkb-add-quick-access').on('click', function () {
                    // Get the current count of quick access buttons
                    const count = $('.qkb-quick-access-field').length;
                    const newIndex = count;

                    // Create a new field
                    const field = `
                    <div class="qkb-quick-access-field">
                        <div class="qkb-quick-access-row">
                            <div class="qkb-quick-access-icon">
                                <label>Icon (FontAwesome)</label>
                                <input type="text"
                                    name="qkb_quick_access_buttons[${newIndex}][icon]"
                                    value=""
                                    class="regular-text"
                                    placeholder="fas fa-info-circle">
                            </div>
                            <div class="qkb-quick-access-name">
                                <label>Button Name</label>
                                <input type="text"
                                    name="qkb_quick_access_buttons[${newIndex}][name]"
                                    value=""
                                    class="regular-text"
                                    placeholder="Help">
                            </div>
                        </div>
                        <div class="qkb-quick-access-content">
                            <label>Content</label>
                            <textarea
                                name="qkb_quick_access_buttons[${newIndex}][content]"
                                class="widefat"
                                rows="5"></textarea>
                        </div>
                        <div class="qkb-quick-access-options">
                            <label>
                                <input type="checkbox"
                                    name="qkb_quick_access_buttons[${newIndex}][show_in_modal]"
                                    class="qkb-modal-toggle"
                                    value="1">
                                Open in Modal
                            </label>
                        </div>
                        <div class="qkb-quick-access-modal-title" style="display:none;">
                            <label>Modal Title</label>
                            <input type="text"
                                name="qkb_quick_access_buttons[${newIndex}][modal_title]"
                                value=""
                                class="regular-text"
                                placeholder="Enter modal title">
                        </div>
                        <button type="button" class="button qkb-remove-quick-access">Remove</button>
                    </div>
                    `;

                    // Append the new field
                    $('.qkb-quick-access-fields').append(field);
                });

                // Remove Quick Access Button
                $(document).on('click', '.qkb-remove-quick-access', function () {
                    $(this).closest('.qkb-quick-access-field').remove();
                });

                // Toggle modal title field visibility when checkbox is clicked
                $(document).on('change', '.qkb-modal-toggle', function () {
                    const $titleField = $(this).closest('.qkb-quick-access-field').find('.qkb-quick-access-modal-title');
                    if ($(this).is(':checked')) {
                        $titleField.show();
                    } else {
                        $titleField.hide();
                    }
                });

                // Crawl functionality
                $('.qkb-crawl-now').on('click', function () {
                    const $button = $(this);
                    const $spinner = $button.find('.spinner');
                    const $status = $('.qkb-crawl-status');
                    const $progress = $('<div class="qkb-progress"><div class="qkb-progress-bar"></div></div>');

                    $button.prop('disabled', true);
                    $spinner.addClass('is-active');
                    $status.html('').append($progress);

                    function processCrawl() {
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'qkb_manual_crawl',
                                nonce: '<?php echo wp_create_nonce("qkb_manual_crawl"); ?>'
                            },
                            success: function (response) {
                                if (response.success) {
                                    $progress.find('.qkb-progress-bar').css('width', response.data.progress + '%');

                                    if (response.data.progress < 100) {
                                        processCrawl();
                                    } else {
                                        $status.html('<div class="notice notice-success inline"><p>' + response.data.message + '</p></div>');
                                    }
                                } else {
                                    $status.html('<div class="notice notice-error inline"><p>' + response.data.message + '</p></div>');
                                }
                            },
                            error: function () {
                                $status.html('<div class="notice notice-error inline"><p><?php echo esc_js(__("An error occurred while crawling URLs", "q-knowledge-base")); ?></p></div>');
                            },
                            complete: function () {
                                // Always re-enable the button when the request completes
                                $button.prop('disabled', false);
                                $spinner.removeClass('is-active');
                            }
                        });
                    }

                    processCrawl();
                });

                // Media uploader functionality is now in admin-settings.js

                // Reset all appearance settings
                $('.qkb-reset-appearance').click(function (e) {
                    e.preventDefault();

                    if (confirm('<?php _e('Are you sure you want to reset ALL appearance settings to defaults? This cannot be undone.', 'q-knowledge-base'); ?>')) {
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'qkb_reset_appearance',
                                reset_type: 'all',
                                nonce: '<?php echo wp_create_nonce("qkb_ajax_nonce"); ?>'
                            },
                            beforeSend: function () {
                                $('.qkb-reset-appearance').prop('disabled', true);
                            },
                            success: function (response) {
                                if (response.success) {
                                    // Reset global appearance settings
                                    $('input[name="qkb_primary_color"]').val('#2271b1');

                                    // Reset floating chatbot appearance settings
                                    $('input[name="qkb_chatbot_name"]').val('Ask Q');
                                    $('input[name="qkb_splash_subtitle"]').val('Your AI Assistant');
                                    $('input[name="qkb_chatbot_primary_color"]').val('#2271b1');
                                    var defaultIcon = '<?php echo esc_js(QKB_PLUGIN_URL . 'assets/images/q.svg'); ?>';
                                    $('input[name="qkb_chatbot_icon"]').val(defaultIcon);
                                    $('.qkb-icon-preview img').attr('src', defaultIcon);

                                    // Reset embedded chat interface appearance settings
                                    $('input[name="qkb_chat_interface_name"]').val('Ask Q');
                                    $('input[name="qkb_chat_interface_subtitle"]').val('Your AI Assistant');
                                    $('input[name="qkb_chat_interface_primary_color"]').val('#2271b1');
                                    $('input[name="qkb_chat_interface_icon"]').val(defaultIcon);
                                    $('.qkb-interface-icon-preview img').attr('src', defaultIcon);

                                    const notice = $('<div class="notice notice-success is-dismissible"><p>' + response.data.message + '</p></div>');
                                    $('.wrap > h1').after(notice);

                                    setTimeout(function () {
                                        window.location.reload();
                                    }, 1500);
                                }
                            },
                            error: function () {
                                const notice = $('<div class="notice notice-error is-dismissible"><p><?php _e('An error occurred while resetting appearance settings.', 'q-knowledge-base'); ?></p></div>');
                                $('.wrap > h1').after(notice);
                            },
                            complete: function () {
                                $('.qkb-reset-appearance').prop('disabled', false);
                            }
                        });
                    }
                });

                // Reset global appearance settings
                $('.qkb-reset-global-appearance').click(function (e) {
                    e.preventDefault();

                    if (confirm('<?php _e('Are you sure you want to reset global appearance settings to defaults? This cannot be undone.', 'q-knowledge-base'); ?>')) {
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'qkb_reset_appearance',
                                reset_type: 'global',
                                nonce: '<?php echo wp_create_nonce("qkb_ajax_nonce"); ?>'
                            },
                            beforeSend: function () {
                                $('.qkb-reset-global-appearance').prop('disabled', true);
                            },
                            success: function (response) {
                                if (response.success) {
                                    // Reset global appearance settings
                                    $('input[name="qkb_primary_color"]').val('#2271b1');

                                    const notice = $('<div class="notice notice-success is-dismissible"><p>' + response.data.message + '</p></div>');
                                    $('.wrap > h1').after(notice);
                                }
                            },
                            error: function () {
                                const notice = $('<div class="notice notice-error is-dismissible"><p><?php _e('An error occurred while resetting global appearance settings.', 'q-knowledge-base'); ?></p></div>');
                                $('.wrap > h1').after(notice);
                            },
                            complete: function () {
                                $('.qkb-reset-global-appearance').prop('disabled', false);
                            }
                        });
                    }
                });

                // Reset chatbot appearance settings
                $('.qkb-reset-chatbot-appearance').click(function (e) {
                    e.preventDefault();

                    if (confirm('<?php _e('Are you sure you want to reset chatbot appearance settings to defaults? This cannot be undone.', 'q-knowledge-base'); ?>')) {
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'qkb_reset_appearance',
                                reset_type: 'chatbot',
                                nonce: '<?php echo wp_create_nonce("qkb_ajax_nonce"); ?>'
                            },
                            beforeSend: function () {
                                $('.qkb-reset-chatbot-appearance').prop('disabled', true);
                            },
                            success: function (response) {
                                if (response.success) {
                                    // Reset floating chatbot appearance settings
                                    $('input[name="qkb_chatbot_name"]').val('Ask Q');
                                    $('input[name="qkb_splash_subtitle"]').val('Your AI Assistant');
                                    $('input[name="qkb_chatbot_primary_color"]').val('#2271b1');
                                    var defaultIcon = '<?php echo esc_js(QKB_PLUGIN_URL . 'assets/images/q.svg'); ?>';
                                    $('input[name="qkb_chatbot_icon"]').val(defaultIcon);
                                    $('.qkb-icon-preview img').attr('src', defaultIcon);

                                    const notice = $('<div class="notice notice-success is-dismissible"><p>' + response.data.message + '</p></div>');
                                    $('.wrap > h1').after(notice);
                                }
                            },
                            error: function () {
                                const notice = $('<div class="notice notice-error is-dismissible"><p><?php _e('An error occurred while resetting chatbot appearance settings.', 'q-knowledge-base'); ?></p></div>');
                                $('.wrap > h1').after(notice);
                            },
                            complete: function () {
                                $('.qkb-reset-chatbot-appearance').prop('disabled', false);
                            }
                        });
                    }
                });

                // Reset interface appearance settings
                $('.qkb-reset-interface-appearance').click(function (e) {
                    e.preventDefault();

                    if (confirm('<?php _e('Are you sure you want to reset interface appearance settings to defaults? This cannot be undone.', 'q-knowledge-base'); ?>')) {
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'qkb_reset_appearance',
                                reset_type: 'interface',
                                nonce: '<?php echo wp_create_nonce("qkb_ajax_nonce"); ?>'
                            },
                            beforeSend: function () {
                                $('.qkb-reset-interface-appearance').prop('disabled', true);
                            },
                            success: function (response) {
                                if (response.success) {
                                    // Reset embedded chat interface appearance settings
                                    $('input[name="qkb_chat_interface_name"]').val('Ask Q');
                                    $('input[name="qkb_chat_interface_subtitle"]').val('Your AI Assistant');
                                    $('input[name="qkb_chat_interface_primary_color"]').val('#2271b1');
                                    var defaultIcon = '<?php echo esc_js(QKB_PLUGIN_URL . 'assets/images/q.svg'); ?>';
                                    $('input[name="qkb_chat_interface_icon"]').val(defaultIcon);
                                    $('.qkb-interface-icon-preview img').attr('src', defaultIcon);

                                    const notice = $('<div class="notice notice-success is-dismissible"><p>' + response.data.message + '</p></div>');
                                    $('.wrap > h1').after(notice);
                                }
                            },
                            error: function () {
                                const notice = $('<div class="notice notice-error is-dismissible"><p><?php _e('An error occurred while resetting interface appearance settings.', 'q-knowledge-base'); ?></p></div>');
                                $('.wrap > h1').after(notice);
                            },
                            complete: function () {
                                $('.qkb-reset-interface-appearance').prop('disabled', false);
                            }
                        });
                    }
                });
            });
        </script>

        <style>
            .qkb-context-priority {
                margin-bottom: 10px;
            }

            .qkb-context-priority .context-label {
                display: inline-block;
                width: 180px;
            }

            .qkb-crawl-now .spinner {
                margin: 0 0 0 5px;
                visibility: hidden;
            }

            .qkb-crawl-now .spinner.is-active {
                visibility: visible;
            }

            .qkb-crawl-status {
                margin-top: 10px;
            }

            .qkb-crawl-status .notice {
                margin: 5px 0;
            }

            .qkb-progress {
                height: 20px;
                background: #f0f0f1;
                border-radius: 3px;
                margin: 10px 0;
                overflow: hidden;
            }

            .qkb-progress-bar {
                height: 100%;
                background: #2271b1;
                width: 0;
                transition: width 0.3s ease;
            }

            .appearance-actions {
                display: flex;
                gap: 10px;
                align-items: center;
                margin-top: 15px;
            }

            .qkb-reset-appearance {
                background-color: #dc3545 !important;
                border-color: #dc3545 !important;
                color: #fff !important;
            }

            .qkb-reset-appearance:hover {
                background-color: #bb2d3b !important;
                border-color: #bb2d3b !important;
            }

            .qkb-reset-global-appearance,
            .qkb-reset-chatbot-appearance,
            .qkb-reset-interface-appearance {
                background-color: #6c757d !important;
                border-color: #6c757d !important;
                color: #fff !important;
            }

            .qkb-reset-global-appearance:hover,
            .qkb-reset-chatbot-appearance:hover,
            .qkb-reset-interface-appearance:hover {
                background-color: #5a6268 !important;
                border-color: #5a6268 !important;
            }

            .qkb-settings-subtab-pane {
                margin-bottom: 15px;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 4px;
            }

            /* Hide all sections that are not in tabs */
            .qkb-settings-form>h2:not(.qkb-settings-tab-pane h2),
            .qkb-settings-form>p:not(.qkb-settings-tab-pane p),
            .qkb-settings-form>table:not(.qkb-settings-tab-pane table) {
                display: none;
            }

            /* Quick Access Options Styling */
            .qkb-quick-access-options {
                margin: 10px 0;
                padding: 5px 0;
                border-top: 1px solid #f0f0f1;
            }

            .qkb-quick-access-options label {
                display: flex;
                align-items: center;
                gap: 5px;
                font-size: 13px;
                color: #50575e;
            }
        </style>
        <?php
    }

    public function enqueue_editor($hook)
    {
        if ($hook !== 'kb_knowledge_base_page_qkb-settings') {
            return;
        }

        wp_enqueue_editor();
        wp_enqueue_media(); // Ensure this is called before the script that uses it
        wp_enqueue_script('qkb-settings', QKB_PLUGIN_URL . 'assets/js/admin-settings.js', ['jquery', 'wp-editor', 'media-upload'], QKB_VERSION, true);

        // Add script for embedding generation
        wp_enqueue_style('dashicons');

        // Enqueue embeddings script
        wp_enqueue_script('qkb-embeddings', QKB_PLUGIN_URL . 'assets/js/embeddings.js', ['jquery'], QKB_VERSION, true);
        wp_localize_script('qkb-embeddings', 'qkb_embeddings', array(
            'nonce' => wp_create_nonce('qkb_ajax_nonce')
        ));

        // Enqueue embeddings CSS
        wp_enqueue_style('qkb-embeddings', QKB_PLUGIN_URL . 'assets/css/embeddings.css', [], QKB_VERSION);

        // Enqueue settings tabs CSS and JS
        wp_enqueue_style('qkb-settings-tabs', QKB_PLUGIN_URL . 'assets/css/settings-tabs.css', [], QKB_VERSION);
        wp_enqueue_style('qkb-modern-settings', QKB_PLUGIN_URL . 'assets/css/modern-settings.css', ['qkb-settings-tabs'], QKB_VERSION);
        wp_enqueue_script('qkb-settings-tabs', QKB_PLUGIN_URL . 'assets/js/settings-tabs.js', ['jquery'], QKB_VERSION, true);
    }

    public function handle_reset_appearance()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $reset_type = isset($_POST['reset_type']) ? sanitize_text_field($_POST['reset_type']) : 'all';
        $message = '';

        switch ($reset_type) {
            case 'global':
                // Reset global appearance settings only
                update_option('qkb_primary_color', '#2271b1');
                $message = __('Global appearance settings have been reset to defaults.', 'q-knowledge-base');
                break;

            case 'chatbot':
                // Reset floating chatbot appearance settings only
                update_option('qkb_chatbot_name', 'Ask Q');
                update_option('qkb_splash_subtitle', 'Your AI Assistant');
                update_option('qkb_chatbot_icon', QKB_PLUGIN_URL . 'assets/images/q.svg');
                update_option('qkb_chatbot_primary_color', '#2271b1');
                update_option('qkb_default_chatbot_assistant', 0);
                $message = __('Chatbot appearance settings have been reset to defaults.', 'q-knowledge-base');
                break;

            case 'interface':
                // Reset embedded chat interface appearance settings only
                update_option('qkb_chat_interface_name', 'Ask Q');
                update_option('qkb_chat_interface_subtitle', 'Your AI Assistant');
                update_option('qkb_chat_interface_icon', QKB_PLUGIN_URL . 'assets/images/q.svg');
                update_option('qkb_chat_interface_primary_color', '#2271b1');
                $message = __('Chat interface appearance settings have been reset to defaults.', 'q-knowledge-base');
                break;

            case 'all':
            default:
                // Reset all appearance settings
                // Reset global appearance settings
                update_option('qkb_primary_color', '#2271b1');

                // Reset floating chatbot appearance settings
                update_option('qkb_chatbot_name', 'Ask Q');
                update_option('qkb_splash_subtitle', 'Your AI Assistant');
                update_option('qkb_chatbot_icon', QKB_PLUGIN_URL . 'assets/images/q.svg');
                update_option('qkb_chatbot_primary_color', '#2271b1');
                update_option('qkb_default_chatbot_assistant', 0);

                // Reset embedded chat interface appearance settings
                update_option('qkb_chat_interface_name', 'Ask Q');
                update_option('qkb_chat_interface_subtitle', 'Your AI Assistant');
                update_option('qkb_chat_interface_icon', QKB_PLUGIN_URL . 'assets/images/q.svg');
                update_option('qkb_chat_interface_primary_color', '#2271b1');

                $message = __('All appearance settings have been reset to defaults.', 'q-knowledge-base');
                break;
        }

        wp_send_json_success([
            'message' => $message
        ]);
    }

    public function handle_generate_gap_suggestion()
    {
        check_ajax_referer('qkb_gap_suggestion', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $gap_id = isset($_POST['gap_id']) ? intval($_POST['gap_id']) : 0;
        if (!$gap_id) {
            wp_send_json_error('Invalid gap ID');
            return;
        }

        $gap_analyzer = new QKB_Gap_Analyzer();
        $gaps = $gap_analyzer->get_content_gaps('pending', 1, $gap_id);

        if (empty($gaps)) {
            wp_send_json_error('Gap not found');
            return;
        }

        $gap = $gaps[0];
        $suggestion = $gap_analyzer->generate_content_suggestion($gap->topic);

        if ($suggestion) {
            // Update the gap with the suggestion
            global $wpdb;
            $wpdb->update(
                $wpdb->prefix . 'qkb_content_gaps',
                ['suggested_content' => $suggestion],
                ['id' => $gap_id]
            );

            wp_send_json_success([
                'suggestion' => $suggestion,
                'topic' => $gap->topic
            ]);
        } else {
            wp_send_json_error('Failed to generate suggestion');
        }
    }

    public function handle_create_gap_draft()
    {
        check_ajax_referer('qkb_gap_draft', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $content = isset($_POST['content']) ? wp_kses_post($_POST['content']) : '';
        $gap_id = isset($_POST['gap_id']) ? intval($_POST['gap_id']) : 0;

        if (!$content || !$gap_id) {
            wp_send_json_error('Invalid parameters');
            return;
        }

        $gap_analyzer = new QKB_Gap_Analyzer();
        $gap = $gap_analyzer->get_content_gaps('pending', 1, $gap_id)[0];

        $post_data = [
            'post_title' => $gap->topic,
            'post_content' => $content,
            'post_status' => 'draft',
            'post_type' => 'kb_knowledge_base'
        ];

        $post_id = wp_insert_post($post_data);

        if ($post_id) {
            wp_send_json_success([
                'edit_url' => admin_url('post.php?post=' . $post_id . '&action=edit')
            ]);
        } else {
            wp_send_json_error('Failed to create draft');
        }
    }

    public function enqueue_content_gaps_scripts($hook)
    {
        if ($hook !== 'kb_knowledge_base_page_qkb-content-gaps') {
            return;
        }

        wp_enqueue_style('qkb-admin-css', plugin_dir_url(QKB_PLUGIN_FILE) . 'assets/css/admin.css', [], QKB_VERSION);
        wp_enqueue_style('qkb-modern-content-gaps', plugin_dir_url(QKB_PLUGIN_FILE) . 'assets/css/modern-content-gaps.css', [], QKB_VERSION);
        wp_enqueue_script('marked-js', 'https://cdn.jsdelivr.net/npm/marked/marked.min.js', [], '4.0.0', true);
        wp_enqueue_script('qkb-content-gaps', plugin_dir_url(QKB_PLUGIN_FILE) . 'assets/js/content-gaps.js', ['jquery', 'marked-js'], QKB_VERSION, true);
        wp_enqueue_editor();

        wp_localize_script('qkb-content-gaps', 'qkbContentGaps', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'suggestionNonce' => wp_create_nonce('qkb_gap_suggestion'),
            'draftNonce' => wp_create_nonce('qkb_gap_draft'),
            'suggestionTitle' => __('Content Suggestion', 'q-knowledge-base'),
            'createContentTitle' => __('Create Knowledge Base Article', 'q-knowledge-base'),
            'topicLabel' => __('Topic', 'q-knowledge-base'),
            'generateSuggestionBtn' => __('Generate Suggestion', 'q-knowledge-base'),
            'createDraftBtn' => __('Create Draft', 'q-knowledge-base'),
            'noSuggestionMsg' => __('No content suggestion available for this topic.', 'q-knowledge-base'),
            'suggestionGeneratedMsg' => __('Content suggestion generated successfully!', 'q-knowledge-base'),
            'draftCreatedMsg' => __('Draft created successfully! Redirecting to editor...', 'q-knowledge-base'),
            'emptyContentMsg' => __('Please enter some content before creating a draft.', 'q-knowledge-base'),
            'errorMsg' => __('An error occurred. Please try again.', 'q-knowledge-base')
        ]);
    }

    public function render_content_gaps_page()
    {
        $gap_analyzer = new QKB_Gap_Analyzer();
        $recommendations = $gap_analyzer->analyze_content_performance();

        // Sort recommendations by priority score (descending)
        usort($recommendations, function ($a, $b) {
            return $b['priority_score'] <=> $a['priority_score'];
        });

        ?>
        <div class="wrap">
            <h1><?php _e('Content Gap Analysis & Recommendations', 'q-knowledge-base'); ?></h1>

            <div class="qkb-analytics-summary">
                <h2><?php _e('Performance Overview', 'q-knowledge-base'); ?></h2>
                <?php $this->render_performance_metrics(); ?>
            </div>

            <div class="qkb-content-recommendations">
                <h2><?php _e('Content Recommendations', 'q-knowledge-base'); ?></h2>
                <p class="description">
                    <?php _e('These topics have been identified as content gaps based on user queries. Create knowledge base articles to address these gaps.', 'q-knowledge-base'); ?>
                </p>

                <table class="wp-list-table widefat fixed striped qkb-content-table">
                    <thead>
                        <tr>
                            <th width="80"><?php _e('Priority', 'q-knowledge-base'); ?></th>
                            <th><?php _e('Topic', 'q-knowledge-base'); ?></th>
                            <th width="150"><?php _e('User Engagement', 'q-knowledge-base'); ?></th>
                            <th width="150"><?php _e('Suggested Content', 'q-knowledge-base'); ?></th>
                            <th width="150"><?php _e('Actions', 'q-knowledge-base'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($recommendations)): ?>
                            <tr>
                                <td colspan="5"><?php _e('No content gaps found. Great job!', 'q-knowledge-base'); ?></td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($recommendations as $index => $rec): ?>
                                <?php
                                $priority_class = '';
                                $priority_score = $rec['priority_score'] * 100;
                                if ($priority_score > 70) {
                                    $priority_class = 'high';
                                } elseif ($priority_score > 40) {
                                    $priority_class = 'medium';
                                } else {
                                    $priority_class = 'low';
                                }
                                ?>
                                <tr>
                                    <td>
                                        <div class="priority-score <?php echo esc_attr($priority_class); ?>">
                                            <?php echo esc_html(number_format($priority_score, 0)); ?>
                                        </div>
                                    </td>
                                    <td><?php echo esc_html($rec['topic']); ?></td>
                                    <td>
                                        <div class="engagement-metrics">
                                            <div>Queries: <?php echo esc_html($rec['metrics']['frequency']); ?></div>
                                            <div>Satisfaction:
                                                <?php echo esc_html(number_format($rec['metrics']['user_engagement']['satisfaction_rate'] * 100, 1)); ?>%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="button show-content-suggestion"
                                            data-suggestion="<?php echo esc_attr(json_encode($rec['suggested_content'])); ?>">
                                            <?php _e('View Suggestion', 'q-knowledge-base'); ?>
                                        </button>
                                    </td>
                                    <td>
                                        <button class="button button-primary create-content"
                                            data-topic="<?php echo esc_attr($rec['topic']); ?>"
                                            data-gap-id="<?php echo esc_attr($index); ?>">
                                            <?php _e('Create Content', 'q-knowledge-base'); ?>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>


        <?php
    }

    private function render_performance_metrics()
    {
        global $wpdb;

        // Get total gaps
        $total_gaps = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->prefix}qkb_content_gaps"
        );

        // Get high priority gaps (priority score > 0.7)
        $high_priority = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->prefix}qkb_content_gaps
            WHERE frequency > 5 AND confidence_score < 0.3"
        );

        // Get average satisfaction rate
        $avg_satisfaction = $wpdb->get_var(
            "SELECT AVG(CASE WHEN feedback > 0 THEN 1 ELSE 0 END)
            FROM {$wpdb->prefix}qkb_ml_interactions"
        );

        // Get content coverage trend
        $coverage_trend = $this->calculate_coverage_trend();

        ?>
        <div class="qkb-metrics-grid">
            <div class="qkb-metric-card">
                <h3><?php _e('Total Content Gaps', 'q-knowledge-base'); ?></h3>
                <div class="qkb-stat">
                    <?php echo esc_html($total_gaps); ?>
                    <?php if ($coverage_trend > 0): ?>
                        <span class="qkb-trend positive">+<?php echo esc_html($coverage_trend); ?>%</span>
                    <?php elseif ($coverage_trend < 0): ?>
                        <span class="qkb-trend negative"><?php echo esc_html($coverage_trend); ?>%</span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="qkb-metric-card">
                <h3><?php _e('High Priority Gaps', 'q-knowledge-base'); ?></h3>
                <div class="qkb-stat">
                    <?php echo esc_html($high_priority); ?>
                    <span class="qkb-description"><?php _e('need immediate attention', 'q-knowledge-base'); ?></span>
                </div>
            </div>

            <div class="qkb-metric-card">
                <h3><?php _e('User Satisfaction', 'q-knowledge-base'); ?></h3>
                <div class="qkb-stat">
                    <?php echo esc_html(number_format($avg_satisfaction * 100, 1)); ?>%
                    <span class="qkb-description"><?php _e('average satisfaction rate', 'q-knowledge-base'); ?></span>
                </div>
            </div>

            <div class="qkb-metric-card">
                <h3><?php _e('Content Coverage', 'q-knowledge-base'); ?></h3>
                <div class="qkb-stat">
                    <?php
                    $coverage = $this->calculate_content_coverage();
                    echo esc_html(number_format($coverage * 100, 1));
                    ?>%
                    <span class="qkb-description"><?php _e('of queries have good responses', 'q-knowledge-base'); ?></span>
                </div>
            </div>
        </div>
        <?php
    }

    private function calculate_coverage_trend()
    {
        global $wpdb;

        // Get gaps closed in last 30 days
        $gaps_closed = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}qkb_content_gaps
            WHERE status = 'resolved'
            AND last_detected >= DATE_SUB(NOW(), INTERVAL %d DAY)",
            30
        ));

        // Get new gaps in last 30 days
        $new_gaps = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}qkb_content_gaps
            WHERE first_detected >= DATE_SUB(NOW(), INTERVAL %d DAY)",
            30
        ));

        // Calculate trend percentage
        if ($new_gaps > 0) {
            return round((($gaps_closed - $new_gaps) / $new_gaps) * 100);
        }

        return 0;
    }

    private function calculate_content_coverage()
    {
        global $wpdb;

        // Get total number of unique queries
        $total_queries = $wpdb->get_var(
            "SELECT COUNT(DISTINCT query) FROM {$wpdb->prefix}qkb_ml_interactions"
        );

        if (!$total_queries) {
            return 0;
        }

        // Get number of queries with good responses (confidence score > 0.7)
        $good_responses = $wpdb->get_var(
            "SELECT COUNT(DISTINCT query) FROM {$wpdb->prefix}qkb_ml_interactions
            WHERE confidence_score > 0.7"
        );

        return $good_responses / $total_queries;
    }
}