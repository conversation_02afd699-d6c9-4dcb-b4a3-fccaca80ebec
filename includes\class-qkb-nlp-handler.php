<?php
if (!defined('ABSPATH')) {
    exit;
}

class QKB_NLP_Handler {
    private $stop_words = ['a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from', 'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the', 'to', 'was', 'were', 'will', 'with'];
    private $sentiment_keywords = [
        'positive' => ['great', 'awesome', 'excellent', 'good', 'happy', 'pleased', 'thanks', 'helpful', 'perfect', 'love', 'appreciate', 'wonderful', 'fantastic', 'brilliant', 'outstanding'],
        'negative' => ['bad', 'poor', 'terrible', 'awful', 'unhappy', 'frustrated', 'annoying', 'difficult', 'wrong', 'hate', 'disappointed', 'useless', 'broken', 'confusing', 'issue'],
        'urgent' => ['urgent', 'asap', 'emergency', 'critical', 'immediately', 'quick', 'fast', 'soon', 'urgent', 'help', 'now', 'priority', 'deadline', 'hurry', 'crucial']
    ];

    // Enhanced properties for NLP
    private $intent_patterns = [
        'question' => ['/\?$/', '/^(what|how|why|when|where|who|which|can|could|would|will|is|are|do|does|did)/i'],
        'command' => ['/^(show|tell|find|search|help|list|explain|get|give|provide|display)/i'],
        'feedback' => ['/^(thanks|thank you|good|great|excellent|awesome|terrible|bad|poor|awful)/i']
    ];
    private $context_memory = [];

    // Pagination and caching properties
    private $messages_per_page = 50;
    private $cache_expiry = 3600; // 1 hour
    private $cache_prefix = 'qkb_chat_';
    
    // New properties for enhanced NLP
    private $language_detection = [];
    private $entity_types = ['person', 'organization', 'location', 'product', 'date', 'time', 'money', 'percent'];
    private $max_memory_size = 1000;
    private $stemming_exceptions = ['better' => 'good', 'worse' => 'bad', 'best' => 'good', 'worst' => 'bad'];

    /**
     * Enhanced text preprocessing with lemmatization and advanced cleaning
     */
    public function preprocess_text($text) {
        if (empty($text)) {
            return '';
        }
        
        // Convert to lowercase
        $text = strtolower($text);
        
        // Remove special characters but keep meaningful punctuation
        $text = preg_replace('/[^a-z0-9\s\?\!\.\,\:\;\-]/', '', $text);
        
        // Normalize whitespace
        $text = preg_replace('/\s+/', ' ', trim($text));
        
        // Basic lemmatization (can be expanded)
        $text = $this->lemmatize($text);
        
        // Remove stop words while preserving context
        $words = explode(' ', $text);
        $words = array_filter($words, function($word) {
            return !in_array($word, $this->stop_words) || $this->is_context_important($word);
        });
        
        return implode(' ', $words);
    }

    /**
     * Analyze query intent and sentiment with improved accuracy
     */
    public function analyze_query($text) {
        if (empty($text)) {
            return [
                'intent' => 'unknown',
                'sentiment' => ['tone' => 'neutral'],
                'keywords' => [],
                'urgency' => false,
                'entities' => []
            ];
        }
        
        $processed_text = $this->preprocess_text($text);
        $words = explode(' ', $processed_text);
        
        $analysis = [
            'intent' => $this->detect_intent($text, $words),
            'sentiment' => $this->analyze_sentiment($words),
            'keywords' => $this->extract_keywords($processed_text),
            'urgency' => $this->detect_urgency($words),
            'entities' => $this->extract_entities($text)
        ];
        
        return $analysis;
    }

    /**
     * Detect query intent using regex patterns for better accuracy
     */
    private function detect_intent($original_text, $words) {
        // Check against regex patterns first
        foreach ($this->intent_patterns as $intent => $patterns) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $original_text)) {
                    return $intent;
                }
            }
        }
        
        // Fallback to word-based detection
        $question_markers = ['what', 'how', 'why', 'when', 'where', 'who', 'which', 'can', 'could', 'would'];
        $command_markers = ['show', 'tell', 'find', 'search', 'help', 'list', 'explain', 'get', 'give'];
        
        $first_word = reset($words);
        $last_char = substr($original_text, -1);
        
        if (in_array($first_word, $question_markers) || $last_char === '?') {
            return 'question';
        } elseif (in_array($first_word, $command_markers)) {
            return 'command';
        }
        
        return 'statement';
    }

    /**
     * Analyze sentiment with scoring
     */
    public function analyze_sentiment($text) {
        $processed_text = $this->preprocess_text($text);
        $words = explode(' ', $processed_text);
        
        $sentiment = [
            'tone' => $this->detect_tone($words),
            'urgency' => $this->detect_urgency($words),
            'emotion' => $this->detect_emotion($words),
            'formality' => $this->detect_formality($words)
        ];
        
        // Add context awareness
        $this->update_context_memory($sentiment);
        
        return $sentiment;
    }

    /**
     * Detect query urgency
     */
    private function detect_urgency($words) {
        foreach ($words as $word) {
            if (in_array($word, $this->sentiment_keywords['urgent'])) {
                return true;
            }
        }
        return false;
    }

    /**
     * Improved lemmatization with exceptions handling
     */
    private function lemmatize($text) {
        $common_suffixes = [
            'ing' => '',
            'ed' => '',
            'ly' => '',
            'es' => '',
            's' => '',
            'ment' => '',
            'tion' => 't',
            'ations' => 'ate',
            'ies' => 'y'
        ];
        
        $words = explode(' ', $text);
        foreach ($words as &$word) {
            // Check for exceptions first
            if (isset($this->stemming_exceptions[$word])) {
                $word = $this->stemming_exceptions[$word];
                continue;
            }
            
            // Apply suffix rules
            foreach ($common_suffixes as $suffix => $replacement) {
                if (strlen($word) > strlen($suffix) + 2 && substr($word, -strlen($suffix)) === $suffix) {
                    $word = substr($word, 0, -strlen($suffix)) . $replacement;
                    break;
                }
            }
        }
        
        return implode(' ', $words);
    }

    /**
     * Check if a stop word might be important for context
     */
    private function is_context_important($word) {
        $important_stop_words = ['not', 'no', 'none', 'never', 'without'];
        return in_array($word, $important_stop_words);
    }

    public function extract_keywords($text, $max_keywords = 5) {
        $processed_text = $this->preprocess_text($text);
        $words = explode(' ', $processed_text);
        
        // Count word frequencies
        $word_counts = array_count_values($words);
        
        // Sort by frequency
        arsort($word_counts);
        
        // Return top keywords
        return array_slice(array_keys($word_counts), 0, $max_keywords);
    }

    /**
     * Extract named entities from text (basic implementation)
     */
    private function extract_entities($text) {
        $entities = [];
        
        // Simple pattern matching for common entity types
        // In a production environment, consider using a dedicated NER library
        
        // Extract emails
        if (preg_match_all('/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}\b/', $text, $matches)) {
            foreach ($matches[0] as $email) {
                $entities[] = ['type' => 'email', 'value' => $email];
            }
        }
        
        // Extract URLs
        if (preg_match_all('/\bhttps?:\/\/[^\s]+\b/', $text, $matches)) {
            foreach ($matches[0] as $url) {
                $entities[] = ['type' => 'url', 'value' => $url];
            }
        }
        
        // Extract dates (simple patterns)
        if (preg_match_all('/\b\d{1,2}\/\d{1,2}\/\d{2,4}\b/', $text, $matches)) {
            foreach ($matches[0] as $date) {
                $entities[] = ['type' => 'date', 'value' => $date];
            }
        }
        
        return $entities;
    }

    /**
     * Calculate text similarity with TF-IDF weighting
     */
    public function calculate_text_similarity($text1, $text2) {
        if (empty($text1) || empty($text2)) {
            return 0;
        }
        
        $processed1 = $this->preprocess_text($text1);
        $processed2 = $this->preprocess_text($text2);
        
        $words1 = explode(' ', $processed1);
        $words2 = explode(' ', $processed2);
        
        if (empty($words1) || empty($words2)) {
            return 0;
        }
        
        // Count word frequencies (TF)
        $freq1 = array_count_values($words1);
        $freq2 = array_count_values($words2);
        
        // Calculate document frequency (IDF)
        $all_words = array_unique(array_merge($words1, $words2));
        $doc_freq = [];
        
        foreach ($all_words as $word) {
            $doc_freq[$word] = (isset($freq1[$word]) ? 1 : 0) + (isset($freq2[$word]) ? 1 : 0);
        }
        
        // Calculate vectors with TF-IDF weights
        $vector1 = [];
        $vector2 = [];
        
        foreach ($all_words as $word) {
            $idf = log(2 / $doc_freq[$word]);
            $vector1[$word] = (isset($freq1[$word]) ? $freq1[$word] : 0) * $idf;
            $vector2[$word] = (isset($freq2[$word]) ? $freq2[$word] : 0) * $idf;
        }
        
        // Calculate cosine similarity
        $dot_product = 0;
        $magnitude1 = 0;
        $magnitude2 = 0;
        
        foreach ($all_words as $word) {
            $dot_product += $vector1[$word] * $vector2[$word];
            $magnitude1 += $vector1[$word] * $vector1[$word];
            $magnitude2 += $vector2[$word] * $vector2[$word];
        }
        
        $magnitude1 = sqrt($magnitude1);
        $magnitude2 = sqrt($magnitude2);
        
        if ($magnitude1 == 0 || $magnitude2 == 0) {
            return 0;
        }
        
        return $dot_product / ($magnitude1 * $magnitude2);
    }

    /**
     * Get paginated chat history with caching
     */
    public function get_chat_history($page = 1, $context_window = null) {
        $cache_key = $this->cache_prefix . 'history_' . $page;
        $cached_data = wp_cache_get($cache_key);

        if (false !== $cached_data) {
            return $cached_data;
        }

        $offset = ($page - 1) * $this->messages_per_page;
        $messages = $this->fetch_messages($offset, $this->messages_per_page, $context_window);

        wp_cache_set($cache_key, $messages, '', $this->cache_expiry);
        return $messages;
    }

    /**
     * Fetch messages with context window optimization
     */
    private function fetch_messages($offset, $limit, $context_window = null) {
        if ($context_window) {
            // Fetch messages within context window for better relevance
            $context_start = max(0, $offset - $context_window);
            $context_limit = $limit + ($offset - $context_start);
            
            return array_slice(
                $this->context_memory, 
                $context_start, 
                $context_limit
            );
        }

        return array_slice(
            $this->context_memory, 
            $offset, 
            $limit
        );
    }

    /**
     * Update context memory with improved memory management
     */
    private function update_context_memory($sentiment) {
        $this->context_memory[] = [
            'sentiment' => $sentiment,
            'timestamp' => time(),
            'id' => uniqid('msg_')
        ];
        
        // Implement virtual scrolling by keeping only recent messages in memory
        if (count($this->context_memory) > $this->max_memory_size) {
            // Remove older messages but keep some context
            $keep_last = (int)($this->max_memory_size * 0.8); // Keep 80% of max size
            $this->context_memory = array_slice($this->context_memory, -$keep_last);
            
            // Cache removed messages for later retrieval if needed
            $this->cache_old_messages(array_slice($this->context_memory, 0, -$keep_last));
        }
    }

    /**
     * Cache older messages for later retrieval
     */
    private function cache_old_messages($messages) {
        if (empty($messages)) {
            return;
        }

        $cache_key = $this->cache_prefix . 'archive_' . time();
        wp_cache_set($cache_key, $messages, '', DAY_IN_SECONDS);
    }

    // Add new method for tone detection
    private function detect_tone($words) {
        $tone_markers = [
            'formal' => ['kindly', 'please', 'would', 'could'],
            'casual' => ['hey', 'hi', 'thanks', 'cool'],
            'urgent' => ['asap', 'urgent', 'emergency', 'immediately'],
            'frustrated' => ['wrong', 'error', 'issue', 'problem']
        ];
        
        $tone_scores = array_fill_keys(array_keys($tone_markers), 0);
        
        foreach ($words as $word) {
            foreach ($tone_markers as $tone => $markers) {
                if (in_array($word, $markers)) {
                    $tone_scores[$tone]++;
                }
            }
        }
        
        return array_search(max($tone_scores), $tone_scores) ?: 'neutral';
    }

    /**
     * Detect emotion from text
     */
    private function detect_emotion($words) {
        $emotion_markers = [
            'happy' => ['happy', 'glad', 'excited', 'pleased', 'delighted', 'joy'],
            'frustrated' => ['frustrated', 'angry', 'upset', 'annoyed', 'irritated'],
            'confused' => ['confused', 'unsure', 'unclear', 'lost', 'help'],
            'neutral' => ['okay', 'fine', 'normal', 'regular']
        ];
        
        $emotion_scores = array_fill_keys(array_keys($emotion_markers), 0);
        
        foreach ($words as $word) {
            foreach ($emotion_markers as $emotion => $markers) {
                if (in_array($word, $markers)) {
                    $emotion_scores[$emotion]++;
                }
            }
        }
        
        // Get the emotion with the highest score
        $max_score = max($emotion_scores);
        if ($max_score === 0) {
            return 'neutral';
        }
        
        return array_search($max_score, $emotion_scores);
    }

    /**
     * Detect formality level
     */
    private function detect_formality($words) {
        $formality_markers = [
            'formal' => ['kindly', 'please', 'would', 'could', 'sincerely', 'regards'],
            'informal' => ['hey', 'hi', 'thanks', 'cool', 'awesome', 'great'],
            'technical' => ['implement', 'configure', 'system', 'process', 'documentation']
        ];
        
        $formality_scores = array_fill_keys(array_keys($formality_markers), 0);
        
        foreach ($words as $word) {
            foreach ($formality_markers as $level => $markers) {
                if (in_array($word, $markers)) {
                    $formality_scores[$level]++;
                }
            }
        }
        
        $max_score = max($formality_scores);
        if ($max_score === 0) {
            return 'neutral';
        }
        
        return array_search($max_score, $formality_scores);
    }

    /**
     * Generate a rule-based response with improved pattern matching
     */
    public function generate_rule_based_response($query) {
        if (empty($query)) {
            return false;
        }
        
        $query_lower = strtolower(trim($query));
        $analysis = $this->analyze_query($query);
        
        // Intent-based responses
        if ($analysis['intent'] === 'question') {
            if (strpos($query_lower, 'what is') === 0 || strpos($query_lower, 'what are') === 0) {
                // Definition questions
                return false; // Let the knowledge base handle these
            }
            
            if (strpos($query_lower, 'how do i') === 0 || strpos($query_lower, 'how to') === 0) {
                // How-to questions
                return false; // Let the knowledge base handle these
            }
        }
        
        // Greeting patterns
        $greetings = ['hello', 'hi ', 'hey', 'greetings', 'good morning', 'good afternoon', 'good evening'];
        foreach ($greetings as $greeting) {
            if (strpos($query_lower, $greeting) !== false) {
                return 'Hello! How can I help you today?';
            }
        }
        
        // Gratitude patterns
        if (strpos($query_lower, 'thank') !== false || strpos($query_lower, 'thanks') !== false) {
            return 'You\'re welcome! Is there anything else I can help you with?';
        }
        
        // Help requests
        if ($query_lower === 'help' || $query_lower === 'i need help') {
            return 'I\'m here to help! Please let me know what you\'re looking for, and I\'ll do my best to assist you.';
        }
        
        // Farewell patterns
        $farewells = ['bye', 'goodbye', 'see you', 'talk to you later', 'have a good day'];
        foreach ($farewells as $farewell) {
            if (strpos($query_lower, $farewell) !== false) {
                return 'Goodbye! Feel free to come back if you have more questions.';
            }
        }
        
        return false;
    }
} 
