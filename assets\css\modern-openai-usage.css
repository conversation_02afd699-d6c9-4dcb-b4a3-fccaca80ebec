/**
 * Modern OpenAI Usage CSS
 *
 * Enhances the Q Knowledge Base OpenAI usage page with a modern, clean design
 */

/* Import Inter font if not already imported */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Main container styling */
.wrap {
    max-width: 1200px;
    margin: 20px auto;
    font-family: var(--qkb-font, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
}

.wrap h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 25px;
    color: var(--qkb-text-dark, #1f2328);
    padding-bottom: 15px;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
}

/* Usage dashboard container */
.qkb-usage-dashboard {
    margin-top: 20px;
}

/* Usage summary section */
.qkb-usage-summary {
    background: #fff;
    border-radius: var(--qkb-radius-md, 15px);
    box-shadow: var(--qkb-shadow-sm, 0 5px 10px rgba(0, 0, 0, 0.1));
    padding: 24px;
    margin-bottom: 30px;
    border: 1px solid var(--qkb-border, #e1e4e8);
}

.qkb-usage-summary h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 15px 0;
    color: var(--qkb-text-dark, #1f2328);
    padding-bottom: 12px;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
}

.qkb-usage-summary p {
    color: var(--qkb-text-light, #646970);
    font-size: 14px;
    margin-bottom: 20px;
}

/* Usage cards grid */
.qkb-usage-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.qkb-usage-card {
    background: var(--qkb-bg, #ffffff);
    border-radius: var(--qkb-radius-sm, 10px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 20px;
    border: 1px solid var(--qkb-border, #e1e4e8);
    transition: all 0.3s ease;
}

.qkb-usage-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--qkb-shadow-sm, 0 5px 10px rgba(0, 0, 0, 0.1));
}

.qkb-usage-card h3 {
    margin-top: 0;
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 500;
    color: var(--qkb-text-light, #646970);
}

.qkb-usage-value {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--qkb-text-dark, #1f2328);
}

.qkb-usage-subtitle {
    font-size: 13px;
    color: var(--qkb-text-light, #646970);
}

/* First card styling - total tokens */
.qkb-usage-card:first-child {
    background: var(--qkb-primary, #2271b1);
    color: white;
}

.qkb-usage-card:first-child h3,
.qkb-usage-card:first-child .qkb-usage-value,
.qkb-usage-card:first-child .qkb-usage-subtitle {
    color: white;
}

/* Monthly usage section */
.qkb-monthly-usage {
    background: #fff;
    border-radius: var(--qkb-radius-md, 15px);
    box-shadow: var(--qkb-shadow-sm, 0 5px 10px rgba(0, 0, 0, 0.1));
    padding: 24px;
    margin-bottom: 30px;
    border: 1px solid var(--qkb-border, #e1e4e8);
}

.qkb-monthly-usage h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
    color: var(--qkb-text-dark, #1f2328);
    padding-bottom: 12px;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
}

/* Table styling */
.qkb-monthly-usage table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border: 1px solid var(--qkb-border, #e1e4e8);
    border-radius: var(--qkb-radius-sm, 10px);
    overflow: hidden;
    margin-top: 10px;
}

.qkb-monthly-usage table thead th {
    background: var(--qkb-bg-light, #f6f7f7);
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
    color: var(--qkb-text-dark, #1f2328);
    font-weight: 600;
    padding: 12px 16px;
    text-align: left;
    font-size: 14px;
}

.qkb-monthly-usage table tbody td {
    padding: 14px 16px;
    vertical-align: middle;
    border-bottom: 1px solid var(--qkb-border-light, #f0f0f0);
    color: var(--qkb-text, #2c3338);
    font-size: 14px;
}

.qkb-monthly-usage table tbody tr:last-child td {
    border-bottom: none;
}

.qkb-monthly-usage table tbody tr:hover {
    background-color: var(--qkb-bg-light, #f6f7f7);
}

/* Actions section */
.qkb-usage-actions {
    background: #fff;
    border-radius: var(--qkb-radius-md, 15px);
    box-shadow: var(--qkb-shadow-sm, 0 5px 10px rgba(0, 0, 0, 0.1));
    padding: 24px;
    margin-bottom: 30px;
    border: 1px solid var(--qkb-border, #e1e4e8);
    text-align: right;
}

.qkb-usage-actions button {
    background: var(--qkb-bg-light, #f6f7f7);
    border: 1px solid var(--qkb-border, #e1e4e8);
    color: var(--qkb-text, #2c3338);
    padding: 8px 16px;
    border-radius: var(--qkb-radius-sm, 10px);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.qkb-usage-actions button:hover {
    background: #f1f1f1;
    border-color: #d5d5d5;
    color: var(--qkb-error, #dc2626);
}

/* Help section */
.qkb-usage-help {
    background: #f8fafc;
    border-radius: var(--qkb-radius-md, 15px);
    padding: 20px;
    border: 1px solid var(--qkb-border, #e1e4e8);
    border-left: 4px solid var(--qkb-primary, #2271b1);
}

.qkb-usage-help h3 {
    margin-top: 0;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
    color: var(--qkb-text-dark, #1f2328);
}

.qkb-usage-help p {
    color: var(--qkb-text, #2c3338);
    font-size: 14px;
    margin-bottom: 10px;
    line-height: 1.5;
}

.qkb-usage-help p:last-child {
    margin-bottom: 0;
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
    .qkb-usage-cards {
        grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
    }
    
    .qkb-usage-summary,
    .qkb-monthly-usage,
    .qkb-usage-actions,
    .qkb-usage-help {
        padding: 16px;
    }
    
    .qkb-monthly-usage table {
        display: block;
        overflow-x: auto;
    }
}
