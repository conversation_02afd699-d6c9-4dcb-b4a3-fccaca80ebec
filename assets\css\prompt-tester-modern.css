/**
 * Modern Prompt Tester CSS
 */

/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Main container */
.qkb-prompt-tester-container {
    display: flex;
    margin-top: 20px;
    gap: 24px;
    font-family: 'Inter', sans-serif;
    max-width: 1600px;
}

/* Sidebar */
.qkb-prompt-tester-sidebar {
    flex: 0 0 350px;
    max-width: 350px;
}

/* Main content */
.qkb-prompt-tester-main {
    flex: 1;
}

/* Form styling */
.qkb-prompt-tester-form {
    background: var(--qkb-bg, #ffffff);
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid var(--qkb-border-light, #e9ecef);
}

.qkb-prompt-tester-form:hover {
    transform: translateY(-2px);
}

.qkb-prompt-tester-form h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    color: var(--qkb-text, #212529);
    padding-bottom: 12px;
    border-bottom: 1px solid var(--qkb-border-light, #e9ecef);
}

.qkb-form-group {
    margin-bottom: 20px;
}

.qkb-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--qkb-text, #212529);
    font-size: 14px;
}

.qkb-form-group textarea,
.qkb-form-group select,
.qkb-form-group input[type="text"] {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--qkb-border, #dee2e6);
    background: var(--qkb-bg-light, #f8f9fa);
    color: var(--qkb-text, #212529);
    font-family: 'Inter', sans-serif;
    font-size: 14px;
}

.qkb-form-group textarea:focus,
.qkb-form-group select:focus,
.qkb-form-group input[type="text"]:focus {
    border-color: var(--qkb-accent-blue, #4285f4);
    outline: none;
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.2);
    background: var(--qkb-bg, #ffffff);
}

.qkb-form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.qkb-form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236c757d' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 40px;
}

.qkb-form-group .description {
    margin-top: 8px;
    font-size: 12px;
    color: var(--qkb-text-light, #6c757d);
}

/* Checkbox styling */
.qkb-form-group input[type="checkbox"] {
    margin-right: 8px;
    position: relative;
    top: 2px;
}

/* Form actions */
.qkb-form-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
    justify-content: flex-end;
}

.qkb-form-actions button {
    padding: 10px 16px;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.qkb-form-actions button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.qkb-form-actions button:hover {
    transform: translateY(-2px);
}

.qkb-form-actions button:hover .dashicons {
    transform: scale(1.1);
}

.qkb-form-actions button:active {
    transform: translateY(0);
}

.qkb-form-actions .button-secondary {
    background: var(--qkb-bg-light, #f8f9fa);
    color: var(--qkb-text, #212529);
    border: 1px solid var(--qkb-border, #dee2e6);
}

.qkb-form-actions .button-secondary:hover {
    background: var(--qkb-bg-hover, #e9ecef);
}

.qkb-form-actions .button-primary {
    background: var(--qkb-accent-blue, #4285f4);
    color: white;
    position: relative;
    overflow: hidden;
}

.qkb-form-actions .button-primary:hover {
    background: var(--qkb-primary-dark, #1a73e8);
}

.qkb-form-actions .button-primary::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(120deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
    transform: translateX(-100%);
}

.qkb-form-actions .button-primary:hover::after {
    transform: translateX(100%);
}

/* Token estimation */
.qkb-token-estimation {
    background: var(--qkb-bg, #ffffff);
    padding: 24px;
    border: 1px solid var(--qkb-border-light, #e9ecef);
}

.qkb-token-estimation h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: var(--qkb-text, #212529);
    padding-bottom: 12px;
    border-bottom: 1px solid var(--qkb-border-light, #e9ecef);
}

.qkb-token-stats {
    margin-top: 16px;
}

.qkb-token-stat {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
    padding: 8px 12px;
    background: var(--qkb-bg-light, #f8f9fa);
}

.qkb-token-stat:nth-child(odd) {
    background: var(--qkb-bg-alt, #f0f2f5);
}

.qkb-token-total {
    margin-top: 12px;
    padding: 10px 12px;
    border-top: 1px solid var(--qkb-border-light, #e9ecef);
    font-weight: 600;
    background: var(--qkb-accent-blue-light, #e8f0fe) !important;
    color: var(--qkb-accent-blue, #4285f4);
}

.qkb-token-warning {
    margin-top: 16px;
    padding: 12px;
    background: var(--qkb-accent-blue-light, #e8f0fe);
    border-left: 4px solid var(--qkb-accent-blue, #4285f4);
    color: var(--qkb-text, #212529);
    font-size: 14px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 0.8; }
    50% { opacity: 1; }
    100% { opacity: 0.8; }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.qkb-form-actions button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.qkb-form-actions button:disabled .dashicons {
    animation: spin 1.5s linear infinite;
}

/* Prompt preview and response containers */
.qkb-prompt-preview-container,
.qkb-response-container {
    background: var(--qkb-bg, #ffffff);
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid var(--qkb-border-light, #e9ecef);
}

.qkb-prompt-preview-container h2,
.qkb-response-container h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    color: var(--qkb-text, #212529);
    padding-bottom: 12px;
    border-bottom: 1px solid var(--qkb-border-light, #e9ecef);
}

.qkb-prompt-section {
    margin-bottom: 24px;
}

.qkb-prompt-section h3 {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--qkb-border-light, #e9ecef);
    font-size: 16px;
    font-weight: 500;
    color: var(--qkb-text-light, #6c757d);
}

/* CodeMirror customizations */
.CodeMirror {
    height: auto;
    min-height: 150px;
    border: 1px solid var(--qkb-border-light, #e9ecef);
    font-family: 'Inter', monospace;
    font-size: 14px;
    line-height: 1.6;
    background: var(--qkb-bg-light, #f8f9fa);
}



.CodeMirror-focused {
    border-color: var(--qkb-accent-blue, #4285f4);
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.2);
}

.CodeMirror-scroll {
    min-height: 150px;
}

.CodeMirror-gutters {
    border-right: 1px solid var(--qkb-border-light, #e9ecef);
    background: var(--qkb-bg-alt, #f0f2f5);
}

.qkb-response-placeholder {
    padding: 20px;
    color: var(--qkb-text-muted, #adb5bd);
    font-style: italic;
    text-align: center;
    background: var(--qkb-bg-light, #f8f9fa);
    border: 1px dashed var(--qkb-border, #dee2e6);
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .qkb-prompt-tester-container {
        flex-direction: column;
    }

    .qkb-prompt-tester-sidebar {
        flex: 0 0 100%;
        max-width: 100%;
    }
}
