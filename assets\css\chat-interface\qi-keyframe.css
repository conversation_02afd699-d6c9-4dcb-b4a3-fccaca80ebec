@keyframes qkbDotBounce {
  0%,
  60%,
  100% {
    transform: translateY(0);
    opacity: 0.4;
  }

  30% {
    transform: translateY(-4px);
    opacity: 1;
  }
}

@keyframes wave {
  0% {
    transform: rotate(0deg);
  }

  10% {
    transform: rotate(14deg);
  }

  20% {
    transform: rotate(-8deg);
  }

  30% {
    transform: rotate(14deg);
  }

  40% {
    transform: rotate(-4deg);
  }

  50% {
    transform: rotate(10deg);
  }

  60% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-150%);
  }

  50% {
    transform: translateX(150%);
  }

  100% {
    transform: translateX(150%);
  }
}

@keyframes qipulse {
  0%,
  100% {
    opacity: 0.5;
  }

  50% {
    opacity: 0.8;
  }
}

@keyframes button-click {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.95);
    background-color: rgba(var(--qkb-primary-rgb, 59, 130, 246), 0.2);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes button-pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}



@keyframes pulse-indicator {
  0% {
    opacity: 0.3;
    transform: scale(0.8);
  }

  50% {
    opacity: 1;
    transform: scale(1.2);
  }

  100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
}

@keyframes qkbSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes tools-pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}



@keyframes send-pulse {
  0% {
    transform: scale(1) rotate(0);
  }

  10% {
    transform: scale(0.9) rotate(0);
  }

  30% {
    transform: scale(1.1) rotate(10deg);
  }

  50% {
    transform: scale(1) rotate(45deg);
  }

  70% {
    transform: scale(1) rotate(90deg);
    opacity: 0.8;
  }

  100% {
    transform: scale(1) rotate(0);
    opacity: 1;
  }
}

@keyframes prompt-button-click {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.95);
    background-color: rgba(var(--qkb-primary-rgb, 59, 130, 246), 0.2);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes floatIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes send-ready-pulse {
  0% {
    box-shadow: var(--qkb-shadow-md);
  }

  50% {
    box-shadow: 0 0 0 4px rgba(var(--qkb-primary-rgb, 59, 130, 246), 0.2);
  }

  100% {
    box-shadow: var(--qkb-shadow-md);
  }
}

@keyframes messageAppear {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes messagePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--qkb-primary-rgb, 59, 130, 246), 0.4);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(var(--qkb-primary-rgb, 59, 130, 246), 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(var(--qkb-primary-rgb, 59, 130, 246), 0);
  }
}

@keyframes pulse-success {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes pulse-success {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
    background-color: rgba(var(--qkb-success-rgb, 22, 163, 74), 0.2);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes pulse-error {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
    background-color: rgba(var(--qkb-error-rgb, 220, 38, 38), 0.2);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translateY(5px);
  }

  10% {
    opacity: 1;
    transform: translateY(0);
  }

  70% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    transform: translateY(-5px);
  }
}

@keyframes loading-bar {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}
