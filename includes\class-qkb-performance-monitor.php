<?php

/**
 * Performance monitoring class for Q-Knowledge Base
 * Tracks response times and performance metrics
 */
class QKB_Performance_Monitor {
    
    private static $instance = null;
    private $metrics = [];
    private $start_times = [];
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Initialize performance monitoring
     */
    public function __construct() {
        add_action('init', [$this, 'init_monitoring']);
        add_action('wp_ajax_qkb_chat_request', [$this, 'start_chat_timer'], 1);
        add_action('wp_ajax_nopriv_qkb_chat_request', [$this, 'start_chat_timer'], 1);
        add_action('shutdown', [$this, 'save_metrics']);
    }
    
    /**
     * Initialize monitoring hooks
     */
    public function init_monitoring() {
        // Monitor database queries
        if (defined('SAVEQUERIES') && SAVEQUERIES) {
            add_filter('query', [$this, 'monitor_query']);
        }
        
        // Monitor memory usage
        $this->record_memory_usage('init');
    }
    
    /**
     * Start timing for chat requests
     */
    public function start_chat_timer() {
        $this->start_timer('chat_request');
        $this->record_memory_usage('chat_start');
    }
    
    /**
     * Start a performance timer
     */
    public function start_timer($key) {
        $this->start_times[$key] = microtime(true);
    }
    
    /**
     * End a performance timer and record the duration
     */
    public function end_timer($key, $context = []) {
        if (!isset($this->start_times[$key])) {
            return false;
        }
        
        $duration = microtime(true) - $this->start_times[$key];
        unset($this->start_times[$key]);
        
        $this->record_metric($key, $duration, array_merge($context, [
            'timestamp' => time(),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ]));
        
        return $duration;
    }
    
    /**
     * Record a performance metric
     */
    public function record_metric($key, $value, $context = []) {
        if (!isset($this->metrics[$key])) {
            $this->metrics[$key] = [];
        }
        
        $this->metrics[$key][] = [
            'value' => $value,
            'context' => $context,
            'timestamp' => time()
        ];
        
        // Keep only last 100 entries per metric to prevent memory issues
        if (count($this->metrics[$key]) > 100) {
            $this->metrics[$key] = array_slice($this->metrics[$key], -100);
        }
    }
    
    /**
     * Record memory usage
     */
    public function record_memory_usage($context) {
        $this->record_metric('memory_usage', memory_get_usage(true), [
            'context' => $context,
            'peak_memory' => memory_get_peak_usage(true)
        ]);
    }
    
    /**
     * Monitor database queries
     */
    public function monitor_query($query) {
        $start_time = microtime(true);
        
        // Hook into query completion (this is a simplified approach)
        add_action('shutdown', function() use ($query, $start_time) {
            $duration = microtime(true) - $start_time;
            
            // Only log slow queries (> 0.1 seconds)
            if ($duration > 0.1) {
                $this->record_metric('slow_query', $duration, [
                    'query' => substr($query, 0, 200), // Truncate for storage
                    'query_type' => $this->get_query_type($query)
                ]);
            }
        }, 1);
        
        return $query;
    }
    
    /**
     * Get query type from SQL
     */
    private function get_query_type($query) {
        $query = trim(strtoupper($query));
        if (strpos($query, 'SELECT') === 0) return 'SELECT';
        if (strpos($query, 'INSERT') === 0) return 'INSERT';
        if (strpos($query, 'UPDATE') === 0) return 'UPDATE';
        if (strpos($query, 'DELETE') === 0) return 'DELETE';
        return 'OTHER';
    }
    
    /**
     * Get performance statistics
     */
    public function get_stats($metric_key = null) {
        if ($metric_key) {
            return isset($this->metrics[$metric_key]) ? $this->metrics[$metric_key] : [];
        }
        
        $stats = [];
        
        foreach ($this->metrics as $key => $values) {
            if (empty($values)) continue;
            
            $durations = array_column($values, 'value');
            $stats[$key] = [
                'count' => count($durations),
                'avg' => array_sum($durations) / count($durations),
                'min' => min($durations),
                'max' => max($durations),
                'total' => array_sum($durations),
                'recent' => array_slice($values, -10) // Last 10 entries
            ];
        }
        
        return $stats;
    }
    
    /**
     * Get performance summary
     */
    public function get_performance_summary() {
        $stats = $this->get_stats();
        $summary = [
            'overall_health' => 'good',
            'issues' => [],
            'recommendations' => []
        ];
        
        // Check chat request performance
        if (isset($stats['chat_request'])) {
            $chat_stats = $stats['chat_request'];
            if ($chat_stats['avg'] > 5.0) {
                $summary['overall_health'] = 'poor';
                $summary['issues'][] = 'Chat requests are taking too long (avg: ' . round($chat_stats['avg'], 2) . 's)';
                $summary['recommendations'][] = 'Consider optimizing database queries or increasing server resources';
            } elseif ($chat_stats['avg'] > 3.0) {
                $summary['overall_health'] = 'fair';
                $summary['issues'][] = 'Chat requests are slower than optimal (avg: ' . round($chat_stats['avg'], 2) . 's)';
                $summary['recommendations'][] = 'Review caching strategies and database optimization';
            }
        }
        
        // Check for slow queries
        if (isset($stats['slow_query']) && $stats['slow_query']['count'] > 0) {
            $summary['issues'][] = 'Detected ' . $stats['slow_query']['count'] . ' slow database queries';
            $summary['recommendations'][] = 'Review and optimize database queries, consider adding indexes';
        }
        
        // Check memory usage
        if (isset($stats['memory_usage'])) {
            $memory_stats = $stats['memory_usage'];
            $avg_memory_mb = $memory_stats['avg'] / 1024 / 1024;
            if ($avg_memory_mb > 128) {
                $summary['issues'][] = 'High memory usage detected (avg: ' . round($avg_memory_mb, 2) . 'MB)';
                $summary['recommendations'][] = 'Consider optimizing memory usage or increasing PHP memory limit';
            }
        }
        
        return $summary;
    }
    
    /**
     * Save metrics to database
     */
    public function save_metrics() {
        if (empty($this->metrics)) {
            return;
        }
        
        // Save aggregated metrics to options table
        $current_stats = get_option('qkb_performance_stats', []);
        
        foreach ($this->metrics as $key => $values) {
            if (!isset($current_stats[$key])) {
                $current_stats[$key] = [
                    'total_count' => 0,
                    'total_duration' => 0,
                    'max_duration' => 0,
                    'last_updated' => time()
                ];
            }
            
            $durations = array_column($values, 'value');
            $current_stats[$key]['total_count'] += count($durations);
            $current_stats[$key]['total_duration'] += array_sum($durations);
            $current_stats[$key]['max_duration'] = max($current_stats[$key]['max_duration'], max($durations));
            $current_stats[$key]['last_updated'] = time();
        }
        
        update_option('qkb_performance_stats', $current_stats);
        
        // Clean up old data (keep only last 7 days)
        $this->cleanup_old_stats();
    }
    
    /**
     * Clean up old performance statistics
     */
    private function cleanup_old_stats() {
        $stats = get_option('qkb_performance_stats', []);
        $cutoff_time = time() - (7 * DAY_IN_SECONDS);
        
        foreach ($stats as $key => $data) {
            if (isset($data['last_updated']) && $data['last_updated'] < $cutoff_time) {
                unset($stats[$key]);
            }
        }
        
        update_option('qkb_performance_stats', $stats);
    }
    
    /**
     * Get performance dashboard data
     */
    public function get_dashboard_data() {
        $stats = get_option('qkb_performance_stats', []);
        $current_stats = $this->get_stats();
        $summary = $this->get_performance_summary();
        
        return [
            'current_session' => $current_stats,
            'historical' => $stats,
            'summary' => $summary,
            'last_updated' => time()
        ];
    }
    
    /**
     * Reset performance statistics
     */
    public function reset_stats() {
        $this->metrics = [];
        delete_option('qkb_performance_stats');
    }
    
    /**
     * Log performance alert
     */
    public function log_performance_alert($message, $context = []) {
        error_log('QKB Performance Alert: ' . $message . ' | Context: ' . json_encode($context));
        
        // Store alert for dashboard
        $alerts = get_option('qkb_performance_alerts', []);
        $alerts[] = [
            'message' => $message,
            'context' => $context,
            'timestamp' => time()
        ];
        
        // Keep only last 50 alerts
        if (count($alerts) > 50) {
            $alerts = array_slice($alerts, -50);
        }
        
        update_option('qkb_performance_alerts', $alerts);
    }
}

// Initialize performance monitoring
QKB_Performance_Monitor::get_instance();
