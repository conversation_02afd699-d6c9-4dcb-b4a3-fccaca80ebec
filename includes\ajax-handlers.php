<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AJAX handlers for the assistant builder
 */
class QKB_AJAX_Handlers
{
    /**
     * Instance of this class
     */
    private static $instance = null;

    /**
     * Get the instance of this class
     */
    public static function get_instance()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct()
    {
        // Add AJAX handlers
        add_action('wp_ajax_qkb_get_terms', [$this, 'ajax_get_terms']);
        add_action('wp_ajax_qkb_get_knowledge_content', [$this, 'ajax_get_knowledge_content']);
        add_action('wp_ajax_qkb_delete_assistant', [$this, 'ajax_delete_assistant']);

        // Add AJAX handler for getting assistant info
        add_action('wp_ajax_qkb_get_assistant_info', [$this, 'ajax_get_assistant_info']);
        add_action('wp_ajax_nopriv_qkb_get_assistant_info', [$this, 'ajax_get_assistant_info']);



        add_action('wp_ajax_qkb_submit_formidable_form', [$this, 'ajax_submit_formidable_form']);
        add_action('wp_ajax_nopriv_qkb_submit_formidable_form', [$this, 'ajax_submit_formidable_form']);

        // Hook into Formidable Forms submission process
        add_action('frm_after_create_entry', array($this, 'after_form_submit'), 30, 2);
    }

    /**
     * AJAX handler for getting terms
     */
    public function ajax_get_terms()
    {
        // Check permissions
        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Permission denied');
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Get taxonomy
        $taxonomy = isset($_POST['taxonomy']) ? sanitize_text_field($_POST['taxonomy']) : '';
        if (empty($taxonomy)) {
            wp_send_json_error('Taxonomy is required');
        }

        // Get terms
        $terms = get_terms([
            'taxonomy' => $taxonomy,
            'hide_empty' => false
        ]);

        if (is_wp_error($terms)) {
            wp_send_json_error($terms->get_error_message());
        }

        wp_send_json_success(['terms' => $terms]);
    }

    /**
     * AJAX handler for getting knowledge content
     */
    public function ajax_get_knowledge_content()
    {
        // Check permissions
        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Permission denied');
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Get knowledge content
        $args = [
            'post_type' => ['kb_knowledge_base', 'kb_external_content'],
            'posts_per_page' => -1,
            'post_status' => 'publish'
        ];

        $query = new WP_Query($args);
        $content = [];

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $content[] = [
                    'id' => get_the_ID(),
                    'title' => get_the_title(),
                    'type' => get_post_type() === 'kb_knowledge_base' ? 'Knowledge Base' : 'External Content'
                ];
            }
        }

        wp_reset_postdata();

        wp_send_json_success(['content' => $content]);
    }



    /**
     * AJAX handler for deleting an assistant
     */
    public function ajax_delete_assistant()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Get assistant ID
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;
        if (empty($assistant_id)) {
            wp_send_json_error('Assistant ID is required');
        }

        // Trigger action before deleting assistant
        do_action('qkb_before_assistant_delete', $assistant_id);

        // Delete assistant
        $result = wp_delete_term($assistant_id, 'kb_assistant');

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(['message' => __('Assistant deleted successfully', 'q-knowledge-base')]);
        }
    }

    /**
     * AJAX handler for getting assistant info
     */
    public function ajax_get_assistant_info()
    {
        // Check permissions
        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Permission denied');
        }

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_ajax_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Get assistant ID
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;

        if (!$assistant_id) {
            wp_send_json_error('Invalid assistant ID');
        }

        // Get assistant
        $assistant = get_term($assistant_id, 'kb_assistant');

        if (is_wp_error($assistant) || !$assistant) {
            wp_send_json_error('Assistant not found');
        }

        // Get assistant meta
        $icon = get_term_meta($assistant_id, 'assistant_icon', true);
        $description = get_term_meta($assistant_id, 'assistant_description', true) ?: $assistant->description;
        $model = get_term_meta($assistant_id, 'assistant_model', true);
        $template = get_term_meta($assistant_id, 'assistant_template', true);
        $openai_id = get_term_meta($assistant_id, 'assistant_openai_id', true);
        $knowledge = get_term_meta($assistant_id, 'assistant_knowledge', true) ?: [];
        $suggested_prompts = get_term_meta($assistant_id, 'assistant_suggested_prompts', true) ?: [];

        // Return assistant data
        wp_send_json_success([
            'id' => $assistant_id,
            'name' => $assistant->name,
            'description' => $description,
            'icon' => $icon,
            'model' => $model,
            'template' => $template,
            'openai_id' => $openai_id,
            'knowledge' => $knowledge,
            'suggested_prompts' => $suggested_prompts
        ]);
    }

    /**
     * AJAX handler for searching the knowledge base
     */
    public function ajax_search_knowledge_base()
    {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'qkb_ajax_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Get query
        $query = isset($_POST['query']) ? sanitize_text_field($_POST['query']) : '';
        if (empty($query)) {
            wp_send_json_error('Query is required');
            return;
        }

        // Search knowledge base
        $args = [
            'post_type' => ['kb_knowledge_base', 'post', 'page'],
            'post_status' => 'publish',
            's' => $query,
            'posts_per_page' => 5
        ];

        $search_query = new WP_Query($args);
        $results = [];

        if ($search_query->have_posts()) {
            while ($search_query->have_posts()) {
                $search_query->the_post();
                $results[] = [
                    'id' => get_the_ID(),
                    'title' => get_the_title(),
                    'url' => get_permalink(),
                    'snippet' => wp_trim_words(strip_tags(get_the_content()), 30),
                    'type' => get_post_type() === 'kb_knowledge_base' ? 'Knowledge Base' : 'Content'
                ];
            }
        }

        wp_reset_postdata();

        wp_send_json_success($results);
    }





    /**
     * AJAX handler for submitting a Formidable Form
     */
    public function ajax_submit_formidable_form()
    {
        // Log the request for debugging
        error_log('AJAX request received for qkb_submit_formidable_form');
        error_log('POST data: ' . print_r($_POST, true));

        // Verify nonce - make this optional since we might be handling direct form submissions
        $nonce_verified = false;
        if (isset($_POST['nonce'])) {
            $nonce_value = $_POST['nonce'];
            $is_ajax_nonce_valid = wp_verify_nonce($nonce_value, 'qkb_ajax_nonce');
            $is_chat_nonce_valid = wp_verify_nonce($nonce_value, 'qi_chat_nonce');

            if ($is_ajax_nonce_valid || $is_chat_nonce_valid) {
                $nonce_verified = true;
            } else {
                error_log('Invalid nonce: ' . $nonce_value);
            }
        }

        // Get form ID - try multiple sources
        $form_id = 0;

        // First check direct form_id parameter
        if (isset($_POST['form_id'])) {
            $form_id = intval($_POST['form_id']);
        }

        // If not found, check in form_data
        if (empty($form_id) && isset($_POST['form_data']) && isset($_POST['form_data']['form_id'])) {
            $form_id = intval($_POST['form_data']['form_id']);
        }

        // If still not found, check in item_meta
        if (empty($form_id) && isset($_POST['item_meta']) && is_array($_POST['item_meta'])) {
            // This is a direct Formidable Forms submission
            // Process it using Formidable's own handler
            error_log('Direct Formidable Forms submission detected');

            // Check if Formidable Forms is active
            if (class_exists('FrmEntriesController')) {
                // Let Formidable handle it
                error_log('Letting Formidable Forms handle the submission directly');

                // We'll hook into frm_after_create_entry to capture the result
                add_action('frm_after_create_entry', array($this, 'after_form_submit'), 30, 2);

                // Return without sending JSON response - let Formidable handle it
                return;
            }
        }

        // If we still don't have a form ID, try to find it in other POST data
        if (empty($form_id)) {
            foreach ($_POST as $key => $value) {
                if (preg_match('/^form_id_(\d+)$/', $key, $matches) || $key === 'form_id') {
                    $form_id = intval($value);
                    break;
                }
            }
        }

        // If we still don't have a form ID, error out
        if (empty($form_id)) {
            error_log('Form ID could not be determined from request');
            wp_send_json_error('Form ID is required');
            return;
        }

        error_log('Processing form ID: ' . $form_id);

        // Check if Formidable Forms is active
        if (!class_exists('FrmEntry')) {
            error_log('FrmEntry class not found - Formidable Forms may not be active');
            wp_send_json_error('Formidable Forms is not active');
            return;
        }

        try {
            // Prepare form data
            $entry = array();
            $entry['form_id'] = $form_id;
            $entry['item_meta'] = array();

            // First check if we have direct item_meta from Formidable
            if (isset($_POST['item_meta']) && is_array($_POST['item_meta'])) {
                foreach ($_POST['item_meta'] as $field_id => $value) {
                    if (is_array($value)) {
                        $entry['item_meta'][$field_id] = array_map('sanitize_text_field', $value);
                    } else {
                        $entry['item_meta'][$field_id] = sanitize_text_field($value);
                    }
                }
            }
            // Then check our custom form_data format
            else if (isset($_POST['form_data']) && is_array($_POST['form_data'])) {
                foreach ($_POST['form_data'] as $key => $value) {
                    if (strpos($key, 'field_') === 0) {
                        $field_id = str_replace('field_', '', $key);

                        if (is_array($value)) {
                            $entry['item_meta'][$field_id] = array_map('sanitize_text_field', $value);
                        } else {
                            $entry['item_meta'][$field_id] = sanitize_text_field($value);
                        }
                    }
                }
            }
            // Finally, try to parse all POST data for field values
            else {
                foreach ($_POST as $key => $value) {
                    // Check for standard Formidable field format
                    if (strpos($key, 'item_meta[') === 0) {
                        preg_match('/item_meta\[(\d+)\]/', $key, $matches);
                        if (isset($matches[1])) {
                            $field_id = $matches[1];
                            $entry['item_meta'][$field_id] = sanitize_text_field($value);
                        }
                    }
                    // Check for our custom field_ format
                    else if (strpos($key, 'field_') === 0) {
                        $field_id = str_replace('field_', '', $key);
                        $entry['item_meta'][$field_id] = sanitize_text_field($value);
                    }
                }
            }

            // If we have no field data, error out
            if (empty($entry['item_meta'])) {
                error_log('No field data found in the request');
                wp_send_json_error('No form field data found');
                return;
            }

            // Log the prepared entry
            error_log('Prepared entry: ' . print_r($entry, true));

            // Create entry
            $entry_id = FrmEntry::create($entry);

            if (is_wp_error($entry_id)) {
                error_log('Error creating entry: ' . $entry_id->get_error_message());
                wp_send_json_error($entry_id->get_error_message());
                return;
            }

            // Log success
            error_log('Form submitted successfully. Entry ID: ' . $entry_id);

            // Get form settings to check for success message
            $form = FrmForm::getOne($form_id);
            $success_message = '';

            if ($form && isset($form->options['success_msg'])) {
                $success_message = $form->options['success_msg'];
            }

            wp_send_json_success([
                'entry_id' => $entry_id,
                'message' => $success_message ?: 'Form submitted successfully!'
            ]);

        } catch (Exception $e) {
            error_log('Exception in form submission: ' . $e->getMessage());
            wp_send_json_error('Error processing form: ' . $e->getMessage());
        }
    }


    /**
     * Handle successful form submission
     *
     * @param int $entry_id The ID of the newly created entry
     * @param int $form_id The ID of the form
     */
    public function after_form_submit($entry_id, $form_id)
    {
        // Log the successful submission regardless of context
        error_log('Formidable Form submitted successfully. Entry ID: ' . $entry_id . ', Form ID: ' . $form_id);

        // Check if this is from a quick action
        $is_quick_action = (
            isset($_POST['qkb_quick_action']) ||
            isset($_COOKIE['qkb_quick_action_active']) ||
            (isset($_POST['frm_ajax']) && $_POST['frm_ajax'] == 1)
        );

        if ($is_quick_action) {
            // Log the successful submission from quick action
            error_log('Formidable Form submitted successfully via quick action. Entry ID: ' . $entry_id . ', Form ID: ' . $form_id);

            // Get the form to retrieve success message
            if (class_exists('FrmForm')) {
                $form = FrmForm::getOne($form_id);
                $success_message = '';

                if ($form && isset($form->options['success_msg'])) {
                    $success_message = $form->options['success_msg'];
                } else {
                    $success_message = 'Form submitted successfully!';
                }

                // Check if we're in a quick action modal context
                $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
                $is_modal_context = (
                    strpos($referer, 'qkb-quick-action') !== false ||
                    isset($_POST['qkb_quick_action']) ||
                    isset($_COOKIE['qkb_quick_action_active'])
                );

                if ($is_modal_context) {
                    // Add custom JavaScript to replace the form with success message
                    add_filter('frm_success_filter', function ($success_message) {
                        $script = "
                        <script type='text/javascript'>
                        jQuery(document).ready(function($) {
                            // Find the form container
                            var $form = $('.frm_forms').closest('form');
                            if (!$form.length) {
                                $form = $('.frm_forms');
                            }

                            // Replace with success message
                            $form.replaceWith('<div class=\"qkb-form-success\">" . esc_js($success_message) . "</div>' +
                                '<button class=\"qkb-form-close-button\">Close</button>');

                            // Add close button handler
                            $('.qkb-form-close-button').on('click', function() {
                                $('.qkb-quick-access-modal').removeClass('active');
                            });
                        });
                        </script>
                        ";

                        return $success_message . $script;
                    });
                }
            }
        }
    }
}