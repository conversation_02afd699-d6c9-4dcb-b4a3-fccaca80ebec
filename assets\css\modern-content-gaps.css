/**
 * Modern Content Gap Analysis CSS
 *
 * Enhances the Q Knowledge Base content gap analysis page with a modern, clean design
 */

/* Import Inter font if not already imported */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Main container styling */
.wrap {
    max-width: 1200px;
    margin: 20px auto;
    font-family: var(--qkb-font, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
}

.wrap h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 25px;
    color: var(--qkb-text-dark, #1f2328);
    padding-bottom: 15px;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
}

/* Performance metrics grid */
.qkb-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0 30px;
}

.qkb-metric-card {
    background: #fff;
    padding: 24px;
    border-radius: var(--qkb-radius-md, 15px);
    box-shadow: var(--qkb-shadow-sm, 0 5px 10px rgba(0, 0, 0, 0.1));
    border: 1px solid var(--qkb-border, #e1e4e8);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.qkb-metric-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--qkb-shadow, 0 10px 25px rgba(0, 0, 0, 0.15));
}

.qkb-metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--qkb-primary, #2271b1), var(--qkb-primary-light, #2271b1ad));
}

.qkb-metric-card h3 {
    margin: 0 0 15px;
    color: var(--qkb-text-dark, #1f2328);
    font-size: 16px;
    font-weight: 600;
}

.qkb-stat {
    font-size: 28px;
    font-weight: 700;
    color: var(--qkb-primary, #2271b1);
    display: flex;
    align-items: center;
    gap: 12px;
}

.qkb-trend {
    font-size: 14px;
    padding: 6px 10px;
    border-radius: 20px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.qkb-trend.positive {
    background: rgba(22, 163, 74, 0.1);
    color: var(--qkb-success, #16a34a);
}

.qkb-trend.negative {
    background: rgba(220, 38, 38, 0.1);
    color: var(--qkb-error, #dc2626);
}

.qkb-description {
    font-size: 14px;
    font-weight: normal;
    color: var(--qkb-text-light, #646970);
    margin-top: 5px;
    display: block;
}

/* Content recommendations section */
.qkb-analytics-summary,
.qkb-content-recommendations {
    background: #fff;
    border-radius: var(--qkb-radius-md, 15px);
    box-shadow: var(--qkb-shadow-sm, 0 5px 10px rgba(0, 0, 0, 0.1));
    padding: 24px;
    margin-bottom: 30px;
    border: 1px solid var(--qkb-border, #e1e4e8);
}

.qkb-analytics-summary h2,
.qkb-content-recommendations h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 15px 0;
    color: var(--qkb-text-dark, #1f2328);
    padding-bottom: 12px;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
}

.qkb-content-recommendations p.description {
    color: var(--qkb-text-light, #646970);
    font-size: 14px;
    margin-bottom: 20px;
}

/* Table styling */
.qkb-content-recommendations table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border: 1px solid var(--qkb-border, #e1e4e8);
    border-radius: var(--qkb-radius-sm, 10px);
    overflow: hidden;
}

.qkb-content-recommendations th {
    background: var(--qkb-bg-light, #f6f7f7);
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: var(--qkb-text-dark, #1f2328);
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
    font-size: 14px;
}

.qkb-content-recommendations td {
    padding: 16px;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
    vertical-align: middle;
}

.qkb-content-recommendations tr:last-child td {
    border-bottom: none;
}

.qkb-content-recommendations tr:hover td {
    background: var(--qkb-bg-light, #f6f7f7);
}

/* Priority score styling */
.priority-score {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-weight: 600;
    color: #fff;
    font-size: 16px;
    transition: transform 0.2s ease;
}

.priority-score:hover {
    transform: scale(1.1);
}

.priority-score.high {
    background-color: var(--qkb-error, #dc2626);
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.2);
}

.priority-score.medium {
    background-color: var(--qkb-warning, #f59e0b);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.2);
}

.priority-score.low {
    background-color: var(--qkb-primary, #2271b1);
    box-shadow: 0 4px 8px rgba(34, 113, 177, 0.2);
}

/* Engagement metrics styling */
.engagement-metrics {
    font-size: 14px;
    line-height: 1.5;
    color: var(--qkb-text, #2c3338);
}

.engagement-metrics div {
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Button styling */
.qkb-content-recommendations .button {
    padding: 8px 12px;
    height: auto;
    font-size: 14px;
    font-weight: 500;
    border-radius: var(--qkb-radius-sm, 10px);
    transition: all 0.2s ease;
}

.qkb-content-recommendations .button-primary {
    background: var(--qkb-primary, #2271b1);
    border-color: var(--qkb-primary-dark, #135e96);
}

.qkb-content-recommendations .button-primary:hover {
    background: var(--qkb-primary-dark, #135e96);
    transform: translateY(-2px);
}

.qkb-content-recommendations .button:not(.button-primary):hover {
    background: #f0f0f1;
    transform: translateY(-2px);
}

/* Modal styling */
.qkb-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100000;
}

.qkb-modal.open {
    display: block;
}

.qkb-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.qkb-modal-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    border-radius: var(--qkb-radius-md, 15px);
    box-shadow: var(--qkb-shadow, 0 10px 25px rgba(0, 0, 0, 0.15));
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.qkb-modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.qkb-modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--qkb-text-dark, #1f2328);
}

.qkb-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    line-height: 1;
    cursor: pointer;
    color: var(--qkb-text-light, #646970);
    padding: 0;
}

.qkb-modal-content {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
}

/* Responsive adjustments */
@media (max-width: 782px) {
    .qkb-metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .qkb-modal-container {
        width: 95%;
    }
}
