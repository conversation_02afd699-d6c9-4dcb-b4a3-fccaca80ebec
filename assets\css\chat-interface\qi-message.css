.qi-chat-messages {
  overflow-y: auto;
  background-color: transparent;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: var(--qkb-border) transparent;
  position: relative;
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  padding: 10px 160px;
  min-height: 0;
}

.qi-message {
  display: flex;
  flex-direction: column;
  max-width: 85%;
  animation: messageAppear 0.5s var(--qkb-transition);
  position: relative;
}

/* System Message */
.qi-system-message {
  max-width: 100%;
  margin: 8px auto;
  text-align: center;
  opacity: 0.7;
}

.qi-message-content h1,
.qi-message-content h2,
.qi-message-content h3,
.qi-message-content h4,
.qi-message-content h5,
.qi-message-content h6 {
  font-size: var(--qkb-font-size-sm);
}

.qi-message-content a {
  text-decoration: none !important;
  color: var(--qkb-text);
  font-weight: bold;
}

.qi-system-message .qi-message-content {
  background-color: var(--qkb-bg-light);
  color: var(--qkb-text-light);
  font-size: 0.9em;
  padding: 4px 12px;
  border-radius: 12px;
  display: inline-block;
}

.qi-user-message {
  margin-left: auto;
  align-items: flex-end;
}

.qi-assistant-message {
  margin-right: auto;
  align-items: flex-start;
  margin-top: var(--qkb-gap-md);
}

.qi-assistant-message .qi-message-content {
  color: var(--qkb-text);
  transition: all 0.3s var(--qkb-transition);
  font-size: var(--qkb-font-size-sm);
  line-height: 1.6;
  word-wrap: break-word;
  overflow-wrap: break-word;
  background-color: var(--qkb-bg);
  position: relative;
}

.qi-user-message .qi-message-content {
  font-size: var(--qkb-font-size-sm);
  background: var(--qkb-primary);
  color: var(--qkb-bg);
  padding: 10px 15px;
  border-radius: var(--qkb-radius-md) var(--qkb-radius-md) 0
    var(--qkb-radius-md);
  position: relative;
  overflow: hidden;
}

/* End Chat Messages */

/* Message Actions Styling */
.qi-message-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--qkb-gap);
  opacity: 0;
  transition: all 0.3s var(--qkb-transition);
  position: relative;
  z-index: 2;
  margin-top: 4px;
  border-radius: var(--qkb-radius-sm);
  transform: translateY(5px);
}

.qi-message-actions.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Copy button styling */
.qi-action-button {
  background: transparent;
  border: none;
  color: var(--qkb-border);
  cursor: pointer;
  padding: 8px;
  transition: var(--qkb-transition);
  position: relative;
  overflow: hidden;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qi-action-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    120deg,
    rgba(var(--qkb-primary-rgb, 59, 130, 246), 0.1),
    rgba(var(--qkb-primary-rgb, 59, 130, 246), 0),
    rgba(var(--qkb-primary-rgb, 59, 130, 246), 0.1)
  );
  transform: translateX(-100%);
  transition: transform 0.6s var(--qkb-transition);
  border-radius: inherit;
}

.qi-action-button:hover {
  background-color: var(--qkb-bg);
  color: var(--qkb-primary);
  transform: translateY(-2px);
}

.qi-action-button:hover::before {
  transform: translateX(100%);
}

.qi-action-button:active {
  transform: translateY(0);
  box-shadow: none;
}

.qi-copy-button.qi-copy-success {
  color: var(--qkb-success);
  background-color: rgba(var(--qkb-success-rgb, 22, 163, 74), 0.1);
  animation: pulse-success 0.5s var(--qkb-transition);
}

.qi-copy-button.qi-copy-success::after {
  content: "Copied!";
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
  white-space: nowrap;
}

.qi-copy-button.qi-copy-success:hover::after {
  opacity: 1;
  bottom: -25px;
}

/* Feedback buttons container */
.qi-feedback-container {
  display: flex;
  gap: var(--qkb-gap);
}

/* Feedback buttons styling */
.qi-feedback-container {
  display: flex;
  gap: 6px;
}

.qi-feedback-button {
  background: transparent !important;
  border: 1px solid transparent;
  color: var(--qkb-border);
  cursor: pointer;
  padding: var(--qkb-gap-md);
  transition: var(--qkb-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  width: 20px;
  height: 20px;
  position: relative;
  overflow: hidden;
}

.qi-feedback-button:hover {
  transform: translateY(-2px);
  border: none;
}

.qi-thumbs-up::before {
  background-color: rgba(var(--qkb-success-rgb, 22, 163, 74), 0.1);
}

.qi-thumbs-down::before {
  background-color: rgba(var(--qkb-error-rgb, 220, 38, 38), 0.1);
}

.qi-thumbs-up:hover {
  color: var(--qkb-success);
  border-color: var(--qkb-success);
}

.qi-thumbs-down:hover {
  color: var(--qkb-error);
  border-color: var(--qkb-error);
}

.qi-feedback-button.active.qi-thumbs-up {
  background-color: rgba(var(--qkb-success-rgb, 22, 163, 74), 0.1);
  color: var(--qkb-success);
  border-color: var(--qkb-success);
  box-shadow: 0 0 0 2px rgba(var(--qkb-success-rgb, 22, 163, 74), 0.2);
  animation: pulse-success 0.5s var(--qkb-transition);
  transform: translateY(-2px);
}

.qi-feedback-button.active.qi-thumbs-down {
  background-color: rgba(var(--qkb-error-rgb, 220, 38, 38), 0.1);
  color: var(--qkb-error);
  border-color: var(--qkb-error);
  box-shadow: 0 0 0 2px rgba(var(--qkb-error-rgb, 220, 38, 38), 0.2);
  animation: pulse-error 0.5s var(--qkb-transition);
  transform: translateY(-2px);
}

.qi-feedback-thanks {
  display: inline-block;
  font-size: 12px;
  color: var(--qkb-success);
  background-color: rgba(var(--qkb-success-rgb, 22, 163, 74), 0.1);
  padding: 6px 12px;
  border-radius: var(--qkb-radius-sm);
  margin-left: var(--qkb-gap);
  animation: fadeInOut 3s var(--qkb-transition) forwards;
  border: 1px solid rgba(var(--qkb-success-rgb, 22, 163, 74), 0.2);
}

.qi-edit-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.qi-edit-input {
  width: 100%;
  min-height: 80px;
  padding: 10px;
  border: 1px solid var(--qkb-border);
  border-radius: 8px;
  font-family: inherit;
  font-size: inherit;
  line-height: 1.5;
  resize: vertical;
  background-color: rgba(255, 255, 255, 0.8);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.qi-edit-input:focus {
  outline: none;
  border-color: var(--qkb-primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.qi-edit-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.qi-edit-save,
.qi-edit-cancel {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--qkb-border);
}

.qi-edit-save {
  background-color: var(--qkb-primary);
  color: white;
  border-color: var(--qkb-primary);
}

.qi-edit-save:hover {
  background-color: var(--qkb-primary-dark, #1a56db);
  transform: translateY(-1px);
}

.qi-edit-cancel {
  background-color: transparent;
  color: var(--qkb-text);
}

.qi-edit-cancel:hover {
  background-color: var(--qkb-bg-light);
  transform: translateY(-1px);
}

.qi-edit-save:active,
.qi-edit-cancel:active {
  transform: translateY(0);
}

/* User message action buttons have been removed */

/* Message updating indicator */
.qi-message.qi-updating {
  position: relative;
}

.qi-message.qi-updating::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--qkb-primary),
    transparent
  );
  animation: loading-bar 1.5s infinite ease-in-out;
  z-index: 3;
}

.qi-scroll-bottom {
  position: absolute;
  bottom: 180px;
  top: auto;
  right: 30px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: var(--qkb-text-light);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
  z-index: 10;
}

.qi-scroll-bottom.visible {
  opacity: 1;
  transform: translateY(0);
}

.qi-scroll-bottom:hover {
  background-color: var(--qkb-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.qi-scroll-bottom:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qi-input-area {
  overflow-y: auto;
  overflow-x: hidden;
  height: 60px;
}

.qi-user-message .qi-message-paragraph {
  color: var(--qkb-bg);
}

.qi-message-paragraph {
  margin: 0 0 0.8em 0 !important;
  line-height: 1.6;
  color: var(--qkb-text);
}

.qi-message-paragraph:last-child {
  margin-bottom: 0 !important;
}

.qi-message-paragraph + .qi-message-paragraph {
  margin-top: 0.4em !important;
}

.qi-message-heading {
  margin: 1.2em 0 0.6em 0 !important;
  font-weight: 600;
  color: var(--qkb-text);
  line-height: 1.3;
}

.qi-message-heading:first-child {
  margin-top: 0 !important;
}

.qi-message-heading + .qi-message-paragraph,
.qi-message-heading + .qi-message-list {
  margin-top: 0.4em !important;
}

.qi-message-heading.qi-h1 {
  font-size: 1.35em;
  border-bottom: 2px solid var(--qkb-border);
  padding-bottom: 0.5em;
  margin: 1.2em 0 0.8em 0 !important;
}

.qi-message-heading.qi-h2 {
  font-size: 1.25em;
  margin: 1em 0 0.6em 0 !important;
}

.qi-message-heading.qi-h3 {
  font-size: 1.15em;
  margin: 0.8em 0 0.5em 0 !important;
}

.qi-message-heading.qi-h4 {
  font-size: 1.1em;
  margin: 0.6em 0 0.4em 0 !important;
}

.qi-message-list {
  margin: 0.8em 0 !important;
  padding-left: 1.5em;
}

.qi-message-list:first-child {
  margin-top: 0 !important;
}

.qi-message-list:last-child {
  margin-bottom: 0 !important;
}

.qi-message-list.qi-ordered-list {
  list-style-type: decimal;
}

.qi-message-list ul,
.qi-message-list ol {
  margin: 0.3em 0;
  padding-left: 1.5em;
}

.qi-list-item {
  margin: 0.2em 0;
  line-height: 1.5;
}

.qi-list-item:first-child {
  margin-top: 0;
}

.qi-list-item:last-child {
  margin-bottom: 0;
}

/* Reduce spacing between consecutive lists */
.qi-message-list + .qi-message-list {
  margin-top: 0.4em !important;
}

/* Reduce spacing between headings and lists */
.qi-message-heading + .qi-message-list {
  margin-top: 0.3em !important;
}

.qi-message-quote {
  border-left: 4px solid var(--qkb-primary);
  margin: 1.2em 0;
  padding: 1em 1.2em;
  background-color: var(--qkb-bg-light);
  font-style: italic;
  color: var(--qkb-text-light);
  border-radius: 0 var(--qkb-radius-sm) var(--qkb-radius-sm) 0;
}

.qi-message-link {
  color: var(--qkb-primary);
  text-decoration: underline;
  transition: color 0.2s ease;
}

.qi-message-link:hover {
  color: var(--qkb-primary-dark);
  text-decoration: none;
}

.qi-inline-code {
  background-color: var(--qkb-bg-light);
  color: var(--qkb-text);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: "Courier New", Courier, monospace;
  font-size: 0.9em;
  border: 1px solid var(--qkb-border);
}

/* Enhanced text formatting */
.qi-message-content strong {
  font-weight: 600;
  color: var(--qkb-text);
}

.qi-message-content em {
  font-style: italic;
  color: var(--qkb-text);
}

.qi-message-content del {
  text-decoration: line-through;
  opacity: 0.7;
}

.qi-message-content sup {
  font-size: 0.75em;
  vertical-align: super;
  line-height: 0;
}

.qi-message-content sub {
  font-size: 0.75em;
  vertical-align: sub;
  line-height: 0;
}

/* Code Block Styling */
.qi-code-block {
  margin: 1.5em 0;
  overflow: hidden;
  background-color: var(--qkb-bg-light);
  border-radius: var(--qkb-radius-sm);
  border: 1px solid var(--qkb-border);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.qi-code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--qkb-padding-sm);
  background-color: var(--qkb-border);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.qi-code-language {
  font-size: var(--qkb-font-size-xs);
  color: var(--qkb-text);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.qi-copy-code-btn {
  background: transparent;
  border: none;
  color: var(--qkb-text-light);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: var(--qkb-radius-sm);
  transition: all 0.2s ease;
  font-size: var(--qkb-font-size-xs);
  display: flex;
  align-items: center;
  gap: 4px;
}

.qi-copy-code-btn:hover {
  background-color: rgba(var(--qkb-primary-rgb), 0.1);
  color: var(--qkb-primary);
  transform: translateY(-1px);
}

.qi-copy-code-btn:active {
  transform: translateY(0);
}

.qi-copy-code-btn.qi-copy-success {
  color: #28a745;
}

.qi-code-block pre {
  margin: 0;
  padding: 1.2em;
  background-color: #f8f9fa;
  overflow-x: auto;
  font-family: "Courier New", Courier, monospace;
  font-size: var(--qkb-font-size-xs);
  line-height: 1.6;
}

.qi-code-block code {
  background: none !important;
  padding: 0 !important;
  border: none !important;
  color: #333;
  white-space: pre;
  font-family: inherit;
  font-size: inherit;
}

/* Fallback styling for when Prism.js is not available */
.qi-code-fallback {
  color: #333 !important;
  background-color: #f8f9fa !important;
}

/* Table Styling */
.qi-table-container {
  margin: 1.5em 0;
  overflow-x: auto;
  border: 1px solid var(--qkb-border);
  background-color: var(--qkb-bg);
}

.qi-message-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--qkb-font-size-sm);
  line-height: 1.6;
}

.qi-table-header {
  background-color: var(--qkb-bg-light);
  color: var(--qkb-text);
  font-weight: 600;
  padding: 0.8em 1em;
  text-align: left;
  border-bottom: 2px solid var(--qkb-border);
  border-right: 1px solid var(--qkb-border);
}

.qi-table-header:last-child {
  border-right: none;
}

.qi-table-row {
  transition: background-color 0.2s ease;
}

.qi-table-row:nth-child(even) {
  background-color: rgba(var(--qkb-primary-rgb), 0.02);
}

.qi-table-row:hover {
  background-color: rgba(var(--qkb-primary-rgb), 0.05);
}

.qi-table-cell {
  padding: 0.8em 1em;
  border-bottom: 1px solid var(--qkb-border);
  border-right: 1px solid var(--qkb-border);
  vertical-align: top;
  line-height: 1.6;
}

.qi-table-cell:last-child {
  border-right: none;
}

.qi-table-row:last-child .qi-table-cell {
  border-bottom: none;
}

/* Additional spacing rules for better content separation */
.qi-message-content > *:not(:first-child) {
  margin-top: 1.2em;
}

.qi-message-content > *:not(:last-child) {
  margin-bottom: 1.2em;
}

/* Specific spacing adjustments for adjacent elements */
.qi-message-heading + .qi-message-paragraph {
  margin-top: 0.8em;
}

.qi-message-paragraph + .qi-message-heading {
  margin-top: 1.8em;
}

.qi-message-list + .qi-message-paragraph,
.qi-message-paragraph + .qi-message-list {
  margin-top: 1.2em;
}

.qi-code-block + .qi-message-paragraph,
.qi-message-paragraph + .qi-code-block {
  margin-top: 1.5em;
}

.qi-table-container + .qi-message-paragraph,
.qi-message-paragraph + .qi-table-container {
  margin-top: 1.5em;
}

.qi-message-quote + .qi-message-paragraph,
.qi-message-paragraph + .qi-message-quote {
  margin-top: 1.2em;
}

/* Ensure proper spacing for first and last elements */
.qi-message-content > :first-child {
  margin-top: 0 !important;
}

.qi-message-content > :last-child {
  margin-bottom: 0 !important;
}

/* Responsive Design for Mobile */
@media screen and (max-width: 768px) {
  .qi-chat-messages {
    padding: 10px var(--qkb-gap-md);
  }

  .qi-message {
    max-width: 95%;
  }

  .qi-assistant-message .qi-message-content,
  .qi-user-message .qi-message-content {
    padding: var(--qkb-gap-sm) var(--qkb-gap-md);
    font-size: var(--qkb-font-size-xs);
  }

  /* Adjust spacing for mobile */
  .qi-message-paragraph {
    margin: 0 0 1em 0;
    line-height: 1.6;
  }

  .qi-message-heading {
    margin: 1.4em 0 0.8em 0;
  }

  .qi-message-list {
    margin: 1em 0;
    padding-left: 1.5em;
  }

  .qi-code-block {
    margin: 1.2em 0;
  }

  .qi-code-block pre {
    padding: 1em;
    font-size: 12px;
  }

  .qi-table-container {
    margin: 1.2em 0;
    font-size: var(--qkb-font-size-xs);
  }

  .qi-table-header,
  .qi-table-cell {
    padding: 0.6em 0.8em;
    font-size: var(--qkb-font-size-xs);
  }

  .qi-message-actions {
    gap: var(--qkb-gap-sm);
  }

  .qi-action-button,
  .qi-feedback-button {
    width: 28px;
    height: 28px;
    padding: 6px;
  }

  /* Reduce spacing between elements on mobile */
  .qi-message-content > *:not(:first-child) {
    margin-top: 1em;
  }

  .qi-message-content > *:not(:last-child) {
    margin-bottom: 1em;
  }
}

@media screen and (max-width: 480px) {
  .qi-chat-messages {
    padding: 8px var(--qkb-gap-sm);
  }

  .qi-message {
    max-width: 98%;
  }

  .qi-assistant-message .qi-message-content,
  .qi-user-message .qi-message-content {
    padding: var(--qkb-gap-sm);
  }

  .qi-message-heading.qi-h1 {
    font-size: 1.1em;
  }

  .qi-message-heading.qi-h2 {
    font-size: 1.05em;
  }

  .qi-message-heading.qi-h3 {
    font-size: 1em;
  }
}
