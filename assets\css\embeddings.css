.qkb-embeddings-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}
.qkb-embeddings-actions {
    margin-top: 15px;
}
.qkb-progress-bar {
    height: 20px;
    background-color: #f0f0f1;
    border-radius: 3px;
    margin: 15px 0 5px;
    overflow: hidden;
}
.qkb-progress-bar-inner {
    height: 100%;
    background-color: #2271b1;
    width: 0;
    transition: width 0.3s ease;
}
.qkb-progress-text {
    margin-bottom: 15px;
    font-size: 13px;
}
.qkb-embeddings-results {
    margin-top: 15px;
    padding: 10px;
    background: #f0f8ff;
    border-left: 4px solid #2271b1;
}
.qkb-results-errors {
    margin-top: 10px;
    padding: 10px;
    background: #fff8f7;
    border-left: 4px solid #d63638;
}
.qkb-error-list {
    margin: 5px 0 0 20px;
}
.qkb-generate-all-embeddings .dashicons {
    margin-right: 5px;
}
.dashicons-update-spin {
    animation: qkb-spin 2s linear infinite;
}
@keyframes qkb-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
