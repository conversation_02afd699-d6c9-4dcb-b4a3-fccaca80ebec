/**
 * Modern Settings Page Styling
 * 
 * Enhances the Q Knowledge Base settings page with a modern, clean design
 */

/* Import fonts if not already imported */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Main container styling */
.wrap {
    max-width: 1200px;
    margin: 20px auto;
    font-family: var(--qkb-font, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
}

.wrap h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 25px;
    color: var(--qkb-text-dark, #1f2328);
    padding-bottom: 15px;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
}

/* Settings form container */
.qkb-settings-form {
    background: #fff;
    border-radius: var(--qkb-radius-md, 15px);
    box-shadow: var(--qkb-shadow-sm, 0 5px 10px rgba(0, 0, 0, 0.1));
    overflow: hidden;
}

/* Enhanced tab navigation */
.qkb-settings-tab-nav {
    display: flex;
    flex-wrap: wrap;
    background: #fff;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
    padding: 0;
    position: sticky;
    top: 32px;
    z-index: var(--qkb-z-index-high, 100);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
}

.qkb-settings-tab-button {
    padding: 16px 20px;
    background: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    color: var(--qkb-text-light, #646970);
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
}

.qkb-settings-tab-button:hover {
    color: var(--qkb-primary, #2271b1);
    background-color: rgba(34, 113, 177, 0.04);
}

.qkb-settings-tab-button.active {
    color: var(--qkb-primary, #2271b1);
    border-bottom-color: var(--qkb-primary, #2271b1);
    background-color: rgba(34, 113, 177, 0.04);
}

.qkb-settings-tab-button:focus {
    outline: none;
    box-shadow: 0 0 0 1px var(--qkb-primary-light, #2271b1ad);
}

.qkb-tab-icon {
    font-size: 18px;
}

/* Tab content styling */
.qkb-settings-tab-pane {
    display: none;
    padding: 30px;
    animation: fadeIn 0.4s ease-in-out;
}

.qkb-settings-tab-pane.active {
    display: block;
}

.qkb-settings-tab-pane h2.title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
    padding: 0 0 15px 0;
    border-bottom: 1px solid var(--qkb-border, #e1e4e8);
    color: var(--qkb-text-dark, #1f2328);
}

/* Card-based settings groups */
.qkb-settings-card {
    background: #fff;
    border-radius: var(--qkb-radius-sm, 10px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid var(--qkb-border, #e1e4e8);
}

.qkb-settings-card h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px 0;
    color: var(--qkb-text-dark, #1f2328);
}

/* Form elements styling */
.form-table {
    border-collapse: collapse;
    margin-top: 0;
    width: 100%;
}

.form-table th {
    font-weight: 500;
    text-align: left;
    padding: 15px 10px 15px 0;
    width: 200px;
    vertical-align: top;
    color: var(--qkb-text, #2c3338);
}

.form-table td {
    padding: 15px 0;
    vertical-align: top;
}

/* Input styling */
.qkb-settings-form input[type="text"],
.qkb-settings-form input[type="url"],
.qkb-settings-form input[type="number"],
.qkb-settings-form input[type="password"],
.qkb-settings-form select,
.qkb-settings-form textarea {
    border: 1px solid var(--qkb-border, #e1e4e8);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    max-width: 100%;
    color: var(--qkb-text, #2c3338);
    background-color: #fff;
}

.qkb-settings-form input[type="text"]:focus,
.qkb-settings-form input[type="url"]:focus,
.qkb-settings-form input[type="number"]:focus,
.qkb-settings-form input[type="password"]:focus,
.qkb-settings-form select:focus,
.qkb-settings-form textarea:focus {
    border-color: var(--qkb-primary, #2271b1);
    box-shadow: 0 0 0 1px var(--qkb-primary-light, #2271b1ad);
    outline: none;
}

/* Color picker styling */
.qkb-color-picker {
    width: 60px;
    height: 30px;
    padding: 0;
    border: 1px solid var(--qkb-border, #e1e4e8);
    border-radius: 6px;
    cursor: pointer;
}

/* Description text */
.description {
    font-size: 13px;
    color: var(--qkb-text-light, #646970);
    margin-top: 6px;
    font-style: normal;
}

/* Submit button wrapper */
.qkb-settings-submit-wrapper {
    position: sticky;
    bottom: 0;
    background: #fff;
    padding: 15px 20px;
    margin-top: 20px;
    z-index: var(--qkb-z-index-high, 100);
    border-top: 1px solid var(--qkb-border, #e1e4e8);
    text-align: right;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

/* Submit button styling */
.qkb-settings-submit-wrapper .button-primary {
    background: var(--qkb-primary, #2271b1);
    border-color: var(--qkb-primary, #2271b1);
    color: white;
    padding: 6px 16px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.qkb-settings-submit-wrapper .button-primary:hover {
    background: var(--qkb-primary-dark, #135e96);
    border-color: var(--qkb-primary-dark, #135e96);
}

/* Checkbox and radio styling */
.qkb-settings-form input[type="checkbox"],
.qkb-settings-form input[type="radio"] {
    border: 1px solid var(--qkb-border, #e1e4e8);
}

.qkb-settings-form input[type="checkbox"]:checked,
.qkb-settings-form input[type="radio"]:checked {
    border-color: var(--qkb-primary, #2271b1);
    background-color: var(--qkb-primary, #2271b1);
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
    .qkb-settings-tab-nav {
        top: 46px;
    }
    
    .qkb-settings-tab-button {
        padding: 12px 15px;
        font-size: 13px;
    }
    
    .form-table th {
        width: 100%;
        display: block;
        padding-bottom: 0;
    }
    
    .form-table td {
        width: 100%;
        display: block;
        padding-top: 8px;
    }
    
    .qkb-settings-tab-pane {
        padding: 20px 15px;
    }
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
