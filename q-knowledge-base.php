<?php
// Ensure WordPress functions are available
if (!defined('WPINC')) {
    die('Direct access not allowed.');
}

// Add at the very top, before any other code
require_once(ABSPATH . 'wp-admin/includes/plugin.php');
require_once(ABSPATH . 'wp-includes/pluggable.php');

/**
 * Plugin Name: Q Knowledge Base
 * Description: An advanced Knowledge Base plugin featuring an AI-powered chatbot to enhance user interaction and support, providing seamless access to information.
 * Version: 1.0.5
 * Author: Q-Ai
 * License: GPL v2 or later
 * Text Domain: q-knowledge-base
 * Domain Path: /languages
 * Requires at least: 5.8
 * Requires PHP: 7.4
 * Tested up to: 6.0
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('QKB_VERSION', '1.0.5');
define('QKB_PLUGIN_FILE', __FILE__);
define('QKB_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('QKB_PLUGIN_URL', plugin_dir_url(__FILE__));
define('QKB_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Load plugin classes
require_once QKB_PLUGIN_DIR . 'includes/post-types/class-qkb-knowledge-base.php';
require_once QKB_PLUGIN_DIR . 'includes/chat/class-qkb-chatbot.php';
require_once QKB_PLUGIN_DIR . 'includes/class-qkb-settings.php';
require_once QKB_PLUGIN_DIR . 'includes/class-qkb-openai-handler.php';
require_once QKB_PLUGIN_DIR . 'includes/class-qkb-nlp-handler.php';
require_once QKB_PLUGIN_DIR . 'includes/class-qkb-ml-handler.php';
require_once QKB_PLUGIN_DIR . 'includes/class-qkb-crawler.php';
require_once QKB_PLUGIN_DIR . 'includes/class-qkb-rate-limiter.php';
require_once QKB_PLUGIN_DIR . 'includes/class-qkb-error-handler.php';
require_once QKB_PLUGIN_DIR . 'includes/class-qkb-cache.php';
require_once QKB_PLUGIN_DIR . 'includes/class-qkb-gap-analyzer.php';
require_once QKB_PLUGIN_DIR . 'includes/class-qkb-database-optimizer.php';
require_once QKB_PLUGIN_DIR . 'includes/class-qkb-performance-monitor.php';
require_once QKB_PLUGIN_DIR . 'includes/openai/class-qkb-openai-usage.php';
require_once QKB_PLUGIN_DIR . 'includes/openai/class-qkb-openai-assistants.php';
require_once QKB_PLUGIN_DIR . 'includes/admin/class-qkb-assistant-builder.php';
require_once QKB_PLUGIN_DIR . 'includes/admin/class-qkb-document-settings.php';
require_once QKB_PLUGIN_DIR . 'includes/class-qkb-document-extractor.php';
require_once QKB_PLUGIN_DIR . 'includes/shortcodes/class-qkb-quick-action-shortcodes.php';
require_once QKB_PLUGIN_DIR . 'includes/ajax-handlers.php';
require_once QKB_PLUGIN_DIR . 'includes/ajax-handlers-updated.php';
require_once QKB_PLUGIN_DIR . 'includes/formidable-forms-integration.php';
require_once QKB_PLUGIN_DIR . 'admin-fix-feedback-table.php';

QKB_Knowledge_Base::get_instance();
new QKB_Chatbot();
new QKB_Settings();
QKB_Document_Settings::get_instance();
global $qkb_ml_handler;
$qkb_ml_handler = new QKB_ML_Handler();

// Initialize OpenAI assistants
QKB_Assistant_Builder::get_instance();

// Initialize AJAX handlers
QKB_AJAX_Handlers::get_instance();

$crawler = new QKB_Crawler();
$crawler->init();

function qkb_elementor_compatibility_check()
{
    if (!did_action('elementor/loaded')) {
        add_action('admin_notices', function () {
            if (current_user_can('activate_plugins')) {
                $message = sprintf(
                    esc_html__('"%1$s" requires "%2$s" to be installed and activated. %3$s', 'q-knowledge-base'),
                    '<strong>Q Knowledge Base</strong>',
                    '<strong>Elementor</strong>',
                    '<a href="' . esc_url(admin_url('plugins.php')) . '">' . esc_html__('Please check your plugins', 'q-knowledge-base') . '</a>'
                );

                printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
            }
        });
        return false;
    }
    return true;
}
add_action('plugins_loaded', 'qkb_elementor_compatibility_check');

register_activation_hook(__FILE__, 'qkb_activate_plugin');

function qkb_activate_plugin()
{
    // Initialize ML Handler and create tables
    $ml_handler = QKB_ML_Handler::get_instance();

    // Force recreation of tables to ensure they have the correct structure
    $ml_handler->recreate_tables();

    // Log activation
    error_log('Q Knowledge Base plugin activated - tables created/verified');
}

register_deactivation_hook(__FILE__, 'qkb_deactivate_plugin');

function qkb_deactivate_plugin()
{
}

// Register AJAX handlers for feedback analytics
function qkb_register_feedback_analytics_ajax_handlers()
{
    // Only register if ML Handler class exists
    if (class_exists('QKB_ML_Handler')) {
        // Get ML Handler instance
        $ml_handler = QKB_ML_Handler::get_instance();

        // Register AJAX handlers
        add_action('wp_ajax_qkb_get_feedback_analytics', array($ml_handler, 'ajax_get_feedback_analytics'));
        add_action('wp_ajax_qkb_get_feedback_table', array($ml_handler, 'ajax_get_feedback_table'));
        add_action('wp_ajax_qkb_get_feedback_details', array($ml_handler, 'ajax_get_feedback_details'));
        add_action('wp_ajax_qkb_export_feedback', array($ml_handler, 'ajax_export_feedback'));
        add_action('wp_ajax_qkb_clear_feedback', array($ml_handler, 'ajax_clear_feedback'));
    }
}
add_action('wp_loaded', 'qkb_register_feedback_analytics_ajax_handlers');

register_activation_hook(__FILE__, 'qkb_activate_crawler');
function qkb_activate_crawler()
{
    wp_schedule_event(time(), get_option('qkb_crawl_frequency', 'weekly'), 'qkb_crawl_scheduled_event');
}

register_deactivation_hook(__FILE__, 'qkb_deactivate_crawler');
function qkb_deactivate_crawler()
{
    wp_clear_scheduled_hook('qkb_crawl_scheduled_event');
}

add_action('admin_enqueue_scripts', function ($hook) {
    if ($hook === 'kb_knowledge_base_page_qkb-feedback-analytics') {
        wp_enqueue_style(
            'qkb-admin-feedback',
            QKB_PLUGIN_URL . 'assets/css/admin-feedback.css',
            [],
            QKB_VERSION
        );
    }
});

/**
 * Initialize Formidable Forms integration
 */
function qkb_initialize_formidable_forms_integration()
{
    // Check if Formidable Forms is active
    if (class_exists('FrmHooksController')) {
        // Get AJAX handlers instance
        $ajax_handlers = QKB_AJAX_Handlers::get_instance();

        // Get Formidable Forms integration instance
        $formidable_integration = QKB_Formidable_Forms_Integration::get_instance();

        // Register AJAX endpoints
        add_action('wp_ajax_qkb_submit_formidable_form_direct', array($formidable_integration, 'ajax_submit_formidable_form_direct'));
        add_action('wp_ajax_nopriv_qkb_submit_formidable_form_direct', array($formidable_integration, 'ajax_submit_formidable_form_direct'));

        // Add hook for form submission
        add_action('frm_after_create_entry', array($ajax_handlers, 'after_form_submit'), 30, 2);

        // Add hook to force entry creation
        add_filter('frm_continue_to_create', function ($continue, $form_id) {
            // Check if this is a submission from a quick action
            if (isset($_POST['qkb_quick_action']) || isset($_COOKIE['qkb_quick_action_active'])) {
                error_log('Forcing entry creation for form ID: ' . $form_id);
                return true;
            }
            return $continue;
        }, 99, 2);

        // Add a hidden field to forms in quick actions to identify them
        add_action('frm_form_fields_hidden', function ($fields, $form) {
            // Check if we're in a quick action context
            $is_quick_action = (
                (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'qkb-quick-action') !== false) ||
                isset($_GET['qkb_quick_action']) ||
                isset($_COOKIE['qkb_quick_action_active'])
            );

            if ($is_quick_action) {
                echo '<input type="hidden" name="qkb_quick_action" value="1">';
            }

            return $fields;
        }, 20, 2);

        // Set a cookie when a quick action is triggered
        add_action('wp_footer', function () {
            ?>
            <script type="text/javascript">
                jQuery(document).ready(function ($) {
                    $(document).on('click', '.qkb-quick-action-trigger-button', function () {
                        // Set a cookie to indicate we're in a quick action context
                        document.cookie = "qkb_quick_action_active=1; path=/; max-age=3600";
                    });
                });
            </script>
            <?php
        });
    }
}
add_action('init', 'qkb_initialize_formidable_forms_integration');



