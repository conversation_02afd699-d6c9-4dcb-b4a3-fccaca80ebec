


/* Chat Interface Enhancements */

/* Suggested prompts */
.qi-suggested-prompts {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
    animation: fadeIn 0.5s var(--qkb-transition) both;
    animation-delay: 0.2s;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 25px;
    width: 100%;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}


.qi-suggested-prompts h3 {
    font-size: var(--qkb-font-size-sm);
    color: var(--qkb-text-light);
    font-weight: normal;
    margin: 0;
    position: relative;
}


/* Suggested Prompt Buttons */
.qi-prompt-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: var(--qkb-gap-lg);
    justify-content: center;
    width: 100%;
    margin-top: 10px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.qi-prompt-button {
    background: var(--qkb-bg);
    border: 1px solid var(--qkb-primary-light);
    border-radius: var(--qkb-radius-sm);
    padding: var(--qkb-padding-sm);
    font-size: var(--qkb-font-size-sm);
    color: var(--qkb-text-light);
    cursor: pointer;
    transition: var(--qkb-transition);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    -webkit-border-radius: var(--qkb-radius-sm);
    -moz-border-radius: var(--qkb-radius-sm);
    -ms-border-radius: var(--qkb-radius-sm);
    -o-border-radius: var(--qkb-radius-sm);
    -webkit-transition: var(--qkb-transition);
    -moz-transition: var(--qkb-transition);
    -ms-transition: var(--qkb-transition);
    -o-transition: var(--qkb-transition);
}



.qi-prompt-button:hover {
    background: linear-gradient(to bottom, var(--qkb-bg), var(--qkb-bg-alt));
    border-color: var(--qkb-primary);
    transform: translateY(-3px) scale(1.02);
    color: var(--qkb-primary);
}







