
/* Header Styling */
.qi-chat-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--qkb-padding);
    margin-bottom: var(--qkb-gap);
    border-bottom: 2px solid var(--qkb-primary-light);
    position: relative;

}

.qi-current-assistant {
    display: flex;
    align-items: center;
    min-width: 30%;
    max-width: 300px;
    cursor: pointer;
}

.qi-assistant-icon-wrapper,
.qi-assistant-icon-wrapper i {
    position: relative;
    width: 30px;
    height: 30px;
    background: var(--qkb-primary);
    border-radius: var(--qkb-radius-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--qkb-bg);
}

.qi-assistant-select {
    border: none;
    color: var(--qkb-primary);
    font-size: var(--qkb-font-size-sm);
    padding: var(--qkb-padding-sm);
    cursor: pointer;
}

.qi-assistant-select:focus {
    outline: none;
}

.qi-chat-actions {
    display: flex;
    gap: var(--qkb-gap);
    position: relative;
    flex: 0 0 auto;
}

.qi-toggle-sidebar {
    padding: 0px;
    background: transparent;
    color: var(--qkb-primary-dark);
    transition: var(--qkb-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    margin-right:15px;
}

.qi-toggle-sidebar:hover {
    background: transparent;
       color: var(--qkb-primary-dark);
}

.qi-toggle-sidebar:focus {
    background: transparent;
       color: var(--qkb-primary-dark);
}

.qi-toggle-sidebar:active {
    background: transparent;
       color: var(--qkb-primary-dark);
}


.qi-chat-actions button {
    width: 40px;
    height: 40px;
    padding: var(--qkb-gap);
    border-radius: var(--qkb-radius-xs);
    background: var(--qkb-primary-light);
    color: var(--qkb-primary-dark);
    transition: var(--qkb-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
}

.qi-chat-actions button:hover {
    background-color: var(--qkb-primary-dark);
    color: var(--qkb-bg);
    transform: var(--qkb-transform);
}

.qi-chat-actions button:focus {
    background-color: var(--qkb-primary-light);
    color: var(--qkb-primary);
    transform: var(--qkb-transform);
}


.qi-chat-actions .qi-new-chat {
    background: var(--qkb-gradient);
    width: auto;
    color: var(--qkb-bg);
    padding: var(--qkb-padding);
}

.qi-new-chat::after {
    content: "New Chat";
    font-size: var(--qkb-font-size-sm);
    font-weight: var(--qkb-font-weight-medium);
    color: var(--qkb-bg);
    margin-left: var(--qkb-gap-md);
}

.qi-chat-actions .qi-new-chat:hover {
    background: var(--qkb-primary-dark);
    transform: var(--qkb-transform);
}

body.qi-fullscreen-active {
    overflow: hidden;
}
