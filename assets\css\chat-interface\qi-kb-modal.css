.qi-kb-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: var(--qkb-transition);
    background: var(--qkb-primary-light);
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: var(--qkb-blur);
    z-index: var(--qkb-z-index-modal);
}

.qi-kb-modal-close-global {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--qkb-bg-alt);
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--qkb-text-light);
    z-index: 10;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s var(--qkb-transition);
}

.qi-kb-modal-close-global:hover {
    color: var(--qkb-error);
    transform: scale(1.1);
    background: var(--qkb-bg-hover);
}

.qi-kb-modal.show {
    opacity: 1;
    visibility: visible;
}

.qi-kb-modal-content {
    background: var(--qkb-bg);
    border-radius: var(--qkb-radius-sm);
    width: 100%;
    max-width: 600px;
    box-shadow: var(--qkb-shadow);
    position: relative;
    max-height: 90vh;
    overflow-y: auto;
    padding: var(--qkb-padding-sm);
    opacity: 0;
}

.qi-kb-modal.show .qi-kb-modal-content {
    opacity: 1;
}

.qi-kb-modal-header {
    border-bottom: 1px solid var(--qkb-primary-light);
}

.qi-kb-modal-header h2 {
    font-size: var(--qkb-font-size-lg);
    font-weight: var(--qkb-font-weight-semibold);
    color: var(--qkb-text);
}

.qi-no-knowledge {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--qkb-gap-xl);
    text-align: center;
    background-color: var(--qkb-bg-alt);
    margin: var(--qkb-gap-xl) 0;
    min-height: 200px;
}

.qi-no-knowledge-icon {
    font-size: 48px;
    margin-bottom: var(--qkb-gap-lg);
    background-color: var(--qkb-bg);
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qi-no-knowledge p {
    font-size: var(--qkb-font-size-md);
    color: var(--qkb-text);
    margin-bottom: var(--qkb-gap-lg);
}

.qi-no-knowledge .qi-kb-add-knowledge-btn {
    padding: var(--qkb-gap) var(--qkb-gap-lg);
    color: var(--qkb-bg);
    border: none;
    border-radius: var(--qkb-radius-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s var(--qkb-transition);
}

.qi-no-knowledge .qi-kb-add-knowledge-btn:hover {
    transform: translateY(-2px);
}





.qi-kb-form-actions {
    display: flex;
    gap: var(--qkb-gap-md);
    justify-content: center;
    margin-top: var(--qkb-gap-xl);
}

.qi-kb-form-actions button {
    padding: var(--qkb-padding-sm);
    border: none;
    border-radius: var(--qkb-radius-xs);
    cursor: pointer;
    transition: var(--qkb-transition);
    -webkit-border-radius: var(--qkb-radius-xs);
    -moz-border-radius: var(--qkb-radius-xs);
    -ms-border-radius: var(--qkb-radius-xs);
    -o-border-radius: var(--qkb-radius-xs);
    -webkit-transition: var(--qkb-transition);
    -moz-transition: var(--qkb-transition);
    -ms-transition: var(--qkb-transition);
    -o-transition: var(--qkb-transition);
}





.qi-kb-cancel {
    background: var(--qkb-bg-alt);
    color: var(--qkb-text);
    border: 1px solid var(--qkb-border-light);
}

.qi-kb-cancel:hover {
    background: var(--qkb-bg-hover);
    transform: translateY(-2px);
}

.qi-kb-tab-content {
    transition: opacity 0.3s ease, visibility 0.3s ease;
}




.qi-no-knowledge {
    text-align: center;
    padding: var(--qkb-gap-xl);
    color: var(--qkb-text-light);
}

.qi-no-knowledge-icon {
    font-size: calc(var(--qkb-font-size-lg) * 2);
    margin-bottom: var(--qkb-gap-lg);
    color: var(--qkb-text-light);
    opacity: 0.5;
}

.qi-no-knowledge p {
    margin: var(--qkb-gap-md) 0;
    font-size: var(--qkb-font-size-md);
}

.qi-no-knowledge-desc {
    color: var(--qkb-text-light);
    font-size: var(--qkb-font-size-sm);
    opacity: 0.8;
}


.qi-kb-form-group.kb-content-file .file-preview {
    max-width: 200px;
    margin-top: var(--qkb-gap-lg);
    border-radius: var(--qkb-radius-sm);
    overflow: hidden;
    border: 1px solid var(--qkb-border-light);
    transition: all 0.3s var(--qkb-transition);
    transform: translateY(0);
}

.qi-kb-form-group.kb-content-file .file-preview:hover {
    transform: translateY(-2px);
    box-shadow: var(--qkb-shadow);
}



.qi-kb-tab.active {
    background: var(--qkb-primary-light);
    font-weight: 600;
}

.qi-kb-tab:hover {
    background: var(--qkb-primary-light);
}

.qi-kb-tab-content {
    display: none;
    padding: var(--qkb-gap-lg);
}

.qi-knowledge-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: var(--qkb-gap-lg);
    overflow: hidden;
}

.qi-knowledge-table th,
.qi-knowledge-table td {
    padding: var(--qkb-gap-md);
    text-align: left;
    border-bottom: 1px solid var(--qkb-border-light);
}

.qi-knowledge-table th {
    font-weight: 600;
    color: var(--qkb-text);
    position: sticky;
    top: 0;
    z-index: 10;
}

.qi-knowledge-table tr:last-child td {
    border-bottom: none;
}

.qi-knowledge-table tr:hover td {
    background-color: var(--qkb-bg-alt);
    transition: var(--qkb-transition);
    -webkit-transition: var(--qkb-transition);
    -moz-transition: var(--qkb-transition);
    -ms-transition: var(--qkb-transition);
    -o-transition: var(--qkb-transition);
}

.qi-knowledge-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--qkb-gap);
    margin-top: var(--qkb-gap-lg);
}

.qi-kb-form-group.kb-content-file .upload-status {
    display: none;
    align-items: center;
    gap: var(--qkb-gap);
    margin-top: var(--qkb-gap-md);
    font-size: var(--qkb-font-size-sm);
    padding: var(--qkb-gap) var(--qkb-gap-md);
    border-radius: var(--qkb-radius-sm);
    background-color: var(--qkb-bg);
    transition: all 0.3s var(--qkb-transition);
    animation: fadeInUp 0.3s var(--qkb-transition);
    position: relative;
    width: 100%;
}

.qi-kb-form-group.kb-content-file .upload-status.success {
    color: var(--qkb-success);
    border-left: 3px solid var(--qkb-success);
    background-color: rgba(var(--qkb-success-rgb, 34, 197, 94), 0.05);
}

.qi-kb-form-group.kb-content-file .upload-status.error {
    color: var(--qkb-error);
    border-left: 3px solid var(--qkb-error);
    background-color: rgba(var(--qkb-error-rgb, 220, 38, 38), 0.05);
}

.qi-kb-form-group.kb-content-file .upload-status .status-icon {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.qi-kb-form-group.kb-content-file .upload-status .status-icon i {
    font-size: var(--qkb-font-size-md);
}

.qi-kb-form-group.kb-content-file .upload-status .status-message {
    flex: 1 1 auto;
    font-weight: 500;
}

.qi-kb-form-group.kb-content-file .upload-status .close-status {
    flex: 0 0 auto;
    background: transparent;
    border: none;
    color: inherit;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s var(--qkb-transition);
    opacity: 0.7;
}

.qi-kb-form-group.kb-content-file .upload-status .close-status:hover {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
    opacity: 1;
}




.qi-kb-tab {
    padding: var(--qkb-padding-sm);
    font-size: var(--qkb-font-size-md) !important;
    color: var(--qkb-text-light) !important;
    border: none !important;
    border-style: none !important;
    background: transparent;
    position: relative;
    transition: var(--qkb-transition);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--qkb-gap);
}

.qi-kb-tab i {
    font-size: 1.1em;
}

.qi-kb-tab.active {
    background: transparent !important;
    color: var(--qkb-primary) !important;
    font-weight: var(--qkb-font-weight-medium);
}

.qi-kb-tab:hover:not(.active) {
    background: none !important;
    color: var(--qkb-primary);
    transform: translateY(-2px);
}



.qi-kb-tab-content {
    padding: var(--qkb-gap-lg);
    transition: var(--qkb-transition);
    position: relative;
}

/* Form Styling */
.qi-kb-form {
    margin-top:var(--qkb-gap-xl);
    display:flex;
    flex-direction:column;
    gap:var(--qkb-gap-lg);
}

.qi-kb-form-group label {
    display: block;
    margin-bottom: var(--qkb-gap);
/* Form Field Styling */
.qi-kb-form-group input[type="text"],
.qi-kb-form-group input[type="email"],
.qi-kb-form-group input[type="url"],
.qi-kb-form-group input[type="password"],
.qi-kb-form-group input[type="number"],
.qi-kb-form-group input[type="tel"],
.qi-kb-form-group input[type="search"],
.qi-kb-form-group textarea,
.qi-kb-form-group select {
    width: 100%;
    padding: var(--qkb-padding-sm);
    border: 1px solid var(--qkb-border-light);
    border-radius: var(--qkb-radius-xs);
    background-color: var(--qkb-bg-alt);
    color: var(--qkb-text);
    font-size: var(--qkb-font-size-md);
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    box-sizing: border-box; /* Ensure padding doesn't affect width */
}

.qi-kb-form-group input[type="text"]:focus,
.qi-kb-form-group input[type="email"]:focus,
.qi-kb-form-group input[type="url"]:focus,
.qi-kb-form-group input[type="password"]:focus,
.qi-kb-form-group input[type="number"]:focus,
.qi-kb-form-group input[type="tel"]:focus,
.qi-kb-form-group input[type="search"]:focus,
.qi-kb-form-group textarea:focus,
.qi-kb-form-group select:focus {
    outline: none;
    border-color: var(--qkb-primary);
    box-shadow: 0 0 0 2px rgba(var(--qkb-primary-rgb, 0, 123, 255), 0.25);
}

.qi-kb-form-group input[type="text"]:hover,
.qi-kb-form-group input[type="email"]:hover,
.qi-kb-form-group input[type="url"]:hover,
.qi-kb-form-group input[type="password"]:hover,
.qi-kb-form-group input[type="number"]:hover,
.qi-kb-form-group input[type="tel"]:hover,
.qi-kb-form-group input[type="search"]:hover,
.qi-kb-form-group textarea:hover,
.qi-kb-form-group select:hover {
    border-color: var(--qkb-primary);
}
}



/* Knowledge Table Improvements */
.qi-knowledge-table {
    overflow: hidden;
    border-style: none;
}

.qi-knowledge-table th {
    background: var(--qkb-bg-light);
    border-style: none;
    color: var(--qkb-text);
    font-weight: var(--qkb-font-weight-medium);
    font-size: var(--qkb-font-size-sm);
    padding: var(--qkb-gap-md);
    text-align: left;
}

.qi-knowledge-table td {
    padding: var(--qkb-padding-sm) !important;
    border-style: none;
}

.qi-knowledge-pagination {
    display: flex;
    gap: var(--qkb-gap-lg);
    justify-content: center;
    margin-top: var(--qkb-gap-lg);
}

.qi-knowledge-pagination button {
    padding: var(--qkb-padding-sm) !important;
    border: none;
    border-radius: var(--qkb-radius-xs);
    background: var(--qkb-primary);
    font-size: var(--qkb-font-size-sm);
    color: var(--qkb-bg);
    cursor: pointer;
    transition: all 0.2s ease;
}

.qi-knowledge-pagination button:hover:not(:disabled) {
    background: var(--qkb-primary);
    transform: translateY(-2px);
}

.qi-knowledge-pagination button.active {
    background: var(--qkb-primary);
    color: var(--qkb-bg);
    border-color: transparent;
    font-weight: 600;
}

.qi-knowledge-pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--qkb-border);
    color: var(--qkb-text-light);
}

.qi-knowledge-delete {
    border: 1px solid var(--qkb-error);
    color: var(--qkb-error);
    font-weight: 600;
    border-radius: var(--qkb-radius-sm);
    padding: var(--qkb-gap) var(--qkb-gap-md);
    background-color: rgba(var(--qkb-error-rgb, 220, 38, 38), 0.05);
    transition: all 0.2s var(--qkb-transition);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: var(--qkb-gap);
}

.qi-knowledge-delete:hover {
    background-color: rgba(var(--qkb-error-rgb, 220, 38, 38), 0.1);
    transform: translateY(-2px);
}

.qi-knowledge-delete:active {
    transform: translateY(0);
}

.qi-knowledge-delete i {
    font-size: var(--qkb-font-size-sm);
}




.qi-kb-form-group label {
    display: block;
    margin-bottom: var(--qkb-gap);
    font-weight: 600;
    color: var(--qkb-text);
    font-size: var(--qkb-font-size-sm);
}

.qi-kb-form-group input[type="text"],
.qi-kb-form-group input[type="url"],
.qi-kb-form-group select,
.qi-kb-form-group textarea {
    width: 100%;
    padding: var(--qkb-padding-sm); /* Adjusted padding */
    border: 1px solid var(--qkb-border-light); /* Adjusted border */
    border-radius: var(--qkb-radius-xs); /* Adjusted border-radius */
    background: var(--qkb-bg-light);
    color: var(--qkb-text);
    font-size: var(--qkb-font-size-sm); /* Adjusted font-size */
    transition: var(--qkb-transition);
}

.qi-kb-form-group input[type="text"]:focus,
.qi-kb-form-group input[type="url"]:focus,
.qi-kb-form-group select:focus,
.qi-kb-form-group textarea:focus {
    border-color: var(--qkb-primary);
    box-shadow: 0 0 0 3px rgba(var(--qkb-primary-rgb, 59, 130, 246), 0.2); /* Adjusted box-shadow */
    outline: none;
}

.qi-kb-form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 40px;
}

.qi-kb-form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.qi-kb-form-group .description {
    margin-top: var(--qkb-gap-sm);
    font-size: var(--qkb-font-size-xs);
    color: var(--qkb-text-light);
}



.qi-kb-submit,
.qi-kb-cancel {
    padding: var(--qkb-padding-sm) !important;
    border-radius: var(--qkb-radius-xs) !important;
    border: none;
    cursor: pointer;
    font-size: var(--qkb-font-size-sm) !important;
    font-weight: 600;
    transition: var(--qkb-transition);
}

.qi-kb-submit {
    background: var(--qkb-primary) !important;
    color: var(--qkb-bg) !important;
}

.qi-kb-submit:hover:not(:disabled) {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
    color: var(--qkb-bg);
}

.qi-kb-submit:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.qi-kb-cancel {
    background: var(--qkb-primary-light) !important;
    color: var(--qkb-text) !important;
}

.qi-kb-cancel:hover {
    background: var(--qkb-primary);
    border-color: var(--qkb-text-light);
    transform: translateY(-1px);
}

/* File Upload Enhancements */
.qi-kb-form-group.kb-content-file {
    position: relative;
    border-radius: var(--qkb-radius-sm);
    transition: all 0.3s var(--qkb-transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--qkb-gap-md);
    padding: var(--qkb-gap-xl);
    background-color: var(--qkb-bg-light);
    min-height: 200px;
    justify-content: center;
    overflow: hidden;
    -webkit-border-radius: var(--qkb-radius-sm);
    -moz-border-radius: var(--qkb-radius-sm);
    -ms-border-radius: var(--qkb-radius-sm);
    -o-border-radius: var(--qkb-radius-sm);
}

.qi-kb-form-group.kb-content-file::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(66, 133, 244, 0.05) 0%, rgba(66, 133, 244, 0) 100%);
    z-index: 0;
    opacity: 0.5;
    transition: opacity 0.3s var(--qkb-transition);
}

.qi-kb-form-group.kb-content-file:hover {
    border-color: var(--qkb-accent-blue);
    background: var(--qkb-accent-blue-light);
    transform: translateY(-2px);
}

.qi-kb-form-group.kb-content-file:hover::before {
    opacity: 1;
}

.qi-kb-form-group.kb-content-file.dragover {
    border-color: var(--qkb-success);
    background: rgba(74, 222, 128, 0.1);
    transform: translateY(-4px);
    box-shadow: var(--qkb-shadow);
}

.qi-kb-form-group.kb-content-file.dragover::before {
    background: linear-gradient(135deg, rgba(74, 222, 128, 0.1) 0%, rgba(74, 222, 128, 0.05) 100%);
    opacity: 1;
}

.qi-kb-form-group.kb-content-file.dragover::after {
    content: '📄 Drop your file here';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: var(--qkb-font-size-lg);
    font-weight: 600;
    color: var(--qkb-success);
    background-color: rgba(255, 255, 255, 0.9);
    padding: var(--qkb-gap-md);
    border-radius: var(--qkb-radius-sm);
    z-index: 10;
    animation: qipulse 1.5s infinite;
}


.qi-kb-form-group.kb-content-file .file-input-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--qkb-gap-md);
    text-align: center;
    position: relative;
    z-index: 1;
}

.qi-kb-form-group.kb-content-file .file-input {
    display: none;
}

.qi-kb-form-group.kb-content-file .file-input-label {
    display: inline-flex;
    align-items: center;
    gap: var(--qkb-gap);
    padding: var(--qkb-padding-sm);
    background: var(--qkb-primary);
    color: var(--qkb-bg);
    border-radius: var(--qkb-radius-xs);
    cursor: pointer;
    transition: all 0.2s var(--qkb-transition);
    font-weight: 600;
    position: relative;
    overflow: hidden;
    -webkit-border-radius: var(--qkb-radius-xs);
    -moz-border-radius: var(--qkb-radius-xs);
    -ms-border-radius: var(--qkb-radius-xs);
    -o-border-radius: var(--qkb-radius-xs);
}

.file-formats {
    font-size: var(--qkb-font-size-xs);
    color: var(--qkb-text-light);
    margin-top: var(--qkb-gap-sm);
    font-weight:bold;
}

.qi-kb-form-group.kb-content-file .file-input-label::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.3s var(--qkb-transition);
}

.qi-kb-form-group.kb-content-file .file-input-label:hover {
    transform: translateY(-2px);
    box-shadow: var(--qkb-shadow);
}

.qi-kb-form-group.kb-content-file .file-input-label:hover::before {
    transform: translateX(0);
}

.qi-kb-form-group.kb-content-file .file-input-label i {
    font-size: 1.25rem;
}

.qi-kb-form-group.kb-content-file .file-name {
    font-size: var(--qkb-font-size-sm);
    color: var(--qkb-text);
    width: 100%;
    max-width: 100%;
    background-color: var(--qkb-primary-light);
    padding: var(--qkb-padding-md);
    border-radius: var(--qkb-radius-xs);
    border: 1px solid var(--qkb-border-light);
    transition: all 0.2s var(--qkb-transition);
    margin-top: var(--qkb-gap);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--qkb-gap);
    position: relative;
    -webkit-border-radius: var(--qkb-radius-xs);
    -moz-border-radius: var(--qkb-radius-xs);
    -ms-border-radius: var(--qkb-radius-xs);
    -o-border-radius: var(--qkb-radius-xs);
}

.qi-kb-form-group.kb-content-file .file-name .file-icon {
    flex: 0 0 auto;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--qkb-primary-light);
    border-radius: var(--qkb-radius-xs);
    color: var(--qkb-primary);
    font-size: var(--qkb-font-size-lg);
    -webkit-border-radius: var(--qkb-radius-xs);
    -moz-border-radius: var(--qkb-radius-xs);
    -ms-border-radius: var(--qkb-radius-xs);
    -o-border-radius: var(--qkb-radius-xs);
}

.qi-kb-form-group.kb-content-file .file-name .file-details {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.qi-kb-form-group.kb-content-file .file-name .file-name-text {
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.qi-kb-form-group.kb-content-file .file-name .file-size {
    font-size: var(--qkb-font-size-xs);
    color: var(--qkb-text-light);
}

.qi-kb-form-group.kb-content-file .file-name .file-remove-btn {
    flex: 0 0 auto;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: none;
    border: none;
    color: var(--qkb-text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s var(--qkb-transition);
}

.qi-kb-form-group.kb-content-file .file-name .file-remove-btn:hover {
    background-color: rgba(var(--qkb-error-rgb, 220, 38, 38), 0.1);
    color: var(--qkb-error);
    transform: scale(1.1);
}

.qi-kb-form-group.kb-content-file .upload-progress {
    width: 100%;
    height: 6px;
    background: var(--qkb-bg-light);
    margin-top: var(--qkb-gap-md);
    overflow: hidden;
    display: none;
    position: relative;
}

.qi-kb-form-group.kb-content-file .upload-progress-bar {
    width: 0%;
    height: 100%;
    background: linear-gradient(90deg, var(--qkb-primary), var(--qkb-accent-blue));
    transition: width 0.3s var(--qkb-transition);
    position: relative;
    overflow: hidden;
}

.qi-kb-form-group.kb-content-file .upload-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
    transform: translateX(-100%);
    animation: progressShimmer 1.5s infinite;
}


.qi-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.qi-loading-spinner {
    border: 4px solid var(--qkb-border);
    border-top: 4px solid var(--qkb-primary);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

.qi-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--qkb-gap-xl);
    color: var(--qkb-text-light);
    font-size: var(--qkb-font-size-md);
    min-height: 200px;
    background-color: var(--qkb-bg-alt);
    margin: var(--qkb-gap-lg) 0;
}

.qi-loading i {
    margin-right: var(--qkb-gap);
    font-size: var(--qkb-font-size-lg);
    color: var(--qkb-primary);
}


.fa-spin {
    animation: spin 1s linear infinite;
}