<?php
if (!defined('ABSPATH')) {
    exit;
}

class QKB_OpenAI_Embedding extends QKB_OpenAI_Base {
    public function get_embedding($text) {
        try {
            $this->rate_limiter->throttle();
            
            if (empty($this->api_key)) {
                return new WP_Error('missing_api_key', __('API key is not configured', 'q-knowledge-base'));
            }

            // Check cache first
            $cache_key = 'embedding_' . md5($text);
            $cached = $this->cache->get($cache_key);
            if ($cached) {
                return $cached;
            }

            $response = $this->make_request('embeddings', [
                'model' => $this->embedding_model,
                'input' => $text
            ]);

            if (is_wp_error($response)) {
                return $response;
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);

            if (isset($body['error'])) {
                return new WP_Error('openai_error', $body['error']['message']);
            }

            $embedding = $body['data'][0]['embedding'];
            
            // Cache the embedding
            $this->cache->set($cache_key, $embedding, 24 * HOUR_IN_SECONDS);
            
            return $embedding;

        } catch (Exception $e) {
            $this->error_handler->log_error('Embedding generation error', $e);
            return new WP_Error('embedding_error', $e->getMessage());
        }
    }

    public function batch_get_embeddings($texts) {
        $embeddings = [];
        foreach ($texts as $text) {
            $embedding = $this->get_embedding($text);
            if (!is_wp_error($embedding)) {
                $embeddings[] = $embedding;
            }
        }
        return $embeddings;
    }
}
