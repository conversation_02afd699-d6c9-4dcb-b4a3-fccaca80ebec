<?php

/**
 * Database optimization class for Q-Knowledge Base
 * Handles database indexing and performance optimizations
 */
class QKB_Database_Optimizer {
    
    /**
     * Initialize database optimizations
     */
    public static function init() {
        add_action('init', [__CLASS__, 'maybe_optimize_database']);
        add_action('qkb_optimize_database', [__CLASS__, 'optimize_database']);
    }
    
    /**
     * Check if database optimization is needed
     */
    public static function maybe_optimize_database() {
        $last_optimization = get_option('qkb_last_db_optimization', 0);
        $optimization_interval = 7 * DAY_IN_SECONDS; // Weekly optimization
        
        if (time() - $last_optimization > $optimization_interval) {
            wp_schedule_single_event(time() + 60, 'qkb_optimize_database');
        }
    }
    
    /**
     * Optimize database for better performance
     */
    public static function optimize_database() {
        global $wpdb;
        
        try {
            // Add indexes for knowledge base search
            self::add_search_indexes();
            
            // Add indexes for taxonomy relationships
            self::add_taxonomy_indexes();
            
            // Add indexes for meta queries
            self::add_meta_indexes();
            
            // Optimize tables
            self::optimize_tables();
            
            // Update last optimization time
            update_option('qkb_last_db_optimization', time());
            
            error_log('QKB Database optimization completed successfully');
            
        } catch (Exception $e) {
            error_log('QKB Database optimization failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Add indexes for knowledge base search performance
     */
    private static function add_search_indexes() {
        global $wpdb;
        
        // Index for post type and status filtering
        $index_name = 'qkb_post_type_status_idx';
        if (!self::index_exists($wpdb->posts, $index_name)) {
            $wpdb->query("
                ALTER TABLE {$wpdb->posts} 
                ADD INDEX {$index_name} (post_type, post_status, post_date)
            ");
        }
        
        // Full-text index for content search
        $fulltext_index = 'qkb_content_fulltext_idx';
        if (!self::index_exists($wpdb->posts, $fulltext_index)) {
            $wpdb->query("
                ALTER TABLE {$wpdb->posts} 
                ADD FULLTEXT {$fulltext_index} (post_title, post_content)
            ");
        }
        
        // Index for post title searches
        $title_index = 'qkb_post_title_idx';
        if (!self::index_exists($wpdb->posts, $title_index)) {
            $wpdb->query("
                ALTER TABLE {$wpdb->posts} 
                ADD INDEX {$title_index} (post_title(50))
            ");
        }
    }
    
    /**
     * Add indexes for taxonomy relationships
     */
    private static function add_taxonomy_indexes() {
        global $wpdb;
        
        // Index for term relationships
        $tr_index = 'qkb_term_rel_idx';
        if (!self::index_exists($wpdb->term_relationships, $tr_index)) {
            $wpdb->query("
                ALTER TABLE {$wpdb->term_relationships} 
                ADD INDEX {$tr_index} (object_id, term_taxonomy_id)
            ");
        }
        
        // Index for term taxonomy
        $tt_index = 'qkb_term_taxonomy_idx';
        if (!self::index_exists($wpdb->term_taxonomy, $tt_index)) {
            $wpdb->query("
                ALTER TABLE {$wpdb->term_taxonomy} 
                ADD INDEX {$tt_index} (taxonomy, term_id)
            ");
        }
    }
    
    /**
     * Add indexes for meta queries
     */
    private static function add_meta_indexes() {
        global $wpdb;
        
        // Index for postmeta
        $meta_index = 'qkb_postmeta_idx';
        if (!self::index_exists($wpdb->postmeta, $meta_index)) {
            $wpdb->query("
                ALTER TABLE {$wpdb->postmeta} 
                ADD INDEX {$meta_index} (post_id, meta_key(50), meta_value(50))
            ");
        }
    }
    
    /**
     * Optimize database tables
     */
    private static function optimize_tables() {
        global $wpdb;
        
        $tables = [
            $wpdb->posts,
            $wpdb->postmeta,
            $wpdb->term_relationships,
            $wpdb->term_taxonomy,
            $wpdb->terms
        ];
        
        foreach ($tables as $table) {
            $wpdb->query("OPTIMIZE TABLE {$table}");
        }
    }
    
    /**
     * Check if an index exists on a table
     */
    private static function index_exists($table, $index_name) {
        global $wpdb;
        
        $result = $wpdb->get_var($wpdb->prepare(
            "SHOW INDEX FROM {$table} WHERE Key_name = %s",
            $index_name
        ));
        
        return !empty($result);
    }
    
    /**
     * Get database performance statistics
     */
    public static function get_performance_stats() {
        global $wpdb;
        
        $stats = [];
        
        // Get table sizes
        $table_stats = $wpdb->get_results("
            SELECT 
                table_name,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                table_rows
            FROM information_schema.TABLES 
            WHERE table_schema = DATABASE()
            AND table_name IN ('{$wpdb->posts}', '{$wpdb->postmeta}', '{$wpdb->term_relationships}')
        ");
        
        $stats['tables'] = $table_stats;
        
        // Get index usage
        $index_stats = $wpdb->get_results("
            SELECT 
                table_name,
                index_name,
                cardinality
            FROM information_schema.STATISTICS 
            WHERE table_schema = DATABASE()
            AND table_name IN ('{$wpdb->posts}', '{$wpdb->postmeta}', '{$wpdb->term_relationships}')
            AND index_name LIKE 'qkb_%'
        ");
        
        $stats['indexes'] = $index_stats;
        
        // Get slow query log status
        $slow_query_log = $wpdb->get_var("SHOW VARIABLES LIKE 'slow_query_log'");
        $stats['slow_query_log'] = $slow_query_log;
        
        return $stats;
    }
    
    /**
     * Clean up old cache entries
     */
    public static function cleanup_cache() {
        global $wpdb;
        
        // Clean up old transients
        $wpdb->query("
            DELETE FROM {$wpdb->options} 
            WHERE option_name LIKE '_transient_timeout_qkb_%' 
            AND option_value < UNIX_TIMESTAMP()
        ");
        
        $wpdb->query("
            DELETE FROM {$wpdb->options} 
            WHERE option_name LIKE '_transient_qkb_%' 
            AND option_name NOT IN (
                SELECT CONCAT('_transient_', SUBSTRING(option_name, 19))
                FROM {$wpdb->options} 
                WHERE option_name LIKE '_transient_timeout_qkb_%'
            )
        ");
    }
    
    /**
     * Get optimization recommendations
     */
    public static function get_optimization_recommendations() {
        global $wpdb;
        
        $recommendations = [];
        
        // Check for missing indexes
        $missing_indexes = self::check_missing_indexes();
        if (!empty($missing_indexes)) {
            $recommendations[] = [
                'type' => 'missing_indexes',
                'message' => 'Some performance indexes are missing',
                'details' => $missing_indexes
            ];
        }
        
        // Check table sizes
        $large_tables = self::check_large_tables();
        if (!empty($large_tables)) {
            $recommendations[] = [
                'type' => 'large_tables',
                'message' => 'Some tables are getting large and may need optimization',
                'details' => $large_tables
            ];
        }
        
        return $recommendations;
    }
    
    /**
     * Check for missing performance indexes
     */
    private static function check_missing_indexes() {
        $missing = [];
        
        $required_indexes = [
            $GLOBALS['wpdb']->posts => ['qkb_post_type_status_idx', 'qkb_content_fulltext_idx'],
            $GLOBALS['wpdb']->term_relationships => ['qkb_term_rel_idx'],
            $GLOBALS['wpdb']->postmeta => ['qkb_postmeta_idx']
        ];
        
        foreach ($required_indexes as $table => $indexes) {
            foreach ($indexes as $index) {
                if (!self::index_exists($table, $index)) {
                    $missing[] = "{$table}.{$index}";
                }
            }
        }
        
        return $missing;
    }
    
    /**
     * Check for large tables that may need optimization
     */
    private static function check_large_tables() {
        global $wpdb;
        
        $large_tables = [];
        $size_threshold = 100; // MB
        
        $table_sizes = $wpdb->get_results("
            SELECT 
                table_name,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
            FROM information_schema.TABLES 
            WHERE table_schema = DATABASE()
            AND table_name IN ('{$wpdb->posts}', '{$wpdb->postmeta}', '{$wpdb->term_relationships}')
            HAVING size_mb > {$size_threshold}
        ");
        
        foreach ($table_sizes as $table) {
            $large_tables[] = [
                'table' => $table->table_name,
                'size_mb' => $table->size_mb
            ];
        }
        
        return $large_tables;
    }
}

// Initialize the database optimizer
QKB_Database_Optimizer::init();
