<?php
/**
 * QKB Chatbot Handler
 */

if (!defined('ABSPATH')) {
    exit;
}

// Ensure WordPress core is loaded
require_once(ABSPATH . 'wp-includes/pluggable.php');
require_once(ABSPATH . 'wp-includes/functions.php');
require_once(ABSPATH . 'wp-admin/includes/plugin.php');

// Load renderer classes
require_once QKB_PLUGIN_DIR . 'includes/chat/class-qkb-chat-renderer.php';
require_once QKB_PLUGIN_DIR . 'includes/chat/class-qkb-chatbot-renderer.php';
require_once QKB_PLUGIN_DIR . 'includes/chat/class-qkb-chat-interface-renderer.php';

class QKB_Chatbot
{

    private $dependencies_met = true;
    private $ml_handler;
    private $chatbot_renderer;
    private $chat_interface_renderer;

    public function __construct()
    {
        if (!function_exists('add_action')) {
            $this->dependencies_met = false;
            return;
        }

        if (!function_exists('get_option')) {
            require_once ABSPATH . 'wp-includes/option.php';
        }

        require_once QKB_PLUGIN_DIR . 'includes/class-qkb-ml-handler.php';
        $this->ml_handler = QKB_ML_Handler::get_instance();

        // Initialize renderers
        $this->chatbot_renderer = new QKB_Chatbot_Renderer();
        $this->chat_interface_renderer = new QKB_Chat_Interface_Renderer();

        if ($this->dependencies_met && !$this->is_excluded_page()) {
            $this->init_hooks();
        }

        // Add AJAX hooks for lazy-loading the chat interface
        add_action('wp_ajax_qkb_load_chatbot', [$this, 'ajax_load_chatbot']);
        add_action('wp_ajax_nopriv_qkb_load_chatbot', [$this, 'ajax_load_chatbot']);
    }

    private function init_hooks()
    {
        add_action('wp_enqueue_scripts', [$this, 'enqueue_assets']);
        add_action('wp_footer', [$this, 'maybe_render_chatbot']);
        add_action('wp_ajax_qkb_submit_feedback', [$this, 'handle_feedback']);
        add_action('wp_ajax_nopriv_qkb_submit_feedback', [$this, 'handle_feedback']);
        add_action('wp_ajax_qkb_chat_request', [$this, 'handle_chat_request']);
        add_action('wp_ajax_nopriv_qkb_chat_request', [$this, 'handle_chat_request']);
        add_action('wp_ajax_qkb_save_chat_history', [$this, 'save_chat_history']);
        add_action('wp_ajax_qkb_delete_chat_history', [$this, 'delete_chat_history']);
        add_action('wp_ajax_qkb_clear_chat_history', [$this, 'clear_chat_history']);
        add_action('wp_ajax_qkb_get_chat_history', [$this, 'get_chat_history']);
        add_action('wp_ajax_qkb_load_chat_history_item', [$this, 'load_chat_history_item']);
        add_action('wp_ajax_qkb_update_chat_history', [$this, 'update_chat_history']);
        add_action('wp_ajax_qkb_export_transcript', [$this, 'export_transcript']);
        add_action('wp_ajax_nopriv_qkb_export_transcript', [$this, 'export_transcript']);
        add_action('wp_ajax_qkb_save_current_assistant', [$this, 'save_current_assistant']);

        add_shortcode('qkb_chat_interface', function () {
            // Call render_chat_interface and echo its return value
            echo $this->render_chat_interface();
        });
    }

    private function is_excluded_page()
    {
        $excluded_urls = get_option('qkb_excluded_urls', []);

        if (!is_array($excluded_urls)) {
            $excluded_urls = [];
        }

        if (empty($excluded_urls)) {
            return false;
        }

        $current_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

        $current_url = untrailingslashit(esc_url_raw($current_url));
        $excluded_urls = array_map('untrailingslashit', $excluded_urls);

        return in_array($current_url, $excluded_urls);
    }



    public function enqueue_assets()
    {
        // Core scripts
        wp_enqueue_script('jquery');
        wp_enqueue_script('jquery-migrate');

        // Font Awesome (using a more reliable CDN)
        wp_enqueue_style(
            'fontawesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
            array(),
            '6.4.0'
        );

        // Particles.js for welcome message background
        wp_enqueue_script('particles-js', 'https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js', array('jquery'), '2.0.0', true);

        // Prism.js for syntax highlighting
        wp_enqueue_script('prism-js', 'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js', array(), '1.29.0', true);
        wp_enqueue_script('prism-autoloader', 'https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js', array('prism-js'), '1.29.0', true);

        // Plugin styles
        wp_enqueue_style('qkb-root-style', QKB_PLUGIN_URL . 'assets/css/root-style.css', array('fontawesome'), QKB_VERSION);
        wp_enqueue_style('qkb-chatbot-css', QKB_PLUGIN_URL . 'assets/css/chatbot/qkb-chatbot.css', array('qkb-root-style'), QKB_VERSION);
        wp_enqueue_style('qkb-quick-access-buttons', QKB_PLUGIN_URL . 'assets/css/chatbot/quick-access-buttons.css', array('qkb-chatbot-css'), QKB_VERSION);
        wp_enqueue_style('qkb-formidable-forms-handler', QKB_PLUGIN_URL . 'assets/css/formidable-forms-handler.css', array('qkb-chatbot-css'), QKB_VERSION);
        wp_enqueue_style('qi-keyframe-style', QKB_PLUGIN_URL . 'assets/css/chat-interface/qi-keyframe.css', array('fontawesome'), QKB_VERSION);
        wp_enqueue_style('qi-footer-style', QKB_PLUGIN_URL . 'assets/css/chat-interface/qi-footer.css', array('fontawesome'), QKB_VERSION);
        wp_enqueue_style('qi-container-style', QKB_PLUGIN_URL . 'assets/css/chat-interface/qi-container.css', array('fontawesome'), QKB_VERSION);
        wp_enqueue_style('qi-sidebar-style', QKB_PLUGIN_URL . 'assets/css/chat-interface/qi-sidebar.css', array('fontawesome'), QKB_VERSION);
        wp_enqueue_style('qi-header-style', QKB_PLUGIN_URL . 'assets/css/chat-interface/qi-header.css', array('fontawesome'), QKB_VERSION);
        wp_enqueue_style('qi-input-style', QKB_PLUGIN_URL . 'assets/css/chat-interface/qi-input.css', array('fontawesome'), QKB_VERSION);
        wp_enqueue_style('qi-notification-style', QKB_PLUGIN_URL . 'assets/css/chat-interface/qi-notification.css', array('fontawesome'), QKB_VERSION);
        wp_enqueue_style('qi-message-style', QKB_PLUGIN_URL . 'assets/css/chat-interface/qi-message.css', array('fontawesome'), QKB_VERSION);
        wp_enqueue_style('qi-prism-style', QKB_PLUGIN_URL . 'assets/css/chat-interface/qi-prism.css', array('qi-message-style'), QKB_VERSION);
        wp_enqueue_style('qi-suggestion-style', QKB_PLUGIN_URL . 'assets/css/chat-interface/qi-suggestion.css', array('fontawesome'), QKB_VERSION);
        wp_enqueue_style('qi-message-skeleton-style', QKB_PLUGIN_URL . 'assets/css/chat-interface/qi-message-skeleton.css', array('fontawesome'), QKB_VERSION);
        wp_enqueue_style('qi-kb-modal-style', QKB_PLUGIN_URL . 'assets/css/chat-interface/qi-kb-modal.css', array('fontawesome'), QKB_VERSION);
        wp_enqueue_style('qi-export-modal-style', QKB_PLUGIN_URL . 'assets/css/chat-interface/qi-export-modal.css', array('fontawesome'), QKB_VERSION);
        wp_enqueue_style('qi-modal-style', QKB_PLUGIN_URL . 'assets/css/chat-interface/qi-modal.css', array('fontawesome'), QKB_VERSION);
        wp_enqueue_style('qi-chat-style', QKB_PLUGIN_URL . 'assets/css/chat-interface/qi-chat.css', array('fontawesome'), QKB_VERSION);
        wp_enqueue_style('qkb-formidable-forms-handler', QKB_PLUGIN_URL . 'assets/css/formidable-forms-handler.css', array('qkb-chatbot-css'), QKB_VERSION);






        // Plugin scripts - ensure proper dependencies
        wp_enqueue_script('qi-chat-script', QKB_PLUGIN_URL . 'assets/js/qi-chat.js', array('jquery', 'jquery-migrate'), '1.0.0', true);

        // Localize before dependent scripts
        $current_user = wp_get_current_user();
        $user_display_name = $current_user->display_name ?: $current_user->user_login;
        wp_localize_script('qi-chat-script', 'qi_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('qi_chat_nonce'),
            'plugin_url' => QKB_PLUGIN_URL,
            'show_sidebar' => get_option('qi_show_sidebar', 'yes'),
            'i18n' => array(
                'sending' => __('Sending...', 'q-intelligent'),
                'error' => __('Error occurred. Please try again.', 'q-intelligent'),
                'clear_confirm' => __('Clear chat history?', 'q-intelligent'),
                'clear_history_confirm' => __('Are you sure you want to clear chat history for this assistant? This cannot be undone.', 'q-knowledge-base'),
                'rate_limit' => __('Rate limit exceeded. Please wait before sending another message.', 'q-intelligent'),
                'rate_limit_with_time' => __('Rate limit exceeded. Please wait %d minute(s) before sending another message.', 'q-intelligent'),
                'delete_chat_confirm' => __('Are you sure you want to delete this chat? This cannot be undone.', 'q-knowledge-base'),
            )
        ));

        // Dependent scripts
        wp_enqueue_script('qkb-chatbot', QKB_PLUGIN_URL . 'assets/js/chatbot.js', array('jquery', 'qi-chat-script'), '1.0.0', true);
        wp_enqueue_script('qkb-formidable-forms-handler', QKB_PLUGIN_URL . 'assets/js/formidable-forms-handler.js', array('jquery', 'qkb-chatbot'), QKB_VERSION, true);

        // Get the selected assistant ID
        $selected_assistant_id = get_option('qkb_default_chatbot_assistant', 0);
        if ($selected_assistant_id === 0) {
            $selected_assistant_id = $this->get_default_assistant_id();
        }

        wp_localize_script('qkb-chatbot', 'qkbChatbot', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('qkb_ajax_nonce'),
            'plugin_url' => QKB_PLUGIN_URL,
            'prefixText' => __('Tell me more about', 'q-knowledge-base'),
            'defaultassistant' => $this->get_default_assistant_id(),
            'selectedassistant' => $selected_assistant_id,
            'user_name' => $user_display_name,
            'chatbot_name' => get_option('qkb_chatbot_name', 'Ask Q'),
            'chatbot_icon' => get_option('qkb_chatbot_icon', QKB_PLUGIN_URL . 'assets/images/q.svg'),
            'splash_subtitle' => get_option('qkb_splash_subtitle', 'Your AI Assistant'),
            'splashScreenConfig' => [
                'delay' => 4500,
                'animationDuration' => 1000
            ]
        ]);

        // Get color settings for different interfaces
        $global_primary_color = get_option('qkb_primary_color', '#2271b1');
        $chatbot_primary_color = get_option('qkb_chatbot_primary_color', $global_primary_color);
        $interface_primary_color = get_option('qkb_chat_interface_primary_color', $global_primary_color);

        // Process colors for chatbot
        $chatbot_rgb = $this->hex2rgb($chatbot_primary_color);
        $chatbot_css = "
            .qkb-chat-button,
            .qkb-chatbot-container {
                --qkb-primary: {$chatbot_primary_color};
                --qkb-primary-dark: " . $this->adjustBrightness($chatbot_primary_color, -20) . ";
                --qkb-primary-light: rgba({$chatbot_rgb['r']}, {$chatbot_rgb['g']}, {$chatbot_rgb['b']}, 0.1);
                --qkb-gradient: linear-gradient(135deg, {$chatbot_primary_color}, " . $this->adjustBrightness($chatbot_primary_color, -20) . ");
            }
        ";

        // Process colors for chat interface
        $interface_rgb = $this->hex2rgb($interface_primary_color);
        $interface_css = "
            .qi-chat-container {
                --qkb-primary: {$interface_primary_color};
                --qkb-primary-dark: " . $this->adjustBrightness($interface_primary_color, -20) . ";
                --qkb-primary-light: rgba({$interface_rgb['r']}, {$interface_rgb['g']}, {$interface_rgb['b']}, 0.1);
                --qkb-gradient: linear-gradient(135deg, {$interface_primary_color}, " . $this->adjustBrightness($interface_primary_color, -20) . ");
            }
        ";

        // For backward compatibility, set global variables too
        $global_rgb = $this->hex2rgb($global_primary_color);
        $global_css = "
            :root {
                --qkb-primary: {$global_primary_color};
                --qkb-primary-dark: " . $this->adjustBrightness($global_primary_color, -20) . ";
                --qkb-primary-light: rgba({$global_rgb['r']}, {$global_rgb['g']}, {$global_rgb['b']}, 0.1);
                --qkb-gradient: linear-gradient(135deg, {$global_primary_color}, " . $this->adjustBrightness($global_primary_color, -20) . ");
            }
        ";

        // Combine all CSS
        $custom_css = $global_css . $chatbot_css . $interface_css;

        wp_add_inline_style('qkb-root-style', $custom_css);
    }

    private function hex2rgb($hex)
    {
        $hex = str_replace('#', '', $hex);

        if (strlen($hex) == 3) {
            $r = hexdec(substr($hex, 0, 1) . substr($hex, 0, 1));
            $g = hexdec(substr($hex, 1, 1) . substr($hex, 1, 1));
            $b = hexdec(substr($hex, 2, 1) . substr($hex, 2, 1));
        } else {
            $r = hexdec(substr($hex, 0, 2));
            $g = hexdec(substr($hex, 2, 2));
            $b = hexdec(substr($hex, 4, 2));
        }

        return ['r' => $r, 'g' => $g, 'b' => $b];
    }

    private function adjustBrightness($hex, $steps)
    {
        $hex = str_replace('#', '', $hex);

        $r = max(0, min(255, hexdec(substr($hex, 0, 2)) + $steps));
        $g = max(0, min(255, hexdec(substr($hex, 2, 2)) + $steps));
        $b = max(0, min(255, hexdec(substr($hex, 4, 2)) + $steps));

        return sprintf("#%02x%02x%02x", $r, $g, $b);
    }

    public function render_chatbot()
    {
        $this->chatbot_renderer->render();
    }



    public function handle_chat_request()
    {
        // Start performance monitoring
        $performance_monitor = QKB_Performance_Monitor::get_instance();
        $performance_monitor->start_timer('chat_request');
        $performance_monitor->record_memory_usage('chat_request_start');

        // Check for either nonce
        $nonce_verified = false;
        if (isset($_POST['nonce'])) {
            if (
                check_ajax_referer('qkb_ajax_nonce', 'nonce', false) ||
                check_ajax_referer('qi_chat_nonce', 'nonce', false)
            ) {
                $nonce_verified = true;
            }
        }

        if (!$nonce_verified) {
            $performance_monitor->end_timer('chat_request', ['error' => 'invalid_nonce']);
            wp_send_json_error('Invalid nonce');
            return;
        }

        $message = sanitize_text_field($_POST['message']);
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;
        $has_attachment = isset($_POST['has_attachment']) ? (bool) $_POST['has_attachment'] : false;
        $file_attachment = null;
        $file_content = '';

        // Log the assistant ID for debugging
        error_log('Received assistant_id: ' . $assistant_id . ' (type: ' . gettype($assistant_id) . ')');

        // Check if we're using the default assistant flag
        $is_default_assistant = isset($_POST['is_default_assistant']) ? filter_var($_POST['is_default_assistant'], FILTER_VALIDATE_BOOLEAN) : false;
        if ($is_default_assistant) {
            error_log('Using default assistant flag');
            $default_assistant = get_term_by('slug', 'knowledge-base', 'kb_assistant');
            if ($default_assistant) {
                $assistant_id = $default_assistant->term_id;
                error_log('Set assistant_id to default: ' . $assistant_id);
            }
        }

        $user = wp_get_current_user();
        $is_logged_in = $user->exists();

        if (!$is_logged_in && !$assistant_id) {
            $default_assistant = get_term_by('slug', 'knowledge-base', 'kb_assistant');
            if ($default_assistant) {
                $assistant_id = $default_assistant->term_id;
                error_log('Set assistant_id to default for non-logged in user: ' . $assistant_id);
            }
        } else if ($assistant_id) {
            $user_roles = $user->roles;
            $assistant_roles = get_option('qkb_assistant_roles', []);

            if (isset($assistant_roles[$assistant_id]) && !empty($assistant_roles[$assistant_id])) {
                if (!array_intersect($user_roles, $assistant_roles[$assistant_id])) {
                    wp_send_json_error('You do not have permission to access this assistant');
                    return;
                }
            }
        }

        if (strlen($message) > 500) {
            wp_send_json_error('Message exceeds 500 character limit');
            return;
        }

        // File attachment functionality has been removed

        require_once QKB_PLUGIN_DIR . 'includes/class-qkb-openai-handler.php';
        $openai = new QKB_OpenAI_Handler();

        // Check for affirmative responses and add context
        $affirmative_responses = ['yes', 'no'];
        $message_lower = strtolower($message);
        if (in_array($message_lower, $affirmative_responses)) {
            $chat_history = $_SESSION['chat_history'] ?? [];
            if (!empty($chat_history)) {
                $last_bot_message = end($chat_history)['bot'] ?? '';
                $message = $last_bot_message . ' ' . ucfirst($message_lower) . '.';
            }
        }

        $conversation_context = [
            'history' => array_slice($_SESSION['chat_history'] ?? [], -10),
            'user_profile' => [
                'roles' => $user->roles,
                'interaction_count' => get_user_meta($user->ID, 'qkb_interaction_count', true) ?: 0
            ],

        ];

        // Log the assistant ID before searching
        error_log('Searching knowledge base with message: ' . $message . ' and assistant ID: ' . $assistant_id);

        // Make sure we have a valid assistant ID
        if (empty($assistant_id)) {
            $default_assistant = get_term_by('slug', 'knowledge-base', 'kb_assistant');
            if ($default_assistant) {
                $assistant_id = $default_assistant->term_id;
                error_log('Using default assistant ID: ' . $assistant_id);
            }
        }

        $search_results = $openai->search_knowledge_base($message, $assistant_id);

        // Set default values for all possible response components
        if (!isset($search_results['response_type'])) {
            $search_results['response_type'] = 'direct_answer';
        }
        if (!isset($search_results['related_posts'])) {
            $search_results['related_posts'] = [];
        }
        if (!isset($search_results['confidence_score'])) {
            $search_results['confidence_score'] = 0; // Default confidence score
        }

        // File content handling has been removed

        $response = $openai->get_completion(
            $message,
            $search_results['context'],
            $assistant_id,
            $conversation_context
        );

        if (is_wp_error($response)) {
            wp_send_json_error($response->get_error_message());
            return;
        }

        // Update after successful response
        if (!headers_sent() && session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $_SESSION['chat_history'][] = [
            'user' => $message,
            'bot' => $response,
            'timestamp' => time()
        ];

        // Keep only last 5 exchanges
        $_SESSION['chat_history'] = array_slice($_SESSION['chat_history'], -10);

        // End performance monitoring
        $duration = $performance_monitor->end_timer('chat_request', [
            'message_length' => strlen($message),
            'assistant_id' => $assistant_id,
            'has_attachment' => $has_attachment,
            'response_type' => $search_results['response_type'] ?? 'direct_answer',
            'confidence' => $search_results['confidence_score'] ?? 0
        ]);

        // Log slow requests
        if ($duration > 5.0) {
            $performance_monitor->log_performance_alert(
                'Slow chat request detected',
                [
                    'duration' => $duration,
                    'message_length' => strlen($message),
                    'assistant_id' => $assistant_id
                ]
            );
        }

        $performance_monitor->record_memory_usage('chat_request_end');

        // Ensure response is properly formatted for frontend processing
        // Don't escape HTML here as the frontend will handle markdown formatting
        wp_send_json_success([
            'message' => $response,
            'sources' => $search_results['sources'] ?? [],
            'related_articles' => $search_results['related_posts'] ?? [],
            'confidence' => $search_results['confidence_score'] ?? 0,
            'type' => $search_results['response_type'] ?? 'direct_answer'
        ]);
    }


    public function handle_feedback()
    {
        // Verify nonce
        $nonce_verified = false;
        if (isset($_POST['nonce'])) {
            if (
                check_ajax_referer('qkb_ajax_nonce', 'nonce', false) ||
                check_ajax_referer('qi_chat_nonce', 'nonce', false)
            ) {
                $nonce_verified = true;
            }
        }

        if (!$nonce_verified) {
            wp_send_json_error('Invalid security token');
            return;
        }

        // Check if we're handling the old format (interaction_id) or new format (message_id)
        if (isset($_POST['interaction_id'])) {
            // Old format
            $interaction_id = isset($_POST['interaction_id']) ? intval($_POST['interaction_id']) : 0;
            $feedback = isset($_POST['feedback']) ? floatval($_POST['feedback']) : 0;

            if (!$interaction_id) {
                wp_send_json_error('Invalid interaction ID.');
                return;
            }

            // Record feedback using the ML handler.
            $ml_handler = QKB_ML_Handler::get_instance();
            $ml_handler->record_interaction_feedback($interaction_id, $feedback);
        } else {
            // New format from qi-chat.js
            $feedback_type = isset($_POST['feedback_type']) ? sanitize_text_field($_POST['feedback_type']) : '';
            $message_id = isset($_POST['message_id']) ? sanitize_text_field($_POST['message_id']) : '';
            $message_content = isset($_POST['message_content']) ? sanitize_textarea_field($_POST['message_content']) : '';
            $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;

            if (empty($feedback_type) || empty($message_content)) {
                wp_send_json_error('Missing required feedback data');
                return;
            }

            // Get user info
            $user_id = get_current_user_id();
            $user_ip = $this->get_user_ip();

            // Store feedback in database
            $this->ml_handler->store_feedback([
                'user_id' => $user_id,
                'user_ip' => $user_ip,
                'feedback_type' => $feedback_type,
                'message' => $message_content,
                'message_id' => $message_id,
                'assistant_id' => $assistant_id,
                'timestamp' => current_time('mysql')
            ]);
        }

        wp_send_json_success('Feedback recorded.');
    }

    public function ajax_get_assistant_info()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;

        if (!$assistant_id) {
            wp_send_json_error('Invalid assistant ID');
            return;
        }

        $assistant = get_term($assistant_id, 'kb_assistant');
        if (is_wp_error($assistant) || !$assistant) {
            wp_send_json_error('assistant not found');
            return;
        }

        // Get assistant meta data
        $icon = get_term_meta($assistant_id, 'assistant_icon', true);
        $description = get_term_meta($assistant_id, 'assistant_description', true) ?: $assistant->description;



        wp_send_json_success([
            'name' => $assistant->name,
            'description' => $description ?: ' I’m here to help with anything you need. Just ask, and I’ll be happy to assist.',
            'icon' => $icon ?: 'fas fa-robot'
        ]);
    }

    public function maybe_render_chatbot()
    {
        if (is_singular() && has_shortcode(get_the_content(), 'qkb_chat_interface')) {
            return;
        }

        // Check user role visibility
        $allowed_roles = get_option('qkb_chatbot_visibility_roles', []);
        if (!empty($allowed_roles)) {
            $user = wp_get_current_user();
            $show_chatbot = false;

            // For logged-in users, check if they have an allowed role
            if ($user->exists()) {
                $user_roles = (array) $user->roles;
                $show_chatbot = array_intersect($user_roles, $allowed_roles) ? true : false;
            } else {
                // For non-logged-in users, don't show if roles are restricted
                $show_chatbot = false;
            }

            if (!$show_chatbot) {
                return; // Don't render the chatbot
            }
        }

        $chatbot_name = get_option('qkb_chatbot_name', 'Ask Q');
        $chatbot_icon = get_option('qkb_chatbot_icon', QKB_PLUGIN_URL . 'assets/images/q.svg');
        ?>
        <div class="qkb-chat-button" role="button" aria-label="Open Chat">
            <div class="qkb-chat-branding">
                <div class="qkb-bot-avatar">
                    <img src="<?php echo esc_url($chatbot_icon); ?>" alt="<?php echo esc_attr($chatbot_name); ?>"
                        class="qkb-avatar qkb-default-avatar">
                </div>
                <span class="qkb-chat-button-text"><?php echo esc_html($chatbot_name); ?></span>
            </div>

            <div>
                <button class="qkb-chatbot-control-button qkb-expand-button" title="Expand">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="18 15 12 9 6 15" />
                        </polyline>
                    </svg>
                </button>
            </div>

        </div>
        <?php
    }

    public function render_chat_interface()
    {
        return $this->chat_interface_renderer->render();
    }



    /**
     * Save chat history
     */
    public function save_chat_history()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('User not logged in');
            return;
        }

        $chat_data = array(
            'timestamp' => current_time('mysql'),
            'assistant_id' => isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0,
            'title' => isset($_POST['title']) ? sanitize_text_field($_POST['title']) : '',
            'messages' => isset($_POST['messages']) ? $this->sanitize_messages($_POST['messages']) : array()
        );

        $history = get_option('qkb_chat_history_' . $user_id, array());
        array_unshift($history, $chat_data); // Add new chat to beginning

        // Limit history to 50 items
        $history = array_slice($history, 0, 50);

        update_option('qkb_chat_history_' . $user_id, $history);

        wp_send_json_success();
    }

    /**
     * Delete chat history item
     */
    public function delete_chat_history()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('User not logged in');
            return;
        }

        $timestamp = isset($_POST['timestamp']) ? sanitize_text_field($_POST['timestamp']) : '';
        if (!$timestamp) {
            wp_send_json_error('Invalid timestamp');
            return;
        }

        $history = get_option('qkb_chat_history_' . $user_id, array());

        // Remove the chat with matching timestamp
        $history = array_filter($history, function ($item) use ($timestamp) {
            return $item['timestamp'] !== $timestamp;
        });

        update_option('qkb_chat_history_' . $user_id, array_values($history));

        wp_send_json_success();
    }

    /**
     * Clear chat history for specific assistant
     */
    public function clear_chat_history()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('User not logged in');
            return;
        }

        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;
        if (!$assistant_id) {
            wp_send_json_error('Invalid assistant ID');
            return;
        }

        $chat_history = get_option('qkb_chat_history_' . $user_id, array());

        // Filter out only the specified assistant's history
        $chat_history = array_filter($chat_history, function ($item) use ($assistant_id) {
            return !isset($item['assistant_id']) || $item['assistant_id'] !== $assistant_id;
        });

        update_option('qkb_chat_history_' . $user_id, array_values($chat_history));

        wp_send_json_success();
    }

    /**
     * Sanitize messages array
     */
    private function sanitize_messages($messages)
    {
        if (!is_array($messages)) {
            return array();
        }

        return array_map(function ($message) {
            // For chat messages, preserve markdown formatting but sanitize dangerous content
            $content = $message['content'];

            // Remove dangerous scripts and tags but preserve markdown and safe HTML
            $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $content);
            $content = preg_replace('/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi', '', $content);
            $content = preg_replace('/on\w+\s*=\s*["\'][^"\']*["\']/i', '', $content);

            return array(
                'content' => $content,
                'type' => sanitize_text_field($message['type']),
                'timestamp' => sanitize_text_field($message['timestamp'])
            );
        }, $messages);
    }

    /**
     * Get chat history HTML
     */
    public function get_chat_history()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('User not logged in');
            return;
        }


        // Get current assistant ID from request
        $current_assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;

        $chat_history = get_option('qkb_chat_history_' . $user_id, array());

        // Filter history for current assistant
        $assistant_history = array_filter($chat_history, function ($item) use ($current_assistant_id) {
            return isset($item['assistant_id']) && $item['assistant_id'] === $current_assistant_id;
        });

        // Build HTML output as a string
        $html = '';

        if (!empty($assistant_history)) {
            foreach ($assistant_history as $history_item) {
                $timestamp = isset($history_item['timestamp']) ? $history_item['timestamp'] : '';
                $title = isset($history_item['title']) ? esc_html($history_item['title']) : '';
                $assistant_name = '';

                if ($current_assistant_id) {
                    $assistant = get_term($current_assistant_id, 'kb_assistant');
                    if (!is_wp_error($assistant)) {
                        $assistant_name = $assistant->name;
                    }
                }

                $html .= '<div class="qi-history-item" data-chat-id="' . esc_attr($timestamp) . '"'
                    . ' data-assistant-id="' . esc_attr($current_assistant_id) . '">';
                $html .= '<div class="qi-history-assistant">' . esc_html($assistant_name) . '</div>';
                $html .= '<div class="qi-history-title">' . $title . '</div>';
                $html .= '<div class="qi-history-meta">';
                $html .= esc_html(human_time_diff(strtotime($timestamp), current_time('timestamp'))) . ' ago';
                $html .= '</div>';
                $html .= '<button class="qi-delete-history" title="' . esc_attr__('Delete conversation', 'q-knowledge-base') . '">';
                $html .= '<i class="fas fa-times"></i>';
                $html .= '</button>';
                $html .= '</div>';
            }
        } else {
            $html .= '<div class="qi-no-history">';
            $html .= '<div class="qi-no-history-icon"><i class="fas fa-history"></i></div>';
            $html .= '<p>' . __('No chat history yet', 'q-knowledge-base') . '</p>';
            $html .= '<p class="qi-no-history-desc">' . __('Your conversations will appear here once you start chatting.', 'q-knowledge-base') . '</p>';
            $html .= '</div>';
        }
        wp_send_json_success($html);
    }

    /**
     * Load specific chat history item
     */
    public function load_chat_history_item()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('User not logged in');
            return;
        }

        $timestamp = isset($_POST['timestamp']) ? sanitize_text_field($_POST['timestamp']) : '';
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;

        if (!$timestamp) {
            wp_send_json_error('Invalid timestamp');
            return;
        }

        $chat_history = get_option('qkb_chat_history_' . $user_id, array());

        // Find the specific chat
        $chat = null;
        foreach ($chat_history as $history_item) {
            if (
                $history_item['timestamp'] === $timestamp &&
                isset($history_item['assistant_id']) &&
                $history_item['assistant_id'] === $assistant_id
            ) {
                $chat = $history_item;
                break;
            }
        }

        if (!$chat) {
            wp_send_json_error('Chat not found');
            return;
        }

        wp_send_json_success($chat);
    }

    /**
     * Update existing chat history item
     */
    public function update_chat_history()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('User not logged in');
            return;
        }

        $chat_id = isset($_POST['chat_id']) ? sanitize_text_field($_POST['chat_id']) : '';
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;

        if (!$chat_id) {
            wp_send_json_error('Invalid chat ID');
            return;
        }

        $chat_history = get_option('qkb_chat_history_' . $user_id, array());

        // Find and update the specific chat
        foreach ($chat_history as $key => $history_item) {
            if (
                $history_item['timestamp'] === $chat_id &&
                isset($history_item['assistant_id']) &&
                $history_item['assistant_id'] === $assistant_id
            ) {

                $chat_history[$key]['messages'] = $this->sanitize_messages($_POST['messages']);
                // Update timestamp to move to top of list
                $chat_history[$key]['timestamp'] = current_time('mysql');

                // Move updated chat to top
                $updated_chat = $chat_history[$key];
                unset($chat_history[$key]);
                array_unshift($chat_history, $updated_chat);

                update_option('qkb_chat_history_' . $user_id, array_values($chat_history));
                wp_send_json_success();
                return;
            }
        }

        wp_send_json_error('Chat not found');
    }

    /**
     * Get assistant info for welcome message
     */
    public function get_assistant_info()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;

        if (!$assistant_id) {
            wp_send_json_error('Invalid assistant ID');
            return;
        }

        $assistant = get_term($assistant_id, 'kb_assistant');
        if (is_wp_error($assistant) || !$assistant) {
            wp_send_json_error('assistant not found');
            return;
        }

        // Get assistant meta data
        $icon = get_term_meta($assistant_id, 'assistant_icon', true);
        $description = get_term_meta($assistant_id, 'assistant_description', true);

        wp_send_json_success([
            'name' => $assistant->name,
            'description' => $description ?: ' I’m here to help with anything you need. Just ask, and I’ll be happy to assist.',
            'icon' => $icon ?: 'fas fa-robot',
        ]);
    }

    private function can_add_knowledge()
    {
        // Get allowed roles
        $allowed_roles = get_option('qkb_add_knowledge_roles', []);

        // Get current user's roles
        $user = wp_get_current_user();
        if (!$user->exists()) {
            return false;
        }

        // Check if user has any allowed role
        $user_roles = (array) $user->roles;
        return !empty(array_intersect($user_roles, $allowed_roles));
    }

    public function export_transcript()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        $messages = isset($_POST['messages']) ? $_POST['messages'] : [];
        $format = isset($_POST['format']) ? sanitize_text_field($_POST['format']) : 'pdf';
        $export_type = isset($_POST['export_type']) ? sanitize_text_field($_POST['export_type']) : 'all';
        if ($export_type === 'assistant') {
            $messages = array_filter($messages, function ($message) {
                return isset($message['type']) && $message['type'] === 'assistant';
            });
        }
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;

        if (empty($messages)) {
            wp_send_json_error('No messages to export');
            return;
        }

        // Get assistant info
        $assistant_name = '';
        if ($assistant_id) {
            $assistant = get_term($assistant_id, 'kb_assistant');
            if (!is_wp_error($assistant)) {
                $assistant_name = $assistant->name;
            }
        }

        $filename = sanitize_title($assistant_name) . '-chat-' . date('Y-m-d-Hi');

        switch ($format) {
            case 'pdf':
                $this->generate_pdf($messages, $assistant_name, $filename);
                break;
            case 'text':
                $this->generate_text($messages, $assistant_name, $filename);
                break;
            default:
                wp_send_json_error('Invalid export format');
        }
    }

    private function generate_pdf($messages, $assistant_name, $filename)
    {
        require_once(QKB_PLUGIN_DIR . 'vendor/autoload.php');

        $site_name = get_bloginfo('name');
        $site_url = home_url();
        $export_date = date('F j, Y \a\t g:i a');

        $html = '<style>
            .header { border-bottom: 2px solid #666; margin-bottom: 20px; padding-bottom: 10px; }
            .title { color: #2271b1; font-size: 24px; margin: 0; }
            .meta { color: #666; font-size: 12px; margin: 5px 0; }
            .message-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            .message-table td { padding: 12px; border-bottom: 1px solid #eee; vertical-align: top; }
            .timestamp { width: 160px; color: #666; font-size: 12px; }
            .user-message { background-color: #f8f9fa; border-left: 3px solid #2271b1; }
            .assistant-message { background-color: #fff; border-left: 3px solid #4CAF50; }
            .role-badge {
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 3px;
                margin-right: 8px;
                text-transform: uppercase;
            }
            .user-badge { background: #2271b1; color: white; }
            .assistant-badge { background: #4CAF50; color: white; }
        </style>';

        $html .= '<div class="header">
                    <h1 class="title">' . esc_html($site_name) . '</h1>
                    <div class="meta">Exported from: ' . esc_url($site_url) . '</div>
                    <div class="meta">assistant: ' . esc_html($assistant_name) . '</div>
                    <div class="meta">Exported on: ' . esc_html($export_date) . '</div>
                  </div>';

        $html .= '<table class="message-table">';
        foreach ($messages as $message) {
            $message_class = $message['type'] === 'user' ? 'user-message' : 'assistant-message';
            $role_badge = $message['type'] === 'user'
                ? '<span class="role-badge user-badge">You</span>'
                : '<span class="role-badge assistant-badge">' . esc_html($assistant_name) . '</span>';

            $html .= '<tr class="' . esc_attr($message_class) . '">
                <td class="timestamp">' . esc_html(date('M j, Y g:i a', $message['timestamp'] / 1000)) . '</td>
                <td>
                    ' . $role_badge . '
                    <div class="message-content">' . wp_kses_post($message['content']) . '</div>
                </td>
            </tr>';
        }
        $html .= '</table>';

        $dompdf = new \Dompdf\Dompdf();
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();

        // Add page numbers
        $canvas = $dompdf->getCanvas();
        $font = $dompdf->getFontMetrics()->getFont("Helvetica");
        $canvas->page_text(540, 800, "Page {PAGE_NUM} of {PAGE_COUNT}", $font, 10, array(0, 0, 0));

        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '.pdf"');
        echo $dompdf->output();
        exit;
    }

    private function generate_text($messages, $assistant_name, $filename)
    {
        $site_name = get_bloginfo('name');
        $site_url = home_url();
        $export_date = date('F j, Y \a\t g:i a');

        $text = "========================================\n";
        $text .= strtoupper($site_name) . "\n";
        $text .= "Exported from: " . $site_url . "\n";
        $text .= "Assistant: " . $assistant_name . "\n";
        $text .= "Exported on: " . $export_date . "\n";
        $text .= "========================================\n\n";

        foreach ($messages as $message) {
            $timestamp = date('M j, Y g:i a', $message['timestamp'] / 1000);
            $role = ($message['type'] === 'user') ? 'YOU' : strtoupper($assistant_name);
            $content = strip_tags($message['content']);

            $text .= "[$timestamp] [$role]\n";
            $text .= wordwrap($content, 100) . "\n";
            $text .= str_repeat('-', 100) . "\n";
        }

        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename="' . $filename . '.txt"');
        echo $text;
        exit;
    }

    private function get_default_assistant_id()
    {
        $default_assistant = get_term_by('slug', 'knowledge-base', 'kb_assistant');
        return $default_assistant ? $default_assistant->term_id : 0;
    }

    /**
     * Get user IP address
     *
     * @return string User IP address
     */
    private function get_user_ip()
    {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            $ip = $_SERVER['REMOTE_ADDR'] ?? '';
        }
        return sanitize_text_field($ip);
    }

    // New AJAX handler to load the chat interface markup on demand.
    public function ajax_load_chatbot()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        // Get the selected assistant for suggested prompts (same logic as main chatbot)
        $selected_assistant_id = get_option('qkb_default_chatbot_assistant', 0);
        if ($selected_assistant_id === 0) {
            $selected_assistant_id = $this->get_default_assistant_id();
        }

        $suggested_prompts = [];

        // Get the assistant term
        if ($selected_assistant_id > 0) {
            $assistant = get_term($selected_assistant_id, 'kb_assistant');
            if (!is_wp_error($assistant) && $assistant) {
                $suggested_prompts = get_term_meta($assistant->term_id, 'assistant_suggested_prompts', true);
            }
        }

        // If no suggested prompts found, try the default assistant as fallback
        if (empty($suggested_prompts) || !is_array($suggested_prompts)) {
            $default_assistant = get_term_by('slug', 'knowledge-base', 'kb_assistant');
            if ($default_assistant) {
                $suggested_prompts = get_term_meta($default_assistant->term_id, 'assistant_suggested_prompts', true);
            }
        }

        // If still no suggested prompts, use default ones
        if (empty($suggested_prompts) || !is_array($suggested_prompts)) {
            $suggested_prompts = [
                'What can you help me with?',
                'How do I get started?',
                'Tell me about your features'
            ];
        }

        // Render the chat interface and get the HTML
        ob_start();
        $html = $this->render_chat_interface();
        $html = ob_get_clean() . $html;

        // Add the suggested prompts to the response
        $html .= '<script type="text/javascript">
            if (typeof qkbChatbot !== "undefined") {
                qkbChatbot.suggested_prompts = ' . json_encode($suggested_prompts) . ';
            }
        </script>';

        // Send the HTML as a JSON response
        wp_send_json_success($html);
    }

    /**
     * Save the current assistant ID for the user
     */
    public function save_current_assistant()
    {
        // Verify nonce
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        // Get assistant ID
        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;

        if (!$assistant_id) {
            wp_send_json_error('Invalid assistant ID');
            return;
        }

        // Get current user ID
        $user_id = get_current_user_id();

        if ($user_id) {
            // Save to user meta for logged-in users
            update_user_meta($user_id, 'qi_current_assistant', $assistant_id);
        }

        wp_send_json_success();
    }
}
