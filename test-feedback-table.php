<?php
/**
 * Test script to verify the feedback table structure
 */

// Load WordPress
require_once dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';

// Get the ML Handler instance
require_once __DIR__ . '/includes/class-qkb-ml-handler.php';
$ml_handler = QKB_ML_Handler::get_instance();

// Check if table exists and its structure
echo "Checking feedback table structure...\n";

if ($ml_handler->verify_table_structure()) {
    echo "SUCCESS: Feedback table structure is valid.\n";
} else {
    echo "ERROR: Feedback table structure is invalid.\n";
    echo "Recreating feedback table...\n";
    
    if ($ml_handler->recreate_tables()) {
        echo "SUCCESS: Feedback table has been recreated successfully.\n";
    } else {
        echo "ERROR: Failed to recreate feedback table. Please check the error logs for more information.\n";
    }
}

// Test inserting a feedback record
echo "\nTesting feedback insertion...\n";

// Create a test feedback record
$message_id = 'test_' . time();
$query = 'This is a test query';
$response = 'This is a test response';
$feedback = 1; // Positive feedback
$assistant_id = 1; // Default assistant ID

// Insert the record
global $wpdb;
$table_name = $wpdb->prefix . 'qkb_ml_interactions';
$result = $wpdb->insert(
    $table_name,
    [
        'message_id' => $message_id,
        'query' => $query,
        'response' => $response,
        'feedback' => $feedback,
        'assistant_id' => $assistant_id,
        'created_at' => current_time('mysql')
    ],
    ['%s', '%s', '%s', '%d', '%d', '%s']
);

if ($result) {
    echo "SUCCESS: Test feedback record inserted successfully.\n";
    
    // Retrieve the record to verify
    $record = $wpdb->get_row(
        $wpdb->prepare(
            "SELECT * FROM $table_name WHERE message_id = %s",
            $message_id
        )
    );
    
    if ($record) {
        echo "SUCCESS: Test feedback record retrieved successfully.\n";
        echo "Record ID: {$record->id}\n";
        echo "Message ID: {$record->message_id}\n";
        echo "Query: {$record->query}\n";
        echo "Response: {$record->response}\n";
        echo "Feedback: {$record->feedback}\n";
        echo "Assistant ID: {$record->assistant_id}\n";
        echo "Created At: {$record->created_at}\n";
    } else {
        echo "ERROR: Failed to retrieve test feedback record.\n";
    }
} else {
    echo "ERROR: Failed to insert test feedback record. DB Error: " . $wpdb->last_error . "\n";
}

echo "\nTest completed.\n";
